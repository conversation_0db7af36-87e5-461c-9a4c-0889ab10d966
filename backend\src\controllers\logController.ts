import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import logCleanupService from '../services/logCleanupService';
import systemSettingsService from '../services/systemSettingsService';
import logger from '../utils/logger';

/**
 * 获取日志文件列表和统计信息
 * GET /api/admin/logs/files
 */
export const getLogFiles = async (req: Request, res: Response) => {
  try {
    const files = await logCleanupService.getLogFiles();
    const stats = await logCleanupService.getLogStats();
    const settings = await systemSettingsService.getSystemSettings();

    res.json({
      success: true,
      files,
      stats,
      settings: settings.logSettings
    });
  } catch (error) {
    logger.error('获取日志文件列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取日志文件失败'
    });
  }
};

/**
 * 下载日志文件
 * GET /api/admin/logs/download/:filename
 */
export const downloadLogFile = async (req: Request, res: Response) => {
  try {
    const filename = req.params.filename;
    
    // 验证文件名格式（防止路径遍历攻击）
    // 支持标准格式: app-YYYY-MM-DD.log
    // 支持轮转格式: app-YYYY-MM-DD.log.1, app-YYYY-MM-DD.log.2 等
    if (!/^app-\d{4}-\d{2}-\d{2}\.log(\.\d+)?$/.test(filename)) {
      return res.status(400).json({
        success: false,
        message: '无效的文件名格式'
      });
    }

    const filePath = logCleanupService.getLogFilePath(filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    logger.info(`管理员下载日志文件: ${filename}`, {
      adminId: req.user?._id,
      filename
    });

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'text/plain');
    
    // 发送文件
    res.download(filePath, filename);
  } catch (error) {
    logger.error('下载日志文件失败', error);
    res.status(500).json({
      success: false,
      message: '下载失败'
    });
  }
};

/**
 * 手动清理日志
 * POST /api/admin/logs/cleanup
 */
export const manualCleanup = async (req: Request, res: Response) => {
  try {
    const { days } = req.body;
    
    if (!days || isNaN(days) || days < 1 || days > 365) {
      return res.status(400).json({
        success: false,
        message: '清理天数必须是1-365之间的数字'
      });
    }

    logger.info(`管理员手动清理日志`, {
      adminId: req.user?._id,
      days
    });

    const result = await logCleanupService.manualCleanup(days);
    
    res.json({
      success: true,
      message: `已清理 ${result.deletedCount} 个日志文件，释放 ${formatSize(result.freedSpace)} 空间`,
      deletedCount: result.deletedCount,
      freedSpace: result.freedSpace
    });
  } catch (error) {
    logger.error('手动清理日志失败', error);
    res.status(500).json({
      success: false,
      message: '清理失败'
    });
  }
};

/**
 * 获取日志设置
 * GET /api/admin/logs/settings
 */
export const getLogSettings = async (req: Request, res: Response) => {
  try {
    const settings = await systemSettingsService.getSystemSettings();
    res.json({
      success: true,
      settings: settings.logSettings
    });
  } catch (error) {
    logger.error('获取日志设置失败', error);
    res.status(500).json({
      success: false,
      message: '获取日志设置失败'
    });
  }
};

/**
 * 更新日志设置
 * PUT /api/admin/logs/settings
 */
export const updateLogSettings = async (req: Request, res: Response) => {
  try {
    const { retentionDays, enableAutoCleanup, maxFileSize } = req.body;
    
    // 验证参数
    if (retentionDays !== undefined && (isNaN(retentionDays) || retentionDays < 1 || retentionDays > 365)) {
      return res.status(400).json({
        success: false,
        message: '日志保留天数必须是1-365之间的数字'
      });
    }

    if (enableAutoCleanup !== undefined && typeof enableAutoCleanup !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: '自动清理设置必须是布尔值'
      });
    }

    if (maxFileSize !== undefined && !/^\d+MB$/.test(maxFileSize)) {
      return res.status(400).json({
        success: false,
        message: '文件大小格式必须为数字+MB，如：5MB'
      });
    }

    const userId = req.user?._id;

    await systemSettingsService.updateSystemSettings({
      logSettings: {
        retentionDays,
        enableAutoCleanup,
        maxFileSize
      }
    }, userId?.toString());

    logger.info('日志设置更新成功', {
      adminId: userId,
      retentionDays,
      enableAutoCleanup,
      maxFileSize
    });

    res.json({
      success: true,
      message: '日志设置更新成功'
    });
  } catch (error) {
    logger.error('更新日志设置失败', error);
    res.status(500).json({
      success: false,
      message: '更新日志设置失败'
    });
  }
};

/**
 * 格式化文件大小
 */
function formatSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export default {
  getLogFiles,
  downloadLogFile,
  manualCleanup,
  getLogSettings,
  updateLogSettings
};
