/**
 * 全屏模式样式
 */

/* 全屏模式下的主容器样式 */
.chart-composite:fullscreen {
  background-color: rgba(18, 20, 24, 0.85);
  width: 100vw !important;
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  margin: 0;
  transform: translateZ(0); /* 促进GPU加速，减少全屏切换时的重绘 */
  will-change: transform; /* 提示浏览器此元素将发生变化，优化性能 */
  transition: none !important; /* 禁用任何可能的动画过渡，确保立即切换 */
}

/* 确保轻量级图表在全屏模式下正确显示 */
.chart-composite:fullscreen .chart-core-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto !important;
  transform: translateZ(0); /* 促进GPU加速 */
}

/* 图表容器在全屏模式下的样式 */
.chart-composite:fullscreen .chart-core-container > div:first-child {
  width: 100% !important;
  height: 100% !important;
  flex: 1;
  position: relative; /* 确保位置计算基于容器 */
}

/* 确保WebKit浏览器中全屏正常工作 */
.chart-composite:-webkit-full-screen {
  background-color: rgba(18, 20, 24, 0.85);
  width: 100vw !important;
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  margin: 0;
  transform: translateZ(0);
  will-change: transform;
  transition: none !important;
}

/* 确保WebKit浏览器中图表容器正确显示 */
.chart-composite:-webkit-full-screen .chart-core-container,
.chart-composite:-webkit-full-screen .chart-core-container > div:first-child {
  width: 100% !important;
  height: 100% !important;
  flex: 1;
  transform: translateZ(0);
}

/* 确保Mozilla浏览器中全屏正常工作 */
.chart-composite:-moz-full-screen {
  background-color: rgba(18, 20, 24, 0.85);
  width: 100vw !important;
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  margin: 0;
  transform: translateZ(0);
  will-change: transform;
  transition: none !important;
}

/* 确保Mozilla浏览器中图表容器正确显示 */
.chart-composite:-moz-full-screen .chart-core-container,
.chart-composite:-moz-full-screen .chart-core-container > div:first-child {
  width: 100% !important;
  height: 100% !important;
  flex: 1;
  transform: translateZ(0);
}

/* 确保MS Edge浏览器中全屏正常工作 */
.chart-composite:-ms-fullscreen {
  background-color: rgba(18, 20, 24, 0.85);
  width: 100vw !important;
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  margin: 0;
  transform: translateZ(0);
  will-change: transform;
  transition: none !important;
}

/* 确保MS Edge浏览器中图表容器正确显示 */
.chart-composite:-ms-fullscreen .chart-core-container,
.chart-composite:-ms-fullscreen .chart-core-container > div:first-child {
  width: 100% !important;
  height: 100% !important;
  flex: 1;
  transform: translateZ(0);
}

/* 移动设备响应式样式 */
@media (max-width: 640px) {
  /* 图表顶部信息栏在移动设备上的样式 */
  .chart-composite .chart-core-container {
    padding-top: 52px !important; /* 增加顶部填充以容纳可能换行的信息栏 */
  }
  
  /* 调整图表信息栏样式以适应小屏幕 */
  .chart-composite .chart-info-bar {
    flex-wrap: wrap;
    height: auto !important;
    min-height: 44px;
    padding: 4px 8px;
  }
  
  /* 缩小图标大小 */
  .chart-composite .chart-info-bar svg {
    width: 18px;
    height: 18px;
  }
  
  /* 减小按钮内边距 */
  .chart-composite .chart-info-bar button,
  .chart-composite .chart-info-bar .cursor-pointer {
    padding: 2px 6px;
  }
  
  /* 确保下拉菜单正确显示 */
  .chart-composite .chart-info-bar .relative .absolute {
    width: auto;
    max-width: 200px;
    right: 0;
  }
}

/* 特小屏幕设备（小于360px宽） */
@media (max-width: 360px) {
  .chart-composite .chart-info-bar {
    font-size: 12px;
    padding: 2px 4px;
  }
  
  .chart-composite .chart-core-container {
    padding-top: 60px !important; /* 为超小屏幕增加更多空间 */
  }
} 