import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User, { UserDocument } from '../models/User';
import dotenv from 'dotenv';
import { AuthErrorCode } from '../types/errors';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

dotenv.config();

// 分离的JWT密钥 - 不同类型token使用不同密钥提高安全性
const JWT_ACCESS_SECRET = process.env.JWT_ACCESS_SECRET || 'fallback-access-secret';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
const JWT_RESET_SECRET = process.env.JWT_RESET_SECRET || 'fallback-reset-secret';



// Token撤销机制 - 简单的内存黑名单（生产环境可用Redis）
const revokedTokens = new Set<string>();

/**
 * 撤销token
 */
export const revokeToken = (jti: string): void => {
  revokedTokens.add(jti);
  console.log(`Token已撤销: ${jti}`);
};

/**
 * 检查token是否被撤销
 */
export const isTokenRevoked = (jti: string): boolean => {
  return revokedTokens.has(jti);
};

/**
 * 清理过期的撤销token（可选，防止内存泄漏）
 */
export const cleanupRevokedTokens = (): void => {
  // 简单实现：定期清空（实际应该根据token过期时间清理）
  if (revokedTokens.size > 10000) {
    revokedTokens.clear();
    console.log('清理撤销token列表');
  }
};

// 扩展 Express 的 Request 类型，加入 user 属性
declare global {
  namespace Express {
    interface Request {
      user?: UserDocument;
    }
  }
}

/**
 * 生成访问令牌 (Access Token)
 * 30分钟有效期，与自动刷新间隔保持一致，确保无缝衔接
 */
export const generateAccessToken = (userId: string): string => {
  return jwt.sign(
    {
      userId,
      jti: uuidv4() // 添加唯一标识，支持token撤销
    },
    JWT_ACCESS_SECRET, // 使用专用密钥
    { expiresIn: '30m' }
  );
};

/**
 * 生成刷新令牌 (Refresh Token)
 */
export const generateRefreshToken = (userId: string): string => {
  return jwt.sign(
    {
      userId,
      jti: uuidv4() // 添加唯一标识，支持token撤销
    },
    JWT_REFRESH_SECRET, // 使用专用密钥
    { expiresIn: '3d' }
  );
};

/**
 * 为向后兼容保留原函数名（生成访问令牌）
 */
export const generateToken = (userId: string): string => {
  return generateAccessToken(userId);
};

/**
 * 验证 JWT token 的中间件
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 从 Authorization header 中获取 token
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌',
        code: AuthErrorCode.TOKEN_MISSING
      });
    }

    // 提取 token
    const token = authHeader.split(' ')[1];

    // 验证 token
    let decoded: { userId: string; jti?: string };

    try {
      // 使用访问token专用密钥验证
      decoded = jwt.verify(token, JWT_ACCESS_SECRET) as { userId: string; jti?: string };
    } catch (error) {
      // token验证失败
      if (error instanceof jwt.TokenExpiredError) {
        return res.status(401).json({
          success: false,
          message: '认证令牌已过期',
          code: AuthErrorCode.TOKEN_EXPIRED
        });
      } else {
        return res.status(401).json({
          success: false,
          message: '认证令牌无效',
          code: AuthErrorCode.TOKEN_INVALID
        });
      }
    }

    // 检查token是否被撤销
    if (decoded.jti && isTokenRevoked(decoded.jti)) {
      return res.status(401).json({
        success: false,
        message: '认证令牌已被撤销',
        code: AuthErrorCode.TOKEN_INVALID
      });
    }
    
    // 查找用户
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ 
        success: false, 
          message: '用户不存在或已被删除',
          code: AuthErrorCode.USER_NOT_FOUND
        });
      }
      
      // 检查用户是否被封禁
      if (user.role === 'banned') {
        return res.status(403).json({
          success: false,
          message: '您的账号已被封禁，请联系管理员',
          code: AuthErrorCode.USER_BANNED
      });
    }
    
    // 检查用户是否已验证邮箱
    if (!user.isVerified) {
      return res.status(403).json({
        success: false,
          message: '请先验证您的邮箱',
          code: AuthErrorCode.EMAIL_NOT_VERIFIED
      });
    }

    // 检查并自动更新过期的用户状态
    let statusChanged = false;
    const now = new Date();

    // 检查试用期是否过期
    if (user.role === 'trial' && new Date(user.trialEndsAt) < now) {
      user.role = 'normal';
      statusChanged = true;
    }

    // 检查订阅是否过期
    if (user.role === 'subscriber' &&
        user.subscription?.status === 'active' &&
        new Date(user.subscription.endDate) < now) {
      user.role = 'normal';
      user.subscription.status = 'expired';
      statusChanged = true;
    }

    // 如果状态发生变化，保存到数据库
    if (statusChanged) {
      try {
        await user.save();
      } catch (error) {
        console.error(`[认证中间件] 用户 ${user._id} 状态更新失败:`, error);
        // 不阻断请求，仅记录错误
      }
    }

    // 将用户信息添加到请求对象中
    req.user = user;
    next();
  } catch (error) {
    logger.error('认证过程中发生错误', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试',
      code: 'common/server-error'
    });
  }
};



/**
 * 可选认证中间件 - 如果提供了有效的 token 则加载用户信息，否则继续处理
 */
export const optionalAuthenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.split(' ')[1];
    try {
      // 验证token
      const decoded = jwt.verify(token, JWT_ACCESS_SECRET) as { userId: string; jti?: string };

      // 检查token是否被撤销
      if (decoded.jti && isTokenRevoked(decoded.jti)) {
        return next(); // 可选认证，token无效时继续处理
      }

      const user = await User.findById(decoded.userId);

      if (user) {
        req.user = user;
      }
    } catch (error) {
      // 如果令牌验证失败，继续但不设置 req.user
      // 对于可选认证，我们不需要返回错误
    }
    
    next();
  } catch (error) {
    // 如果 token 无效，继续但不设置 req.user
    next();
  }
};

/**
 * 验证刷新令牌的中间件
 */
export const verifyRefreshToken = (token: string): { userId: string; jti?: string } | null => {
  try {
    // 验证刷新token
    const decoded = jwt.verify(token, JWT_REFRESH_SECRET) as { userId: string; jti?: string };

    // 检查token是否被撤销
    if (decoded.jti && isTokenRevoked(decoded.jti)) {
      return null;
    }

    return { userId: decoded.userId, jti: decoded.jti };
  } catch (error) {
    return null;
  }
};

export default authenticate; 