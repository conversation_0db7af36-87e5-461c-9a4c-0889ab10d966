import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  getAdminDocumentById,
  createDocument,
  updateDocument,
  uploadDocumentImage,
  CreateDocumentData,
  UpdateDocumentData
} from '../../api/adminDocs';
import { useToast } from '../../components/ui/use-toast';
import { Button } from '../../components/admin/ui';
import AdminLayout from '../../components/admin/AdminLayout';
import MDEditor from '@uiw/react-md-editor';
import { Save, ArrowLeft, Image, Upload } from 'lucide-react';
import '@uiw/react-md-editor/markdown-editor.css';
import '../../styles/md-editor.css';

const DocumentEditor: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: 'guide' as 'guide' | 'faq' | 'announcement',
    language: 'zh' as 'zh' | 'en',
    isVisible: true,
    order: 0
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // 加载文档数据（编辑模式）
  useEffect(() => {
    if (isEditing && id) {
      const loadDocument = async () => {
        try {
          setIsLoading(true);
          const response = await getAdminDocumentById(id);
          if (response.success) {
            const doc = response.data;
            setFormData({
              title: doc.title,
              content: doc.content,
              category: doc.category,
              language: doc.language,
              isVisible: doc.isVisible,
              order: doc.order
            });
          }
        } catch (error) {
          toast({
            title: '错误',
            description: error instanceof Error ? error.message : '加载文档失败',
            variant: 'destructive'
          });
          navigate('/admin/documents');
        } finally {
          setIsLoading(false);
        }
      };

      loadDocument();
    }
  }, [id, isEditing, toast, navigate]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.content.trim()) {
      toast({
        title: '错误',
        description: '请填写标题和内容',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSaving(true);

      if (isEditing && id) {
        // 更新文档
        const updateData: UpdateDocumentData = {
          title: formData.title,
          content: formData.content,
          category: formData.category,
          language: formData.language,
          isVisible: formData.isVisible,
          order: formData.order
        };
        await updateDocument(id, updateData);
        toast({
          title: '成功',
          description: '文档更新成功'
        });
      } else {
        // 创建文档
        const createData: CreateDocumentData = {
          title: formData.title,
          content: formData.content,
          category: formData.category,
          language: formData.language,
          isVisible: formData.isVisible,
          order: formData.order
        };
        await createDocument(createData);
        toast({
          title: '成功',
          description: '文档创建成功'
        });
      }

      navigate('/admin/documents');
    } catch (error) {
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '保存文档失败',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理图片上传
  const handleImageUpload = async (file: File) => {
    try {
      setIsUploadingImage(true);
      const response = await uploadDocumentImage(file);

      if (response.success) {
        // 获取完整的图片URL
        const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';
        const imageUrl = response.imageUrl.startsWith('http')
          ? response.imageUrl
          : `${API_URL}${response.imageUrl}`;

        // 生成Markdown图片语法
        const imageMarkdown = `![${file.name}](${imageUrl})`;

        // 在当前光标位置插入图片
        const currentContent = formData.content;
        const newContent = currentContent + '\n\n' + imageMarkdown + '\n\n';

        handleInputChange('content', newContent);

        toast({
          title: '成功',
          description: '图片上传成功'
        });
      }
    } catch (error) {
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '图片上传失败',
        variant: 'destructive'
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        toast({
          title: '错误',
          description: '请选择图片文件',
          variant: 'destructive'
        });
        return;
      }

      // 检查文件大小（5MB限制）
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: '错误',
          description: '图片文件大小不能超过5MB',
          variant: 'destructive'
        });
        return;
      }

      handleImageUpload(file);
    }

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="bg-gray-200 h-8 w-1/3 rounded"></div>
            <div className="bg-gray-200 h-4 w-full rounded"></div>
            <div className="bg-gray-200 h-64 w-full rounded"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/admin/documents')}
              className="text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? '编辑文档' : '创建文档'}
              </h1>
              <p className="text-gray-600 mt-1">
                {isEditing ? '修改文档内容和设置' : '创建新的文档内容'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* MDEditor 自带预览功能，无需额外按钮 */}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
                
                {/* 标题 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    标题 *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入文档标题"
                    required
                  />
                </div>

                {/* 分类和语言 */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      分类 *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="guide">使用指南</option>
                      <option value="faq">常见问题</option>
                      <option value="announcement">公告</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      语言 *
                    </label>
                    <select
                      value={formData.language}
                      onChange={(e) => handleInputChange('language', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="zh">中文</option>
                      <option value="en">English</option>
                    </select>
                  </div>
                </div>

                {/* 排序和可见性 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      排序权重
                    </label>
                    <input
                      type="number"
                      value={formData.order}
                      onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">数字越小越靠前</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      可见性
                    </label>
                    <div className="flex items-center space-x-4 mt-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isVisible"
                          checked={formData.isVisible}
                          onChange={() => handleInputChange('isVisible', true)}
                          className="mr-2"
                        />
                        公开
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isVisible"
                          checked={!formData.isVisible}
                          onChange={() => handleInputChange('isVisible', false)}
                          className="mr-2"
                        />
                        隐藏
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* 内容编辑 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">文档内容</h3>
                  <div className="flex items-center space-x-2">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="image-upload"
                    />
                    <label
                      htmlFor="image-upload"
                      className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors ${
                        isUploadingImage ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      {isUploadingImage ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                          上传中...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          上传图片
                        </>
                      )}
                    </label>
                  </div>
                </div>
                <div className="border border-gray-300 rounded-md overflow-hidden">
                  <MDEditor
                    value={formData.content}
                    onChange={(value) => handleInputChange('content', value || '')}
                    height={500}
                    data-color-mode="light"
                    preview="edit"
                    hideToolbar={false}
                    visibleDragbar={false}
                    textareaProps={{
                      placeholder: '请输入 Markdown 格式的文档内容...\n\n支持功能：\n- 标题：# ## ### \n- 列表：- 或 1. \n- 链接：[文字](链接)\n- 图片：![描述](图片链接)\n- 代码：`代码` 或 ```语言\n- 表格、引用等更多功能',
                      style: {
                        fontSize: 14,
                        lineHeight: 1.6,
                        fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
                        color: '#1f2937',
                        backgroundColor: '#ffffff'
                      }
                    }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  支持 Markdown 格式，包括标题、列表、代码块、链接、图片等。可以切换编辑/预览模式。点击"上传图片"按钮可直接上传图片并插入到文档中。
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-4">

                <Button
                  type="submit"
                  disabled={isSaving}
                  variant="primary"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {isEditing ? '更新文档' : '创建文档'}
                    </>
                  )}
                </Button>
              </div>
            </div>


          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default DocumentEditor;
