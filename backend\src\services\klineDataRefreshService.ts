import binanceService from './binanceService';
import klineMemoryCache from './klineMemoryCache';
import sseEventManager from './sseEventManager';
import logger from '../utils/logger';

/**
 * K线数据刷新服务
 * 负责定时从币安API获取最新K线数据，更新缓存并通过SSE推送
 */
class KlineDataRefreshService {
  private refreshInterval: NodeJS.Timeout | null = null;
  private readonly REFRESH_INTERVAL = 10 * 1000; // 10秒刷新间隔
  private readonly CACHE_TTL = 15 * 1000; // 15秒缓存TTL
  private readonly CACHE_KEY = 'realtime_klines_BTCUSDT';
  private isRefreshing = false;

  /**
   * 启动数据刷新服务
   */
  start(): void {
    if (this.refreshInterval) {
      logger.warn('K线数据刷新服务已在运行');
      return;
    }

    logger.info('启动K线数据刷新服务', {
      refreshInterval: this.REFRESH_INTERVAL,
      cacheTTL: this.CACHE_TTL
    });

    // 立即执行一次刷新
    this.refreshKlineData();

    // 设置定时刷新
    this.refreshInterval = setInterval(() => {
      this.refreshKlineData();
    }, this.REFRESH_INTERVAL);
  }

  /**
   * 停止数据刷新服务
   */
  stop(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      logger.info('K线数据刷新服务已停止');
    }
  }

  /**
   * 刷新K线数据
   * 从币安API获取最新数据，更新缓存并推送给SSE客户端
   */
  private async refreshKlineData(): Promise<void> {
    if (this.isRefreshing) {
      logger.debug('K线数据刷新正在进行中，跳过本次刷新');
      return;
    }

    this.isRefreshing = true;

    try {
      // 从币安API获取最新5条30分钟K线数据
      const response = await binanceService.sendRequest('/api/v3/klines', {
        symbol: 'BTCUSDT',
        interval: '30m',
        limit: 5
      });

      if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
        logger.warn('币安API未返回有效的K线数据');
        return;
      }

      // 格式化K线数据为前端所需格式
      const formattedKlines = response.data.map((kline: any[]) => ({
        time: Math.floor(kline[0] / 1000), // 转换为秒级时间戳
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        fromBinance: true // 标记数据来源
      }));

      // 检查数据是否有变化
      const cachedData = klineMemoryCache.get(this.CACHE_KEY);
      const hasChanged = this.hasDataChanged(cachedData, formattedKlines);

      if (hasChanged) {
        // 更新缓存
        klineMemoryCache.set(this.CACHE_KEY, formattedKlines, this.CACHE_TTL);

        // 通过事件管理器发布K线数据更新事件
        sseEventManager.publishKlineUpdate(formattedKlines, {
          source: 'binance-api',
          refreshTime: Date.now()
        });

        // 成功刷新且有数据变化，不记录日志（减少日志噪音）
      } else {
        logger.info('K线数据无变化，跳过推送');
      }

    } catch (error) {
      logger.error('K线数据刷新失败', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 检查数据是否有变化
   * @param cachedData 缓存的数据
   * @param newData 新数据
   * @returns 是否有变化
   */
  private hasDataChanged(cachedData: any[] | null, newData: any[]): boolean {
    if (!cachedData || cachedData.length !== newData.length) {
      return true;
    }

    // 比较最后一条数据的收盘价和时间
    const lastCached = cachedData[cachedData.length - 1];
    const lastNew = newData[newData.length - 1];

    return (
      lastCached.time !== lastNew.time ||
      lastCached.close !== lastNew.close ||
      lastCached.high !== lastNew.high ||
      lastCached.low !== lastNew.low ||
      lastCached.volume !== lastNew.volume
    );
  }



  /**
   * 获取服务状态
   */
  getStatus(): {
    isRunning: boolean;
    refreshInterval: number;
    cacheTTL: number;
    cacheStats: any;
    eventStats: any;
    lastRefreshTime?: number;
  } {
    const cacheStats = klineMemoryCache.getStats();
    const eventStats = sseEventManager.getStats();

    return {
      isRunning: this.refreshInterval !== null,
      refreshInterval: this.REFRESH_INTERVAL,
      cacheTTL: this.CACHE_TTL,
      cacheStats,
      eventStats,
      lastRefreshTime: this.isRefreshing ? Date.now() : undefined
    };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stop();
    logger.info('K线数据刷新服务已销毁');
  }
}

// 导出单例实例
export default new KlineDataRefreshService();
