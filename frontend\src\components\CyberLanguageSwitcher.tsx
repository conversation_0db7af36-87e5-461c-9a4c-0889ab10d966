import React from 'react'
import { ChevronDown, Globe } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface CyberLanguageSwitcherProps {
  className?: string
  variant?: 'default' | 'compact'
}

const CyberLanguageSwitcher: React.FC<CyberLanguageSwitcherProps> = ({
  className,
  variant = 'default'
}) => {
  const { i18n } = useTranslation()

  const languages = [
    { code: 'zh', name: '中文', nativeName: '中文' },
    { code: 'en', name: 'English', nativeName: 'English' }
  ]

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={cn(
            "flex items-center gap-2 px-3 py-2 h-10 rounded-md min-w-[100px] justify-between",
            "text-cyber-text hover:text-cyber-cyan",
            "border border-cyber-border hover:border-cyber-cyan/30",
            "bg-cyber-card/30 hover:bg-cyber-card/40 backdrop-blur-sm",
            "transition-all duration-300 group font-mono text-sm",
            "outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-cyber-cyan/30",
            variant === 'compact' && "px-2 py-1 h-8 min-w-[80px]",
            className
          )}
        >
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            {variant === 'default' && (
              <span className="font-medium">
                {currentLanguage.nativeName}
              </span>
            )}
          </div>
          <ChevronDown className="h-4 w-4 flex-shrink-0" />
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        align="end"
        className={cn(
          "w-32 bg-cyber-card/95 backdrop-blur-md border-cyber-border text-cyber-text",
          "outline-none focus:outline-none"
        )}
        sideOffset={8}
      >
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="cursor-pointer hover:bg-cyber-cyan/15 hover:text-cyber-cyan focus:bg-cyber-cyan/15 focus:text-cyber-cyan transition-colors duration-200 font-mono outline-none focus:outline-none"
          >
            {language.nativeName}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default CyberLanguageSwitcher
