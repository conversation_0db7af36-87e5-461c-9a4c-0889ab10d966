/**
 * 实时折线图系列组件
 * 
 * 管理实时折线数据的渲染与更新
 * 复用主K线数据源但以折线形式展示
 */

import { useEffect, useRef, useCallback } from 'react';
import { IChartApi, ISeriesApi, LineData } from 'lightweight-charts';
import { KLineData } from '../types/chartTypes';

interface RealTimeLineSeriesProps {
  chartApi: IChartApi | null;
  isChartReady: boolean;
  candleData: KLineData[];
  lineSeries: ISeriesApi<'Line'> | null;
  isVisible: boolean;
  onUpdateLastValue?: (value: number) => void;
}

/**
 * 实时折线图系列组件
 */
const RealTimeLineSeries: React.FC<RealTimeLineSeriesProps> = ({
  chartApi,
  isChartReady,
  candleData,
  lineSeries,
  isVisible,
  onUpdateLastValue
}) => {
  // 最新价格引用
  const lastValueRef = useRef<number | null>(null);
  
  // 将K线数据转换为折线数据
  const convertToLineData = useCallback((data: KLineData[]): LineData[] => {
    return data.map(candle => ({
      time: candle.time,
      value: parseFloat(candle.close.toString())
    }));
  }, []);
  
  // 更新折线数据
  useEffect(() => {
    if (!isChartReady || !lineSeries || !isVisible || candleData.length === 0) {
      return;
    }
    
    try {
      const lineData = convertToLineData(candleData);
      lineSeries.setData(lineData);
      
      // 更新最新价格
      const lastCandle = candleData[candleData.length - 1];
      if (lastCandle) {
        const lastValue = parseFloat(lastCandle.close.toString());
        lastValueRef.current = lastValue;
        
        // 调用回调函数
        if (onUpdateLastValue) {
          onUpdateLastValue(lastValue);
        }
      }
      
      console.log('实时折线数据已更新，数据点数量:', lineData.length);
    } catch (error) {
      console.error('更新实时折线数据失败:', error);
    }
  }, [candleData, isChartReady, lineSeries, isVisible, convertToLineData, onUpdateLastValue]);
  
  // 更新折线可见性
  useEffect(() => {
    if (!lineSeries) return;
    
    try {
      lineSeries.applyOptions({ visible: isVisible });
      console.log(`实时折线可见性已更新: ${isVisible ? '显示' : '隐藏'}`);
    } catch (error) {
      console.error('更新实时折线可见性失败:', error);
    }
  }, [isVisible, lineSeries]);
  
  // 组件不渲染任何UI元素
  return null;
};

export default RealTimeLineSeries; 