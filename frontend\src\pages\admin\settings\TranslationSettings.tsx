import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import { useToast } from '../../../components/ui/use-toast';
import useAdminStore from '../../../store/useAdminStore';
import { Button, Input, Label, Card } from '../../../components/admin/ui';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';

const TranslationSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeeplKey, setShowDeeplKey] = useState(false);
  const [showGoogleKey, setShowGoogleKey] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    translationSettings: {
      deeplApiKey: '',
      googleApiKey: '',
      preferredService: 'deepl' as 'deepl' | 'google',
      enableTranslation: false
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();
      setFormData({
        translationSettings: response.settings.translationSettings || {
          deeplApiKey: '',
          googleApiKey: '',
          preferredService: 'deepl',
          enableTranslation: false
        }
      });
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取翻译设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      translationSettings: {
        ...prev.translationSettings,
        [name]: value
      }
    }));
  };

  // 处理开关变化
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    const fieldName = name.replace('translationSettings.', '');
    setFormData(prev => ({
      ...prev,
      translationSettings: {
        ...prev.translationSettings,
        [fieldName]: checked
      }
    }));
  };

  // 处理选择器变化
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    const fieldName = name.replace('translationSettings.', '');
    setFormData(prev => ({
      ...prev,
      translationSettings: {
        ...prev.translationSettings,
        [fieldName]: value
      }
    }));
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        translationSettings: formData.translationSettings
      });
      toast({
        title: '翻译设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新翻译设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  // 检查API密钥是否已配置
  const hasDeeplKey = formData.translationSettings.deeplApiKey && formData.translationSettings.deeplApiKey.length > 8;
  const hasGoogleKey = formData.translationSettings.googleApiKey && formData.translationSettings.googleApiKey.length > 8;
  const hasAnyKey = hasDeeplKey || hasGoogleKey;

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">翻译设置</h1>
          <p className="text-gray-600 mt-1">配置文档自动翻译功能的API密钥和首选服务</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
              {/* 翻译功能开关 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>翻译功能</span>
                  {formData.translationSettings.enableTranslation && hasAnyKey && (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  )}
                  {formData.translationSettings.enableTranslation && !hasAnyKey && (
                    <AlertCircle className="h-5 w-5 text-yellow-600" />
                  )}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <label htmlFor="enableTranslation" className="text-sm font-medium text-gray-700">
                        启用翻译功能
                      </label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          id="enableTranslation"
                          name="translationSettings.enableTranslation"
                          checked={formData.translationSettings.enableTranslation}
                          onChange={handleSwitchChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-700">
                      启用或禁用文档自动翻译功能
                    </p>
                    {formData.translationSettings.enableTranslation && !hasAnyKey && (
                      <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div className="flex items-center space-x-2">
                          <AlertCircle className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm text-yellow-800">
                            翻译功能已启用，但未配置API密钥。请至少配置一个翻译服务的API密钥。
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* DeepL API 设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>DeepL API</span>
                  {hasDeeplKey && <CheckCircle className="h-5 w-5 text-green-600" />}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <Label htmlFor="deeplApiKey">API 密钥</Label>
                    <div className="relative mt-1">
                      <Input
                        id="deeplApiKey"
                        name="deeplApiKey"
                        type={showDeeplKey ? 'text' : 'password'}
                        value={formData.translationSettings.deeplApiKey}
                        onChange={handleChange}
                        placeholder="输入 DeepL API 密钥"
                        className="pr-10"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowDeeplKey(!showDeeplKey)}
                      >
                        {showDeeplKey ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      DeepL 提供高质量的翻译服务，推荐使用。在 <a href="https://www.deepl.com/pro-api" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">DeepL Pro API</a> 获取您的API密钥
                    </p>
                  </div>
                </div>
              </div>

              {/* Google Translate API 设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>Google Translate API</span>
                  {hasGoogleKey && <CheckCircle className="h-5 w-5 text-green-600" />}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <Label htmlFor="googleApiKey">API 密钥</Label>
                    <div className="relative mt-1">
                      <Input
                        id="googleApiKey"
                        name="googleApiKey"
                        type={showGoogleKey ? 'text' : 'password'}
                        value={formData.translationSettings.googleApiKey}
                        onChange={handleChange}
                        placeholder="输入 Google Translate API 密钥"
                        className="pr-10"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowGoogleKey(!showGoogleKey)}
                      >
                        {showGoogleKey ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <p className="text-xs text-gray-700 mt-1">
                      Google 翻译服务，作为备用翻译选项。在 <a href="https://cloud.google.com/translate/docs/setup" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a> 获取您的API密钥
                    </p>
                  </div>
                </div>
              </div>

              {/* 首选翻译服务 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 text-gray-900">首选翻译服务</h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <Label htmlFor="preferredService">首选服务</Label>
                    <select
                      id="preferredService"
                      name="translationSettings.preferredService"
                      value={formData.translationSettings.preferredService}
                      onChange={handleSelectChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mt-1"
                    >
                      <option value="deepl">DeepL (推荐)</option>
                      <option value="google">Google Translate</option>
                    </select>
                    <p className="text-xs text-gray-700 mt-1">
                      选择优先使用的翻译服务，如果首选服务失败会自动切换到备用服务
                    </p>
                  </div>
                </div>
              </div>

              {/* 保存按钮 */}
              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  disabled={isSaving}
                  loading={isSaving}
                >
                  {isSaving ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default TranslationSettings;
