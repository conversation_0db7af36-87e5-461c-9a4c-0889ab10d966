/* MDEditor 自定义样式 */

/* 编辑器容器 */
.w-md-editor {
  background-color: #ffffff !important;
}

/* 工具栏样式 */
.w-md-editor-toolbar {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 8px 12px !important;
}

.w-md-editor-toolbar-child > ul {
  margin: 0 !important;
}

.w-md-editor-toolbar button {
  color: #64748b !important;
  border: none !important;
  background: transparent !important;
  padding: 6px 8px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.w-md-editor-toolbar button:hover {
  background-color: #e2e8f0 !important;
  color: #334155 !important;
}

.w-md-editor-toolbar button.active {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* 编辑区域 */
.w-md-editor .w-md-editor-text-textarea,
.w-md-editor .w-md-editor-text-input,
.w-md-editor .w-md-editor-text,
.w-md-editor-text-textarea,
.w-md-editor-text-input,
.w-md-editor-text {
  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #1f2937 !important;
  background-color: #ffffff !important;
  border: none !important;
  padding: 16px !important;
}

/* 确保 textarea 的文字颜色正确 */
.w-md-editor .w-md-editor-text textarea,
.w-md-editor-text textarea {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

.w-md-editor-text-textarea:focus,
.w-md-editor-text-input:focus {
  outline: none !important;
  box-shadow: none !important;
  color: #1f2937 !important;
}

/* 额外的文字颜色确保 */
.w-md-editor .w-md-editor-text-textarea::placeholder,
.w-md-editor .w-md-editor-text-input::placeholder {
  color: #9ca3af !important;
}

/* 强制设置编辑器内部所有文本颜色 */
.w-md-editor .w-md-editor-text * {
  color: #1f2937 !important;
}

/* 最强优先级的文字颜色设置 */
.w-md-editor[data-color-mode="light"] .w-md-editor-text-textarea,
.w-md-editor[data-color-mode="light"] .w-md-editor-text-input,
.w-md-editor[data-color-mode="light"] .w-md-editor-text textarea {
  color: #1f2937 !important;
  background-color: #ffffff !important;
  caret-color: #1f2937 !important;
}

/* 确保在所有状态下文字都可见 */
.w-md-editor .w-md-editor-text-textarea,
.w-md-editor .w-md-editor-text-input {
  -webkit-text-fill-color: #1f2937 !important;
}

/* 预览区域 */
.w-md-editor-preview {
  background-color: #ffffff !important;
  padding: 16px !important;
  color: #1f2937 !important;
}

/* 分割线 */
.w-md-editor-split {
  border-left: 1px solid #e2e8f0 !important;
}

/* 模式切换按钮 */
.w-md-editor-toolbar-mode {
  border-left: 1px solid #e2e8f0 !important;
  margin-left: 8px !important;
  padding-left: 8px !important;
}

/* 全屏模式 */
.w-md-editor.w-md-editor-fullscreen {
  z-index: 1000 !important;
}

/* 预览内容样式 */
.w-md-editor-preview h1,
.w-md-editor-preview h2,
.w-md-editor-preview h3,
.w-md-editor-preview h4,
.w-md-editor-preview h5,
.w-md-editor-preview h6 {
  color: #1f2937 !important;
  font-weight: 600 !important;
  margin-top: 1.5em !important;
  margin-bottom: 0.5em !important;
}

.w-md-editor-preview h1 {
  font-size: 2em !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding-bottom: 0.3em !important;
}

.w-md-editor-preview h2 {
  font-size: 1.5em !important;
}

.w-md-editor-preview h3 {
  font-size: 1.25em !important;
}

.w-md-editor-preview p {
  margin-bottom: 1em !important;
  line-height: 1.6 !important;
  color: #374151 !important;
}

.w-md-editor-preview code {
  background-color: #f1f5f9 !important;
  color: #e11d48 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-size: 0.875em !important;
}

.w-md-editor-preview pre {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  padding: 1em !important;
  border-radius: 6px !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.w-md-editor-preview pre code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
}

.w-md-editor-preview blockquote {
  border-left: 4px solid #3b82f6 !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  background-color: #f8fafc !important;
  color: #64748b !important;
  font-style: italic !important;
}

.w-md-editor-preview table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 1em 0 !important;
}

.w-md-editor-preview th,
.w-md-editor-preview td {
  border: 1px solid #e2e8f0 !important;
  padding: 8px 12px !important;
  text-align: left !important;
}

.w-md-editor-preview th {
  background-color: #f8fafc !important;
  font-weight: 600 !important;
}

.w-md-editor-preview ul,
.w-md-editor-preview ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.w-md-editor-preview li {
  margin-bottom: 0.5em !important;
}

.w-md-editor-preview a {
  color: #3b82f6 !important;
  text-decoration: underline !important;
}

.w-md-editor-preview a:hover {
  color: #2563eb !important;
}

.w-md-editor-preview img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 6px !important;
  margin: 1em 0 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-md-editor-text-textarea,
  .w-md-editor-text-input,
  .w-md-editor-text,
  .w-md-editor-preview {
    padding: 12px !important;
  }
  
  .w-md-editor-toolbar {
    padding: 6px 8px !important;
  }
  
  .w-md-editor-toolbar button {
    padding: 4px 6px !important;
  }
}
