import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { BarChart2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import configService from '../services/configService';

export const Logo: React.FC = () => {
  const { t } = useTranslation();
  const [siteName, setSiteName] = useState(t('app.title'));

  useEffect(() => {
    const loadSiteName = async () => {
      try {
        const name = await configService.getSiteName();
        setSiteName(name);
      } catch (error) {
        console.error('Failed to load site name:', error);
      }
    };
    loadSiteName();
  }, []);

  return (
    <Link to="/" className="flex items-center space-x-2">
      <BarChart2 className="h-8 w-8 text-primary" />
      <span className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
        {siteName}
      </span>
    </Link>
  );
};