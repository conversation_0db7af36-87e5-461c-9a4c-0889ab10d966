/**
 * 时区管理器
 * 
 * 负责管理图表时区设置，提供时区转换和格式化功能
 */

// 时区选项定义
export interface TimezoneOption {
  id: string;        // 时区ID
  label: string;     // 显示名称
  offset: number;    // UTC偏移量（小时）
}

// 本地存储的键名
const TIMEZONE_STORAGE_KEY = 'chart_timezone_preference';

// 常用时区列表
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  { id: 'UTC', label: 'UTC (Coordinated Universal Time)', offset: 0 },
  { id: 'UTC-10', label: 'UTC-10 (Honolulu)', offset: -10 },
  { id: 'UTC-8', label: 'UTC-8 (Juneau)', offset: -8 },
  { id: 'UTC-7-Phoenix', label: 'UTC-7 (Phoenix)', offset: -7 },
  { id: 'UTC-7-LA', label: 'UTC-7 (Los Angeles)', offset: -7 },
  { id: 'UTC-7-Vancouver', label: 'UTC-7 (Vancouver)', offset: -7 },
  { id: 'UTC-6-Denver', label: 'UTC-6 (Denver)', offset: -6 },
  { id: 'UTC-6-Mexico', label: 'UTC-6 (Mexico City)', offset: -6 },
  { id: 'UTC-5-Bogota', label: 'UTC-5 (Bogota)', offset: -5 },
  { id: 'UTC-5-Lima', label: 'UTC-5 (Lima)', offset: -5 },
  { id: 'UTC-5-Chicago', label: 'UTC-5 (Chicago)', offset: -5 },
  { id: 'UTC-4-Toronto', label: 'UTC-4 (Toronto)', offset: -4 },
  { id: 'UTC-4-NY', label: 'UTC-4 (New York)', offset: -4 },
  { id: 'UTC-4-Santiago', label: 'UTC-4 (Santiago)', offset: -4 },
  { id: 'UTC-3-SaoPaulo', label: 'UTC-3 (São Paulo)', offset: -3 },
  { id: 'UTC-Reykjavik', label: 'UTC (Reykjavik)', offset: 0 },
  { id: 'UTC+1-Lagos', label: 'UTC+1 (Lagos)', offset: 1 },
  { id: 'UTC+1-Tunis', label: 'UTC+1 (Tunis)', offset: 1 },
  { id: 'UTC+2-Amsterdam', label: 'UTC+2 (Amsterdam)', offset: 2 },
  { id: 'UTC+2-Oslo', label: 'UTC+2 (Oslo)', offset: 2 },
  { id: 'UTC+2-Paris', label: 'UTC+2 (Paris)', offset: 2 },
  { id: 'UTC+2-Berlin', label: 'UTC+2 (Berlin)', offset: 2 },
  { id: 'UTC+2-Belgrade', label: 'UTC+2 (Belgrade)', offset: 2 },
  { id: 'UTC+2-Budapest', label: 'UTC+2 (Budapest)', offset: 2 },
  { id: 'UTC+2-Prague', label: 'UTC+2 (Prague)', offset: 2 },
  { id: 'UTC+2-Brussels', label: 'UTC+2 (Brussels)', offset: 2 },
  { id: 'UTC+2-Copenhagen', label: 'UTC+2 (Copenhagen)', offset: 2 },
  { id: 'UTC+2-Warsaw', label: 'UTC+2 (Warsaw)', offset: 2 },
  { id: 'UTC+2-Luxembourg', label: 'UTC+2 (Luxembourg)', offset: 2 },
  { id: 'UTC+2-Rome', label: 'UTC+2 (Rome)', offset: 2 },
  { id: 'UTC+2-Madrid', label: 'UTC+2 (Madrid)', offset: 2 },
  { id: 'UTC+2-Malta', label: 'UTC+2 (Malta)', offset: 2 },
  { id: 'UTC+2-Zurich', label: 'UTC+2 (Zurich)', offset: 2 },
  { id: 'UTC+2-Vienna', label: 'UTC+2 (Vienna)', offset: 2 },
  { id: 'UTC+2-Johannesburg', label: 'UTC+2 (Johannesburg)', offset: 2 },
  { id: 'UTC+3-Bahrain', label: 'UTC+3 (Bahrain)', offset: 3 },
  { id: 'UTC+3-Bucharest', label: 'UTC+3 (Bucharest)', offset: 3 },
  { id: 'UTC+3-Helsinki', label: 'UTC+3 (Helsinki)', offset: 3 },
  { id: 'UTC+3-Qatar', label: 'UTC+3 (Qatar)', offset: 3 },
  { id: 'UTC+3-Cairo', label: 'UTC+3 (Cairo)', offset: 3 },
  { id: 'UTC+3-Kuwait', label: 'UTC+3 (Kuwait)', offset: 3 },
  { id: 'UTC+3-Riga', label: 'UTC+3 (Riga)', offset: 3 },
  { id: 'UTC+3-Riyadh', label: 'UTC+3 (Riyadh)', offset: 3 },
  { id: 'UTC+3-Moscow', label: 'UTC+3 (Moscow)', offset: 3 },
  { id: 'UTC+3-Nairobi', label: 'UTC+3 (Nairobi)', offset: 3 },
  { id: 'UTC+3-Nicosia', label: 'UTC+3 (Nicosia)', offset: 3 },
  { id: 'UTC+3-Tallinn', label: 'UTC+3 (Tallinn)', offset: 3 },
  { id: 'UTC+3-Vilnius', label: 'UTC+3 (Vilnius)', offset: 3 },
  { id: 'UTC+3-Athens', label: 'UTC+3 (Athens)', offset: 3 },
  { id: 'UTC+3-Jerusalem', label: 'UTC+3 (Jerusalem)', offset: 3 },
  { id: 'UTC+4-Dubai', label: 'UTC+4 (Dubai)', offset: 4 },
  { id: 'UTC+4-Muscat', label: 'UTC+4 (Muscat)', offset: 4 },
  { id: 'UTC+5-Ashgabat', label: 'UTC+5 (Ashgabat)', offset: 5 },
  { id: 'UTC+5-Karachi', label: 'UTC+5 (Karachi)', offset: 5 },
  { id: 'UTC+6-Almaty', label: 'UTC+6 (Almaty)', offset: 6 },
  { id: 'UTC+6-Dhaka', label: 'UTC+6 (Dhaka)', offset: 6 },
  { id: 'UTC+7-HoChiMinh', label: 'UTC+7 (Ho Chi Minh)', offset: 7 },
  { id: 'UTC+7-Bangkok', label: 'UTC+7 (Bangkok)', offset: 7 },
  { id: 'UTC+7-Jakarta', label: 'UTC+7 (Jakarta)', offset: 7 },
  { id: 'UTC+8-Manila', label: 'UTC+8 (Manila)', offset: 8 },
  { id: 'UTC+8-Perth', label: 'UTC+8 (Perth)', offset: 8 },
  { id: 'UTC+8-Shanghai', label: 'UTC+8 (Shanghai)', offset: 8 },
  { id: 'UTC+8-Taipei', label: 'UTC+8 (Taipei)', offset: 8 },
  { id: 'UTC+8-Singapore', label: 'UTC+8 (Singapore)', offset: 8 },
  { id: 'UTC+8-HongKong', label: 'UTC+8 (Hong Kong)', offset: 8 },
  { id: 'UTC+8-Beijing', label: 'UTC+8 (Beijing)', offset: 8 },
  { id: 'UTC+9-Tokyo', label: 'UTC+9 (Tokyo)', offset: 9 },
  { id: 'UTC+9-Seoul', label: 'UTC+9 (Seoul)', offset: 9 },
  { id: 'UTC+10-Brisbane', label: 'UTC+10 (Brisbane)', offset: 10 },
  { id: 'UTC+10-Sydney', label: 'UTC+10 (Sydney)', offset: 10 },
  { id: 'UTC+11', label: 'UTC+11 (Norfolk Island)', offset: 11 },
  { id: 'UTC+12', label: 'UTC+12 (New Zealand)', offset: 12 },
  { id: 'UTC+13', label: 'UTC+13 (Tokelau)', offset: 13 },
];

// 单例类，管理图表时区设置
export class TimezoneManager {
  private static instance: TimezoneManager;
  private currentTimezone: TimezoneOption;
  private listeners: Array<(timezone: TimezoneOption) => void> = [];

  private constructor() {
    // 从本地存储加载时区设置（如果有）
    this.currentTimezone = this.loadTimezoneFromStorage() ||
      TIMEZONE_OPTIONS.find(tz => tz.id === 'UTC+8-Beijing') ||
      TIMEZONE_OPTIONS.find(tz => tz.offset === 8) ||
      TIMEZONE_OPTIONS[0]; // 最终回退到UTC
  }

  /**
   * 从本地存储加载时区设置
   * @returns 存储的时区设置，如果没有则返回null
   */
  private loadTimezoneFromStorage(): TimezoneOption | null {
    try {
      const savedTimezoneId = localStorage.getItem(TIMEZONE_STORAGE_KEY);
      if (savedTimezoneId) {
        const savedTimezone = TIMEZONE_OPTIONS.find(tz => tz.id === savedTimezoneId);
        if (savedTimezone) {
          console.log(`从本地存储加载时区设置: ${savedTimezone.label}`);
          return savedTimezone;
        }
      }
    } catch (error) {
      console.warn('读取本地存储的时区设置失败:', error);
    }
    return null;
  }

  /**
   * 将时区设置保存到本地存储
   * @param timezoneId 要保存的时区ID
   */
  private saveTimezoneToStorage(timezoneId: string): void {
    try {
      localStorage.setItem(TIMEZONE_STORAGE_KEY, timezoneId);
      console.log(`已保存时区设置: ${timezoneId}`);
    } catch (error) {
      console.warn('保存时区设置到本地存储失败:', error);
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TimezoneManager {
    if (!TimezoneManager.instance) {
      TimezoneManager.instance = new TimezoneManager();
    }
    return TimezoneManager.instance;
  }

  /**
   * 获取当前时区设置
   */
  public getCurrentTimezone(): TimezoneOption {
    return this.currentTimezone;
  }

  /**
   * 设置时区
   * @param timezoneId 要设置的时区ID
   */
  public setTimezone(timezoneId: string): void {
    const timezone = TIMEZONE_OPTIONS.find(tz => tz.id === timezoneId);
    if (timezone && timezone.id !== this.currentTimezone.id) {
      this.currentTimezone = timezone;
      // 保存到本地存储
      this.saveTimezoneToStorage(timezone.id);
      // 通知所有监听器
      this.notifyListeners();
    }
  }

  /**
   * 添加时区变更监听器
   * @param listener 监听回调函数
   */
  public addListener(listener: (timezone: TimezoneOption) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除时区变更监听器
   * @param listener 要移除的监听器
   */
  public removeListener(listener: (timezone: TimezoneOption) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  /**
   * 通知所有监听器时区已变更
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentTimezone));
  }

  /**
   * 将UTC时间戳转换为当前时区的时间
   * @param timestamp UTC时间戳（秒）
   * @returns 当前时区的日期对象
   */
  public convertToCurrentTimezone(timestamp: number): Date {
    const date = new Date(timestamp * 1000);
    
    // 获取UTC时间的偏移量（分钟）
    const utcOffset = date.getTimezoneOffset();
    
    // 计算当前时区和目标时区之间的偏移差（毫秒）
    const offsetDiff = (utcOffset + this.currentTimezone.offset * 60) * 60 * 1000;
    
    // 返回调整后的日期
    return new Date(date.getTime() + offsetDiff);
  }

  /**
   * 格式化时间戳为当前时区的时间字符串
   * @param timestamp UTC时间戳（秒）
   * @param format 输出格式（默认显示日期和时间）
   * @returns 格式化的时间字符串
   */
  public formatTime(timestamp: number, format: 'full' | 'time' | 'date' | 'datetime' = 'full'): string {
    const date = this.convertToCurrentTimezone(timestamp);
    
    if (format === 'time') {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } else if (format === 'date') {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    } else if (format === 'datetime') {
      // 自定义格式: yyyy-MM-dd HH:mm
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } else {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    }
  }

  /**
   * 获取当前时区的当前时间
   * @returns 格式化的时间字符串
   */
  public getCurrentTime(): string {
    const now = new Date();
    const utcMillis = now.getTime() + (now.getTimezoneOffset() * 60 * 1000);
    const targetMillis = utcMillis + (this.currentTimezone.offset * 60 * 60 * 1000);
    const targetDate = new Date(targetMillis);
    
    return targetDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }
}

// 导出默认单例实例
export default TimezoneManager.getInstance(); 