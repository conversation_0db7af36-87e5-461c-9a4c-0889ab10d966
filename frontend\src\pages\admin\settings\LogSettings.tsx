import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import { useToast } from '../../../components/ui/use-toast';
import { Button, Input, Label, Card } from '../../../components/admin/ui';
import { adminApiInstance } from '../../../api/admin';
import { Loader2, Save, AlertCircle } from 'lucide-react';

interface LogSettings {
  retentionDays: number;
  enableAutoCleanup: boolean;
  maxFileSize: string;
}

const LogSettings: React.FC = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState<LogSettings>({
    retentionDays: 15,
    enableAutoCleanup: true,
    maxFileSize: '5MB'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // 加载日志设置
  useEffect(() => {
    loadLogSettings();
  }, []);

  const loadLogSettings = async () => {
    try {
      setLoading(true);
      const response = await adminApiInstance.get('/admin/logs/settings');
      
      if (response.data.success) {
        setSettings(response.data.settings);
      }
    } catch (error: any) {
      console.error('加载日志设置失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载日志设置',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
              name === 'retentionDays' ? parseInt(value) || 1 : 
              value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // 验证输入
      if (settings.retentionDays < 1 || settings.retentionDays > 365) {
        toast({
          title: '参数错误',
          description: '日志保留天数必须在1-365天之间',
          variant: 'destructive'
        });
        return;
      }

      const response = await adminApiInstance.put('/admin/logs/settings', settings);
      
      if (response.data.success) {
        toast({
          title: '保存成功',
          description: '日志设置已更新',
          variant: 'default'
        });
      } else {
        throw new Error(response.data.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存日志设置失败:', error);
      toast({
        title: '保存失败',
        description: error.response?.data?.message || '保存日志设置失败',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">日志设置</h1>
          <p className="text-gray-600 mt-2">配置系统日志的保留策略和自动清理规则</p>
        </div>

        <Card className="p-6">
          <div className="space-y-6">
            <div>
              <Label htmlFor="retentionDays" className="text-base font-medium">
                日志保留天数
              </Label>
              <Input
                id="retentionDays"
                name="retentionDays"
                type="number"
                min="1"
                max="365"
                value={settings.retentionDays}
                onChange={handleInputChange}
                className="mt-2"
              />
              <p className="text-sm text-gray-500 mt-1">
                系统将自动删除 {settings.retentionDays} 天前的日志文件（1-365天）
              </p>
            </div>

            <div>
              <Label htmlFor="maxFileSize" className="text-base font-medium">
                单个日志文件最大大小
              </Label>
              <Input
                id="maxFileSize"
                name="maxFileSize"
                type="text"
                value={settings.maxFileSize}
                onChange={handleInputChange}
                placeholder="5MB"
                className="mt-2"
              />
              <p className="text-sm text-gray-500 mt-1">
                当日志文件达到此大小时会自动轮转（格式：数字+MB，如：5MB）
              </p>
            </div>

            <div className="flex items-start space-x-3">
              <input
                id="enableAutoCleanup"
                name="enableAutoCleanup"
                type="checkbox"
                checked={settings.enableAutoCleanup}
                onChange={handleInputChange}
                className="mt-1"
              />
              <div>
                <Label htmlFor="enableAutoCleanup" className="text-base font-medium cursor-pointer">
                  启用自动清理
                </Label>
                <p className="text-sm text-gray-500 mt-1">
                  每天凌晨3:10自动清理过期的日志文件
                </p>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800">自动清理说明</h4>
                  <ul className="text-sm text-blue-700 mt-1 space-y-1">
                    <li>• 自动清理任务每天凌晨3:10执行</li>
                    <li>• 只会删除超过保留天数的日志文件</li>
                    <li>• 当天的日志文件不会被删除</li>
                    <li>• 可以随时通过日志管理页面手动清理</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                onClick={loadLogSettings}
                disabled={saving}
              >
                重置
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2"
              >
                {saving ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{saving ? '保存中...' : '保存设置'}</span>
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default LogSettings;
