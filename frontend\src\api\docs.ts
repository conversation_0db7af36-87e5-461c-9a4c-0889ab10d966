import api from './config';

export interface Document {
  _id: string;
  title: string;
  content: string;
  category: 'guide' | 'faq' | 'announcement';
  language: 'zh' | 'en';
  isVisible: boolean;
  order: number;
  originalId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentListResponse {
  success: boolean;
  data: {
    documents: Document[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface DocumentDetailResponse {
  success: boolean;
  data: Document;
}

/**
 * 获取文档列表
 */
export const getDocuments = async (params?: {
  category?: string;
  language?: string;
  page?: number;
  limit?: number;
}): Promise<DocumentListResponse> => {
  const response = await api.get('/docs', { params });
  return response.data;
};

/**
 * 获取文档详情
 */
export const getDocumentById = async (id: string): Promise<DocumentDetailResponse> => {
  const response = await api.get(`/docs/${id}`);
  return response.data;
};

export default {
  getDocuments,
  getDocumentById
};
