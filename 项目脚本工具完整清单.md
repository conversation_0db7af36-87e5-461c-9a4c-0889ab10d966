# 项目脚本工具完整清单

经过详细检查，以下是当前项目中存在的所有脚本工具及其作用：

## 📦 **根目录脚本**

### **1. package.json**
- **位置**: `./package.json`
- **作用**: 根目录依赖管理，主要包含定时任务相关依赖
- **脚本**: 无特定脚本命令

## 🖥️ **前端脚本 (Frontend)**

### **2. frontend/package.json**
- **位置**: `frontend/package.json`
- **脚本命令**:
  - `npm start` - 启动开发服务器 (react-scripts start)
  - `npm run build` - 构建生产版本 (react-scripts build)
  - `npm test` - 运行测试 (react-scripts test)
  - `npm run eject` - 弹出Create React App配置

### **3. 前端工具文件**
- **reportWebVitals.ts**: Web性能监控工具
- **index.tsx**: React应用入口文件
- **App.tsx**: 主应用组件
- **i18n/index.ts**: 国际化配置工具

## ⚙️ **后端脚本 (Backend)**

### **4. backend/package.json**
- **位置**: `backend/package.json`
- **脚本命令**:
  - `npm start` - 启动生产服务器 (`node dist/index.js`)
  - `npm run dev` - 启动开发服务器 (nodemon + ts-node)
  - `npm run build` - 编译TypeScript (`tsc`)
  - `npm test` - 测试命令（当前未实现）

### **5. 币安API连接测试脚本**
- **位置**: `backend/ce.js`
- **作用**: 
  - 测试不同币安API端点的连接性
  - 支持HTTP、HTTPS、SOCKS代理测试
  - 自动尝试多种代理配置
  - 诊断网络连接问题
- **使用**: `node ce.js`

## 🛠️ **后端工具脚本 (backend/scripts/)**

### **6. 管理员账户创建脚本**
- **位置**: `backend/scripts/createAdmin.js`
- **作用**: 
  - 创建系统管理员账户
  - 预设邮箱: `<EMAIL>`
  - 预设密码: `123456`
  - 自动设置管理员权限
- **使用**: `node backend/scripts/createAdmin.js`

### **7. 预测数据清理脚本**
- **位置**: `backend/scripts/cleanPredictionData.js`
- **作用**: 
  - 清理节气预测折线数据
  - 支持多种过滤条件
- **使用**: 
  ```bash
  node cleanPredictionData.js --all           # 删除所有数据
  node cleanPredictionData.js --symbol=BTCUSDT # 按交易对删除
  node cleanPredictionData.js --before=2024-05-01 # 删除指定日期前数据
  node cleanPredictionData.js --after=2024-01-01  # 删除指定日期后数据
  node cleanPredictionData.js --inactive      # 只删除非活跃数据
  ```

### **8. 节气配置清理脚本**
- **位置**: `backend/scripts/cleanSolarTermConfigs.js`
- **作用**: 清理节气预测转换比例配置
- **使用**:
  ```bash
  node cleanSolarTermConfigs.js show                    # 显示所有配置
  node cleanSolarTermConfigs.js clean-expired [月数]    # 清理过期配置
  node cleanSolarTermConfigs.js clean-symbol BTCUSDT   # 清理指定交易对
  node cleanSolarTermConfigs.js clean-cycle 周期键      # 清理指定周期
  node cleanSolarTermConfigs.js clean-year 2025        # 清理指定年份
  node cleanSolarTermConfigs.js clean-all --force      # 清理所有配置
  ```

### **9. 快速清理脚本**
- **位置**: `backend/scripts/quickClean.js`
- **作用**: 
  - 简化版清理工具
  - 快速清理节气预测转换比例配置
  - 显示当前记录并一键清理
- **使用**: `node backend/scripts/quickClean.js`

### **10. 数据库连接测试脚本**
- **位置**: `backend/scripts/testDBConnection.js`
- **作用**: 
  - 测试MongoDB数据库连接
  - 列出所有数据库集合
  - 查找节气相关集合
  - 显示集合文档数量和示例数据
- **使用**: `node backend/scripts/testDBConnection.js`

### **11. 示例文档创建脚本**
- **位置**: `backend/scripts/createSampleDocs.js`
- **作用**: 
  - 创建示例文档数据
  - 包含中英文快速开始指南
  - 包含FAQ和公告示例
  - 支持多语言和分类
- **使用**: `node backend/scripts/createSampleDocs.js`

### **12. 跨周期检查测试脚本**
- **位置**: `backend/scripts/testCrossCycleCheck.js`
- **作用**: 
  - 测试节气周期完整性检查逻辑
  - 验证预测时间点识别
  - 模拟跨周期场景
  - 调试节气日前后的预测逻辑
- **使用**: `node backend/scripts/testCrossCycleCheck.js`

### **13. 脚本说明文档**
- **位置**: `backend/scripts/README.md`
- **作用**: 
  - 详细说明各个脚本的使用方法
  - 提供命令行参数说明
  - 包含使用示例和注意事项

## 🗄️ **后端数据脚本 (backend/src/scripts/)**

### **14. K线数据清理脚本**
- **位置**: `backend/src/scripts/clearKlines.js`
- **作用**: 
  - 清空所有历史30分钟K线数据
  - 删除klines集合中interval为'30m'的记录
- **使用**: `node src/scripts/clearKlines.js`

### **15. 预测数据清理脚本**
- **位置**: `backend/src/scripts/clearPredictions.js`
- **作用**: 
  - 清空所有历史预测数据
  - 删除predictions集合中的所有记录
- **使用**: `node src/scripts/clearPredictions.js`

### **16. 30分钟K线删除脚本**
- **位置**: `backend/src/scripts/deleteThirtyMinKlines.js`
- **作用**: 
  - 删除指定交易对的30分钟K线数据
  - 支持删除所有交易对或特定交易对
- **使用**: 
  ```bash
  node src/scripts/deleteThirtyMinKlines.js BTCUSDT # 删除BTCUSDT数据
  node src/scripts/deleteThirtyMinKlines.js         # 删除所有交易对数据
  ```

### **17. 时间范围K线删除脚本**
- **位置**: `backend/src/scripts/deleteThirtyMinKlinesRange.js`
- **作用**: 
  - 删除指定时间范围内的30分钟K线数据
  - 支持自定义开始和结束日期
- **使用**: 
  ```bash
  node src/scripts/deleteThirtyMinKlinesRange.js BTCUSDT 2023-01-01 2023-01-31
  node src/scripts/deleteThirtyMinKlinesRange.js BTCUSDT 2023-01-01
  node src/scripts/deleteThirtyMinKlinesRange.js
  ```

## 🔧 **后端工具类 (backend/src/utils/)**


### **19. 定时任务调度器**
- **位置**: `backend/src/utils/scheduler.ts`
- **作用**: 
  - 管理所有系统定时任务
  - 预测生成、数据检查、用户通知等
  - 自动启动和管理cron任务

### **20. 节气工具函数**
- **位置**: `backend/src/utils/solarTermUtils.ts`
- **作用**: 
  - 节气日期计算工具
  - 北京时间转换
  - 节气日判断和获取

### **21. 邮件发送工具**
- **位置**: `backend/src/utils/sendVerificationEmail.ts`
- **作用**: 
  - 发送验证邮件
  - 邮件模板处理

### **22. 节气数据**
- **位置**: `backend/src/utils/solarTermData.ts`
- **作用**: 
  - 存储节气日期数据
  - 提供节气查询数据源

## 📋 **配置文件**

### **23. 环境变量示例**
- **位置**: `backend/.env.example`
- **作用**: 
  - 提供环境变量配置模板
  - 包含数据库、邮件、支付等配置示例

### **24. TypeScript配置**
- **位置**: `backend/tsconfig.json`, `frontend/tsconfig.json`
- **作用**: TypeScript编译配置

### **25. Git忽略配置**
- **位置**: `.gitignore`
- **作用**: 定义Git版本控制忽略的文件和目录

## 📚 **文档文件**

### **26. 项目说明文档**
- **位置**: `README.md`
- **作用**: 项目整体介绍、安装和使用说明

### **27. 前端说明文档**
- **位置**: `frontend/README.md`
- **作用**: Create React App标准说明

### **28. 图表架构文档**
- **位置**: `frontend/src/README.md`
- **作用**: 图表组件架构说明和使用指南

## 🎯 **脚本分类总结**

### **🚀 启动和构建脚本 (4个)**
- 前端/后端开发和生产启动
- TypeScript编译构建

### **🔧 开发和测试工具 (4个)**
- 币安API连接测试
- 数据库连接测试
- 跨周期逻辑测试
- 数据库清理工具

### **🗄️ 数据管理脚本 (8个)**
- 各种数据清理脚本
- K线数据管理
- 预测数据管理
- 节气配置管理

### **👤 用户和系统管理 (3个)**
- 管理员账户创建
- 示例文档创建
- 定时任务调度

### **🛠️ 工具函数和配置 (8个)**
- 节气工具函数
- 邮件发送工具
- 环境配置文件
- 文档说明文件

## 💡 **总计统计**

- **总脚本数量**: 28个
- **可执行脚本**: 20个
- **工具函数**: 5个
- **配置文件**: 3个

这些脚本工具覆盖了开发、测试、部署、维护的各个环节，为项目提供了完整的工具链支持。
