import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * 系统设置文档接口
 */
export interface SystemSettingsDocument extends Document {
  // 基础网站信息配置
  siteInfo: {
    siteName: string;               // 网站名称
    siteDescription: string;        // 网站描述
    siteKeywords: string;           // 网站关键词
    logoUrl: string;                // Logo URL（深色背景用）
    logoLightUrl?: string;          // Logo URL（浅色背景用）
    faviconUrl: string;             // Favicon URL
    copyright: string;              // 版权信息
    socialLinks: {                  // 社交媒体链接
      twitter?: string;
      telegram?: string;
      facebook?: string;
      instagram?: string;
      github?: string;
    };
    defaultLanguage: 'zh' | 'en';   // 默认语言设置
  };

  // 用户设置
  userSettings: {
    enableRegistration: boolean;    // 是否开启注册
    inviteCodeRequired: boolean;    // 是否必须使用邀请码注册
    defaultRole: 'trial' | 'normal'; // 默认注册角色
    passwordMinLength: number;      // 密码最小长度
    passwordRequireSpecialChar: boolean; // 密码是否需要特殊字符
    allowedEmailDomains: string[];  // 允许的邮箱域名后缀，空数组表示允许所有
    trialDays: number;              // 默认试用天数
    inviteRewardDays: number;       // 邀请奖励天数
  };

  // 订阅价格设置（美元）- 保留原有结构
  subscriptionPrices: {
    monthly: number;                // 月度订阅价格
    quarterly: number;              // 季度订阅价格
    yearly: number;                 // 年度订阅价格
  };

  // 订阅支付设置
  paymentSettings: {
    provider: string;               // 支付提供商，如 'nowpayments'
    apiKey: string;                 // 支付API密钥
    apiSecretKey: string;           // 支付API密钥（敏感信息）
    apiBaseUrl: string;             // 支付API基础URL
    payCurrency: string;            // 支付币种，如 'usdttrc20'
    callbackUrl: string;            // 支付回调URL
    successUrl: string;             // 支付成功跳转URL
    cancelUrl: string;              // 支付取消跳转URL
    failureLimit: number;           // 支付失败限制次数
    failureLockTime: number;        // 支付失败锁定时间（小时）
  };

  // 系统安全设置
  securitySettings: {
    siteDomain: string;             // 当前站点域名
    apiDomain: string;              // API域名
    binanceApiUrls: string[];       // 币安API基础地址列表
    binanceApiTimeout: number;      // 币安API请求超时时间（毫秒）
    binanceApiRetryTimes: number;   // 币安API请求重试次数
    loginFailLimit: number;         // 登录失败限制次数
    loginLockTime: number;          // 登录失败锁定时间（小时）
    enableKlinePrediction: boolean; // 是否启用K线预测图显示
    enableLinePrediction: boolean;  // 是否启用折线预测图显示
  };

  // 翻译设置
  translationSettings: {
    deeplApiKey: string;            // DeepL API 密钥
    googleApiKey: string;           // Google Translate API 密钥
    preferredService: 'deepl' | 'google';  // 首选翻译服务
    enableTranslation: boolean;     // 是否启用翻译功能
  };

  // 邮件服务设置
  emailSettings: {
    provider: 'resend' | 'brevo' | 'mailersend';  // 当前启用的服务商

    // Resend 配置
    resend: {
      apiKey: string;
      fromEmail: string;                          // Resend 发送邮箱
      enabled: boolean;
    };

    // Brevo 配置
    brevo: {
      apiKey: string;
      fromEmail: string;                          // Brevo 发送邮箱
      enabled: boolean;
    };

    // MailerSend 配置
    mailersend: {
      apiKey: string;
      fromEmail: string;                          // MailerSend 发送邮箱
      enabled: boolean;
    };

    // 高级设置
    retryTimes: number;                           // 重试次数
    timeout: number;                              // 超时时间(毫秒)
    fallbackProvider?: 'resend' | 'brevo' | 'mailersend'; // 备用服务商
    enableLogs: boolean;                          // 启用发送日志
  };

  // 系统日志设置
  logSettings: {
    retentionDays: number;                        // 日志保留天数
    enableAutoCleanup: boolean;                   // 启用自动清理
    maxFileSize: string;                          // 单个日志文件最大大小
  };

  // 验证码设置
  verificationSettings: {
    codeLength: number;                           // 验证码长度（固定6位）
    expireMinutes: number;                        // 验证码有效期（分钟）
    maxAttempts: number;                          // 最大尝试次数
    resendIntervalSeconds: number;                // 重发间隔（秒）
    enableCodeVerification: boolean;              // 是否启用验证码验证
    enableLinkVerification: boolean;              // 是否启用链接验证（向后兼容）
  };

  // 其他系统设置可在此扩展
  updatedAt: Date;                  // 最后更新时间
  updatedBy?: mongoose.Types.ObjectId; // 最后更新人
}

const systemSettingsSchema = new Schema({
  // 基础网站信息配置
  siteInfo: {
    siteName: {
      type: String,
      required: true,
      default: 'HAEHUB'
    },
    siteDescription: {
      type: String,
      required: true,
      default: 'Based on advanced AI algorithms, we provide you with accurate cryptocurrency analysis'
    },
    siteKeywords: {
      type: String,
      required: true,
      default: 'BTC、ETH、Bitcoin, Ethereum, Cryptocurrency, Dogecoin, Crypto Market, Bitcoin Trading'
    },

    copyright: {
      type: String,
      required: true,
      default: 'Copyright © 2025 Haehub Inc. All rights reserved.'
    },
    socialLinks: {
      twitter: {
        type: String,
        default: 'https://x.com/falingling'
      },
      telegram: {
        type: String,
        default: 'https://t.me/haehub'
      },
      facebook: {
        type: String,
        default: ''
      },
      instagram: {
        type: String,
        default: ''
      },
      github: {
        type: String,
        default: ''
      }
    },
    defaultLanguage: {
      type: String,
      enum: ['zh', 'en'],
      default: 'zh'
    }
  },

  // 用户设置
  userSettings: {
    enableRegistration: {
      type: Boolean,
      required: true,
      default: true
    },
    inviteCodeRequired: {
      type: Boolean,
      required: true,
      default: false
    },
    defaultRole: {
      type: String,
      enum: ['trial', 'normal'],
      required: true,
      default: 'trial'
    },
    passwordMinLength: {
      type: Number,
      required: true,
      default: 8
    },
    passwordRequireSpecialChar: {
      type: Boolean,
      required: true,
      default: false
    },
    allowedEmailDomains: {
      type: [String],
      default: []
    },
    trialDays: {
      type: Number,
      required: true,
      default: 7
    },
    inviteRewardDays: {
      type: Number,
      required: true,
      default: 14
    }
  },

  // 订阅价格设置 - 保留原有结构
  subscriptionPrices: {
    monthly: {
      type: Number,
      required: true,
      default: 39
    },
    quarterly: {
      type: Number,
      required: true,
      default: 87
    },
    yearly: {
      type: Number,
      required: true,
      default: 299
    }
  },

  // 订阅支付设置
  paymentSettings: {
    provider: {
      type: String,
      required: true,
      default: 'nowpayments'
    },
    apiKey: {
      type: String,
      default: ''
    },
    apiSecretKey: {
      type: String,
      default: ''
    },
    apiBaseUrl: {
      type: String,
      required: true,
      default: 'https://api-sandbox.nowpayments.io/v1'
    },
    payCurrency: {
      type: String,
      required: true,
      default: 'usdttrc20'
    },
    callbackUrl: {
      type: String,
      default: ''
    },
    successUrl: {
      type: String,
      default: ''
    },
    cancelUrl: {
      type: String,
      default: ''
    },
    failureLimit: {
      type: Number,
      required: true,
      default: 5
    },
    failureLockTime: {
      type: Number,
      required: true,
      default: 24
    }
  },

  // 系统安全设置
  securitySettings: {
    siteDomain: {
      type: String,
      default: ''
    },
    apiDomain: {
      type: String,
      default: ''
    },
    binanceApiUrls: {
      type: [String],
      default: [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ]
    },
    binanceApiTimeout: {
      type: Number,
      required: true,
      default: 30000
    },
    binanceApiRetryTimes: {
      type: Number,
      required: true,
      default: 3
    },
    loginFailLimit: {
      type: Number,
      required: true,
      default: 5
    },
    loginLockTime: {
      type: Number,
      required: true,
      default: 2
    },
    enableKlinePrediction: {
      type: Boolean,
      required: true,
      default: true
    },
    enableLinePrediction: {
      type: Boolean,
      required: true,
      default: true
    }
  },

  // 翻译设置
  translationSettings: {
    deeplApiKey: {
      type: String,
      default: ''
    },
    googleApiKey: {
      type: String,
      default: ''
    },
    preferredService: {
      type: String,
      enum: ['deepl', 'google'],
      default: 'deepl'
    },
    enableTranslation: {
      type: Boolean,
      required: true,
      default: false
    }
  },

  // 邮件服务设置
  emailSettings: {
    provider: {
      type: String,
      enum: ['resend', 'brevo', 'mailersend'],
      default: 'resend'
    },

    // Resend 配置
    resend: {
      apiKey: {
        type: String,
        default: ''
      },
      fromEmail: {
        type: String,
        default: '<EMAIL>'
      },
      enabled: {
        type: Boolean,
        default: true
      }
    },

    // Brevo 配置
    brevo: {
      apiKey: {
        type: String,
        default: ''
      },
      fromEmail: {
        type: String,
        default: '<EMAIL>'
      },
      enabled: {
        type: Boolean,
        default: true
      }
    },

    // MailerSend 配置
    mailersend: {
      apiKey: {
        type: String,
        default: ''
      },
      fromEmail: {
        type: String,
        default: '<EMAIL>'
      },
      enabled: {
        type: Boolean,
        default: false
      }
    },

    // 高级设置
    retryTimes: {
      type: Number,
      default: 2,
      min: 1,
      max: 10
    },
    timeout: {
      type: Number,
      default: 30000,
      min: 5000,
      max: 120000
    },
    fallbackProvider: {
      type: String,
      enum: ['resend', 'brevo', 'mailersend'],
      required: false,
      default: 'brevo'
    },
    enableLogs: {
      type: Boolean,
      default: true
    }
  },

  // 系统日志设置
  logSettings: {
    retentionDays: {
      type: Number,
      default: 15,        // 默认保留15天
      min: 1,
      max: 365
    },
    enableAutoCleanup: {
      type: Boolean,
      default: true       // 默认启用自动清理
    },
    maxFileSize: {
      type: String,
      default: '5MB'      // 单个日志文件最大5MB
    }
  },

  // 验证码设置
  verificationSettings: {
    codeLength: {
      type: Number,
      default: 6,         // 固定6位数字
      min: 4,
      max: 8
    },
    expireMinutes: {
      type: Number,
      default: 10,        // 默认10分钟有效期
      min: 1,
      max: 60
    },
    maxAttempts: {
      type: Number,
      default: 5,         // 默认最多5次尝试
      min: 3,
      max: 10
    },
    resendIntervalSeconds: {
      type: Number,
      default: 60,        // 默认60秒重发间隔
      min: 30,
      max: 300
    },
    enableCodeVerification: {
      type: Boolean,
      default: true       // 默认启用验证码验证
    },
    enableLinkVerification: {
      type: Boolean,
      default: true       // 默认启用链接验证（向后兼容）
    }
  },

  // 元数据
  updatedAt: {
    type: Date,
    default: Date.now
  },

  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  }
});

/**
 * 确保只有一条系统设置记录
 * 使用静态方法获取或创建系统设置
 */
systemSettingsSchema.static('getSettings', async function(): Promise<SystemSettingsDocument> {
  const settings = await this.findOne();
  if (settings) {
    return settings;
  }

  // 如果没有设置，创建默认设置
  return this.create({
    siteInfo: {
      siteName: 'HAEHUB',
      siteDescription: 'Based on advanced AI algorithms, we provide you with accurate cryptocurrency analysis',
      siteKeywords: 'BTC、ETH、Bitcoin, Ethereum, Cryptocurrency, Dogecoin, Crypto Market, Bitcoin Trading',
      copyright: 'Copyright © 2025 Haehub Inc. All rights reserved.',
      socialLinks: {
        twitter: 'https://x.com/falingling',
        telegram: 'https://t.me/haehub',
        facebook: '',
        instagram: '',
        github: ''
      },
      defaultLanguage: 'zh'
    },
    userSettings: {
      enableRegistration: true,
      inviteCodeRequired: false,
      defaultRole: 'trial',
      passwordMinLength: 8,
      passwordRequireSpecialChar: false,
      allowedEmailDomains: [],
      trialDays: 7,
      inviteRewardDays: 14
    },
    subscriptionPrices: {
      monthly: 39,
      quarterly: 87,
      yearly: 299
    },
    paymentSettings: {
      provider: 'nowpayments',
      apiKey: '',
      apiSecretKey: '',
      apiBaseUrl: 'https://api-sandbox.nowpayments.io/v1',
      payCurrency: 'usdttrc20',
      callbackUrl: '',
      successUrl: '',
      cancelUrl: '',
      failureLimit: 5,
      failureLockTime: 24
    },
    securitySettings: {
      siteDomain: '',
      apiDomain: '',
      binanceApiUrls: [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ],
      binanceApiTimeout: 30000,
      binanceApiRetryTimes: 3,
      loginFailLimit: 5,
      loginLockTime: 2,
      enableKlinePrediction: true,
      enableLinePrediction: true
    },
    translationSettings: {
      deeplApiKey: '',
      googleApiKey: '',
      preferredService: 'deepl',
      enableTranslation: false
    },
    emailSettings: {
      provider: 'resend',
      resend: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: true
      },
      brevo: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: true
      },
      mailersend: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: false
      },
      retryTimes: 2,
      timeout: 30000,
      fallbackProvider: 'brevo',
      enableLogs: true
    },
    logSettings: {
      retentionDays: 15,
      enableAutoCleanup: true,
      maxFileSize: '5MB'
    },
    verificationSettings: {
      codeLength: 6,
      expireMinutes: 10,
      maxAttempts: 5,
      resendIntervalSeconds: 60,
      enableCodeVerification: true,
      enableLinkVerification: true
    },
    updatedAt: new Date()
  });
});

/**
 * 系统设置模型接口 - 包含静态方法
 */
export interface SystemSettingsModel extends Model<SystemSettingsDocument> {
  getSettings(): Promise<SystemSettingsDocument>;
}

// 创建模型
const SystemSettings = mongoose.model<SystemSettingsDocument, SystemSettingsModel>(
  'SystemSettings',
  systemSettingsSchema
);

export default SystemSettings;