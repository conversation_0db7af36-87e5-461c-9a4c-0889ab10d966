import express from 'express';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
import SolarTermPredictionLine from '../../models/SolarTermPredictionLine';
import Prediction from '../../models/Prediction';
import ThirtyMinKline from '../../models/ThirtyMinKline';
import SolarTermConfig from '../../models/SolarTermConfig';
import { solarTermDataLoader } from '../../utils/solarTermDataLoader';
import logger from '../../utils/logger';

const router = express.Router();
const execAsync = promisify(exec);

// 数据清理接口
router.post('/data-cleanup', async (req, res) => {
  try {
    const { dataType, action, symbol, beforeDate, afterDate, startDate, endDate } = req.body;

    let timeField: string;
    let dataTypeName: string;
    let result: any;

    // 构建查询条件
    const query: any = {};

    // 添加交易对过滤
    if (symbol) {
      query.symbol = symbol;
    }

    // 根据数据类型执行不同的删除操作
    switch (dataType) {
      case 'solar-term-lines':
        timeField = 'targetStartTime';
        dataTypeName = '节气预测折线';

        // 添加时间过滤
        if (action !== 'all') {
          query[timeField] = {};

          switch (action) {
            case 'before':
              if (!beforeDate) {
                return res.status(400).json({ message: '缺少截止日期参数' });
              }
              const beforeTime = new Date(beforeDate);
              beforeTime.setHours(23, 59, 59, 999);
              query[timeField].$lt = beforeTime.getTime();
              break;

            case 'after':
              if (!afterDate) {
                return res.status(400).json({ message: '缺少起始日期参数' });
              }
              const afterTime = new Date(afterDate);
              afterTime.setHours(0, 0, 0, 0);
              query[timeField].$gt = afterTime.getTime();
              break;

            case 'range':
              if (!startDate || !endDate) {
                return res.status(400).json({ message: '缺少时间范围参数' });
              }
              const start = new Date(startDate);
              start.setHours(0, 0, 0, 0);
              const end = new Date(endDate);
              end.setHours(23, 59, 59, 999);
              query[timeField].$gte = start.getTime();
              query[timeField].$lte = end.getTime();
              break;

            default:
              return res.status(400).json({ message: '无效的清理方式' });
          }
        }

        result = await SolarTermPredictionLine.deleteMany(query);
        break;

      case 'prediction-klines':
        timeField = 'targetStartTime';
        dataTypeName = '预测K线';

        // 添加时间过滤
        if (action !== 'all') {
          query[timeField] = {};

          switch (action) {
            case 'before':
              if (!beforeDate) {
                return res.status(400).json({ message: '缺少截止日期参数' });
              }
              const beforeTime2 = new Date(beforeDate);
              beforeTime2.setHours(23, 59, 59, 999);
              query[timeField].$lt = beforeTime2.getTime();
              break;

            case 'after':
              if (!afterDate) {
                return res.status(400).json({ message: '缺少起始日期参数' });
              }
              const afterTime2 = new Date(afterDate);
              afterTime2.setHours(0, 0, 0, 0);
              query[timeField].$gt = afterTime2.getTime();
              break;

            case 'range':
              if (!startDate || !endDate) {
                return res.status(400).json({ message: '缺少时间范围参数' });
              }
              const start2 = new Date(startDate);
              start2.setHours(0, 0, 0, 0);
              const end2 = new Date(endDate);
              end2.setHours(23, 59, 59, 999);
              query[timeField].$gte = start2.getTime();
              query[timeField].$lte = end2.getTime();
              break;

            default:
              return res.status(400).json({ message: '无效的清理方式' });
          }
        }

        result = await Prediction.deleteMany(query);
        break;

      case 'real-time-klines':
        timeField = 'openTime';
        dataTypeName = '实时K线';

        // 添加时间过滤
        if (action !== 'all') {
          query[timeField] = {};

          switch (action) {
            case 'before':
              if (!beforeDate) {
                return res.status(400).json({ message: '缺少截止日期参数' });
              }
              const beforeTime3 = new Date(beforeDate);
              beforeTime3.setHours(23, 59, 59, 999);
              query[timeField].$lt = beforeTime3.getTime();
              break;

            case 'after':
              if (!afterDate) {
                return res.status(400).json({ message: '缺少起始日期参数' });
              }
              const afterTime3 = new Date(afterDate);
              afterTime3.setHours(0, 0, 0, 0);
              query[timeField].$gt = afterTime3.getTime();
              break;

            case 'range':
              if (!startDate || !endDate) {
                return res.status(400).json({ message: '缺少时间范围参数' });
              }
              const start3 = new Date(startDate);
              start3.setHours(0, 0, 0, 0);
              const end3 = new Date(endDate);
              end3.setHours(23, 59, 59, 999);
              query[timeField].$gte = start3.getTime();
              query[timeField].$lte = end3.getTime();
              break;

            default:
              return res.status(400).json({ message: '无效的清理方式' });
          }
        }

        result = await ThirtyMinKline.deleteMany(query);
        break;

      case 'solar-term-configs':
        timeField = 'createdAt';
        dataTypeName = '节气预测转换比例配置';

        // 添加时间过滤
        if (action !== 'all') {
          query[timeField] = {};

          switch (action) {
            case 'before':
              if (!beforeDate) {
                return res.status(400).json({ message: '缺少截止日期参数' });
              }
              const beforeTime4 = new Date(beforeDate);
              beforeTime4.setHours(23, 59, 59, 999);
              query[timeField].$lt = beforeTime4;
              break;

            case 'after':
              if (!afterDate) {
                return res.status(400).json({ message: '缺少起始日期参数' });
              }
              const afterTime4 = new Date(afterDate);
              afterTime4.setHours(0, 0, 0, 0);
              query[timeField].$gt = afterTime4;
              break;

            case 'range':
              if (!startDate || !endDate) {
                return res.status(400).json({ message: '缺少时间范围参数' });
              }
              const start4 = new Date(startDate);
              start4.setHours(0, 0, 0, 0);
              const end4 = new Date(endDate);
              end4.setHours(23, 59, 59, 999);
              query[timeField].$gte = start4;
              query[timeField].$lte = end4;
              break;

            default:
              return res.status(400).json({ message: '无效的清理方式' });
          }
        }

        result = await SolarTermConfig.deleteMany(query);
        break;

      default:
        return res.status(400).json({ message: '无效的数据类型' });
    }

    res.json({
      success: true,
      message: `成功删除 ${result.deletedCount} 条${dataTypeName}数据`,
      deletedCount: result.deletedCount,
      query: query
    });

  } catch (error) {
    logger.error('数据清理失败:', error);
    res.status(500).json({ 
      message: '数据清理失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
});

// 获取节气数据
router.get('/solar-terms', async (req, res) => {
  try {
    // 使用数据加载器获取节气数据
    const data = solarTermDataLoader.getSolarTermData();
    const fileContent = solarTermDataLoader.getFileContent();

    res.json({
      success: true,
      data: data,
      fileContent: fileContent // 返回完整的文件内容
    });

  } catch (error) {
    logger.error('获取节气数据失败:', error);
    res.status(500).json({
      message: '获取节气数据失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 更新节气数据
router.post('/solar-terms/update', async (req, res) => {
  try {
    const { years = 2 } = req.body;
    const scriptsPath = path.resolve(__dirname, '../../../scripts');
    const scriptPath = path.join(scriptsPath, 'updateSolarTerms.js');

    // 执行节气更新脚本
    const { stdout, stderr } = await execAsync(`node "${scriptPath}" ${years}`, {
      cwd: scriptsPath,
      timeout: 30000 // 30秒超时
    });

    if (stderr) {
      logger.error('节气更新脚本错误输出:', stderr);
    }

    res.json({
      success: true,
      message: `成功更新未来 ${years} 年的节气数据`,
      output: stdout
    });

  } catch (error) {
    logger.error('更新节气数据失败:', error);
    res.status(500).json({ 
      message: '更新节气数据失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
});



// 保存节气数据文件内容
router.put('/solar-terms/file', async (req, res) => {
  try {
    const { content } = req.body;

    if (!content || typeof content !== 'string') {
      return res.status(400).json({ message: '无效的文件内容' });
    }

    // 使用数据加载器保存文件内容
    const success = solarTermDataLoader.saveFileContent(content);

    if (success) {
      res.json({
        success: true,
        message: '文件保存成功'
      });
    } else {
      res.status(500).json({
        message: '保存文件失败'
      });
    }

  } catch (error) {
    logger.error('保存文件失败:', error);
    res.status(500).json({
      message: '保存文件失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;
