import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcrypt';

export interface UserDocument extends Document {
  _id: mongoose.Types.ObjectId;
  email: string;
  username?: string;
  password: string;
  role: 'trial' | 'normal' | 'subscriber' | 'admin' | 'banned';
  trialEndsAt: Date;
  isVerified: boolean;
  registeredIp: string;
  invitedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  lastLoginAt?: Date;
  lastActiveAt?: Date; // 最后活跃时间
  isOnline?: boolean;
  loginFailCount?: number; // 登录失败计数
  lockedUntil?: Date; // 账号锁定时间
  subscription?: {
    plan: 'monthly' | 'quarterly' | 'yearly';
    startDate: Date;
    endDate: Date;
    status: 'active' | 'expired' | 'cancelled';
    paymentId?: string;
  };
  rewardHistory?: Array<{
    type: 'invite' | 'other';
    days: number;
    grantedAt: Date;
    reason: string;
  }>;
  comparePassword(candidatePassword: string): Promise<boolean>;
  originalRole: 'trial' | 'normal' | 'subscriber' | 'admin';
}

const userSchema = new Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  username: {
    type: String,
    unique: true,
    sparse: true,
    default: null
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['trial', 'normal', 'subscriber', 'admin', 'banned'],
    default: 'trial'
  },
  trialEndsAt: {
    type: Date,
    required: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  registeredIp: {
    type: String,
    required: true
  },
  invitedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastLoginAt: {
    type: Date,
    default: null
  },
  lastActiveAt: {
    type: Date,
    default: Date.now
  },
  isOnline: {
    type: Boolean,
    default: false
  },
  loginFailCount: {
    type: Number,
    default: 0
  },
  lockedUntil: {
    type: Date,
    default: null
  },
  subscription: {
    plan: {
      type: String,
      enum: ['monthly', 'quarterly', 'yearly'],
      default: null
    },
    startDate: {
      type: Date,
      default: null
    },
    endDate: {
      type: Date,
      default: null
    },
    status: {
      type: String,
      enum: ['active', 'expired', 'cancelled'],
      default: null
    },
    paymentId: {
      type: String,
      default: null
    }
  },
  originalRole: {
    type: String,
    enum: ['trial', 'normal', 'subscriber', 'admin'],
    required: false
  },
  rewardHistory: [{
    type: {
      type: String,
      enum: ['invite', 'other'],
      required: true
    },
    days: {
      type: Number,
      required: true
    },
    grantedAt: {
      type: Date,
      default: Date.now
    },
    reason: {
      type: String,
      required: true
    }
  }]
});

// 保存前加密密码
userSchema.pre('save', async function(next) {
  // 使用类型断言前先转为 unknown，避免直接转换错误
  const user = this as unknown as UserDocument;

  // 仅当密码被修改时才重新加密
  if (!user.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// 比较密码的方法
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model<UserDocument>('User', userSchema);

export default User;