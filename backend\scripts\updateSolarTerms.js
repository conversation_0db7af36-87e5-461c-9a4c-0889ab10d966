/**
 * 节气数据自动更新脚本（精简版）
 *
 * 功能：
 * 1. 使用 lunar-javascript 库计算未来年份的节气数据
 * 2. 验证数据准确性和完整性
 * 3. 更新 solarTermData.ts 常量文件
 * 4. 智能数据合并：保留现有数据，添加新数据，更新重复数据
 * 5. 自动清理：清理1年前的过期数据，保持数据文件精简
 *
 * 使用方法:
 * node updateSolarTerms.js [years]
 *
 * 参数:
 * years - 可选，获取未来几年的数据，默认为2年，最大5年
 *
 * 示例:
 * node updateSolarTerms.js     # 获取未来2年数据（2026-2027）
 * node updateSolarTerms.js 3   # 获取未来3年数据（2026-2028）
 * node updateSolarTerms.js 1   # 获取未来1年数据（2026）
 *
 * 数据清理策略:
 * - 保留：当前年份及以后的所有数据
 * - 清理：1年前的历史数据（避免文件无限增长）
 */

const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const { getFutureSolarTerms } = require('./solarTermCalculator');
const { updateSolarTermsFile } = require('./solarTermFileUpdater');

// 配置参数
const CONFIG = {
  // 默认获取未来几年的数据
  DEFAULT_YEARS_AHEAD: 2,
  
  // 最大允许获取的年数
  MAX_YEARS_AHEAD: 5,
  
  // 是否启用详细日志
  VERBOSE_LOGGING: true
};

/**
 * 解析命令行参数
 * @returns {Object} 解析后的参数
 */
function parseArguments() {
  const args = process.argv.slice(2);
  
  let yearsAhead = CONFIG.DEFAULT_YEARS_AHEAD;
  
  if (args.length > 0) {
    const inputYears = parseInt(args[0]);
    if (isNaN(inputYears) || inputYears < 1 || inputYears > CONFIG.MAX_YEARS_AHEAD) {
      console.error(`❌ 无效的年数参数: ${args[0]}`);
      console.error(`   年数必须是1-${CONFIG.MAX_YEARS_AHEAD}之间的整数`);
      process.exit(1);
    }
    yearsAhead = inputYears;
  }
  
  return { yearsAhead };
}

/**
 * 显示脚本信息
 * @param {number} yearsAhead 获取的年数
 */
function showScriptInfo(yearsAhead) {
  const currentYear = new Date().getFullYear();
  const targetYears = [];

  for (let i = 1; i <= yearsAhead; i++) {
    targetYears.push(currentYear + i);
  }

  console.log('🌟 节气数据自动更新脚本（精简版）');
  console.log('=====================================');
  console.log(`📅 当前年份: ${currentYear}`);
  console.log(`🎯 目标年份: ${targetYears.join(', ')}`);
  console.log(`📊 获取年数: ${yearsAhead}年`);
  console.log(`🔧 计算库: lunar-javascript`);
  console.log(`📝 更新文件: src/data/solarTermData.json`);
  console.log(`🗑️ 清理策略: 保留${currentYear - 1}年及以后数据`);
  console.log('=====================================\n');
}

/**
 * 验证运行环境
 * @returns {boolean} 环境是否正常
 */
function validateEnvironment() {
  try {
    // 检查 lunar-javascript 库是否可用
    require('lunar-javascript');
    console.log('✅ lunar-javascript 库检查通过');
    
    // 检查目标目录是否存在
    const fs = require('fs');
    const targetDir = path.join(__dirname, '../src/data');

    if (!fs.existsSync(targetDir)) {
      console.log('📁 创建数据目录:', targetDir);
      fs.mkdirSync(targetDir, { recursive: true });
    }
    console.log('✅ 目标目录检查通过');
    
    return true;
    
  } catch (error) {
    console.error('❌ 环境验证失败:', error.message);
    console.error('   请确保已安装 lunar-javascript 库: npm install lunar-javascript');
    return false;
  }
}

/**
 * 显示执行结果摘要
 * @param {Object} result 执行结果
 */
function showResultSummary(result) {
  console.log('\n📋 执行结果摘要');
  console.log('=====================================');

  if (result.success) {
    console.log('✅ 状态: 成功');
    console.log(`📊 更新年份: ${Object.keys(result.data || {}).join(', ')}`);
    console.log(`⏰ 执行时间: ${result.duration}ms`);
    console.log(`🔧 更新模式: 精简版（无备份）`);
  } else {
    console.log('❌ 状态: 失败');
    console.log(`💥 错误信息: ${result.error}`);
  }

  console.log('=====================================');
}

/**
 * 主执行函数
 */
async function main() {
  const startTime = Date.now();
  let result = {
    success: false,
    data: null,
    error: null,
    duration: 0
  };
  
  try {
    // 1. 解析命令行参数
    const { yearsAhead } = parseArguments();
    
    // 2. 显示脚本信息
    showScriptInfo(yearsAhead);
    
    // 3. 验证运行环境
    if (!validateEnvironment()) {
      throw new Error('运行环境验证失败');
    }
    
    // 4. 获取节气数据
    console.log('🔄 开始获取节气数据...\n');
    const solarTermsData = await getFutureSolarTerms(yearsAhead);
    
    if (!solarTermsData || Object.keys(solarTermsData).length === 0) {
      throw new Error('未能获取到有效的节气数据');
    }
    
    console.log(`\n✅ 成功获取${Object.keys(solarTermsData).length}年的节气数据`);
    
    // 5. 更新文件
    console.log('\n🔄 开始更新节气数据文件...\n');
    const updateSuccess = await updateSolarTermsFile(solarTermsData);
    
    if (!updateSuccess) {
      throw new Error('节气数据文件更新失败');
    }
    
    // 6. 记录成功结果
    result.success = true;
    result.data = solarTermsData;
    result.duration = Date.now() - startTime;
    
    console.log('\n🎉 节气数据更新完成！');
    
  } catch (error) {
    console.error('\n💥 脚本执行失败:', error.message);
    
    result.success = false;
    result.error = error.message;
    result.duration = Date.now() - startTime;
    
    // 这里可以添加更多的错误处理逻辑
    // 比如发送告警邮件、记录错误日志等
  } finally {
    // 7. 显示执行结果
    showResultSummary(result);
    
    // 8. 退出程序
    process.exit(result.success ? 0 : 1);
  }
}

/**
 * 处理未捕获的异常
 */
process.on('uncaughtException', (error) => {
  console.error('\n💥 未捕获的异常:', error.message);
  console.error('堆栈信息:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  main,
  parseArguments,
  validateEnvironment
};
