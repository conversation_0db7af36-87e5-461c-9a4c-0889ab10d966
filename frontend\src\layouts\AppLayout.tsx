import React, { useEffect, useState } from 'react';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu";
import { Button } from "../components/ui/button";
import { User, LogOut } from "lucide-react";

import useUserStore from '../store/useUserStore';
import configService, { SiteInfo } from '../services/configService';
import GeometricLogo from '../components/GeometricLogo';
import CyberLanguageSwitcher from '../components/CyberLanguageSwitcher';
import CyberFooter from '../components/CyberFooter';



const AppLayout: React.FC = () => {
  const { t } = useTranslation();
  const { logout } = useUserStore();
  const navigate = useNavigate();
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        const config = await configService.getConfig();
        setSiteInfo(config.siteInfo);

        // 语言设置已在 App.tsx 中处理，这里只需要加载配置
      } catch (error) {
        console.error('加载网站配置失败:', error);
      }
    };

    loadSiteInfo();
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-cyber-bg flex flex-col">
      {/* 网格背景 */}
      <div className="fixed inset-0 bg-[linear-gradient(rgba(0,245,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(0,245,255,0.03)_1px,transparent_1px)] bg-[size:50px_50px] pointer-events-none" />

      {/* 顶部导航栏 */}
      <header className="relative z-10 h-16 bg-cyber-card/20 backdrop-blur-md border-b border-cyber-border/50 flex-shrink-0">
        {/* 动画边框 */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent animate-pulse-neon" />

        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Left side: Logo + Navigation Menu */}
            <div className="flex items-center space-x-8">
              {/* Logo */}
              <GeometricLogo
                to="/dashboard"
                siteName={siteInfo?.siteName || t('app.title')}
                size="xl"
              />

              {/* 导航链接 */}
              <nav className="hidden md:flex items-center space-x-6">
                <Link
                  to="/dashboard"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  Predictron
                </Link>
                <Link
                  to="/gna"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  Gna
                </Link>
                <Link
                  to="/subscribe"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.upgrade')}
                </Link>
                <Link
                  to="/docs"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.docs')}
                </Link>
                <Link
                  to="/faq"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.faq')}
                </Link>
                <Link
                  to="/announcement"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.announcement')}
                </Link>
              </nav>
            </div>

            {/* Right side: Language Switcher + User Menu */}
            <div className="flex items-center space-x-4">
              {/* 语言切换 */}
              <CyberLanguageSwitcher />

              {/* 用户菜单 */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-cyber-text hover:text-cyber-cyan border border-cyber-border hover:border-cyber-cyan/30 bg-cyber-card/30 hover:bg-cyber-card/40 backdrop-blur-sm transition-all duration-300 h-10 w-10 font-mono text-sm outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-cyber-cyan/30"
                  >
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="bg-cyber-card/95 backdrop-blur-md border-cyber-border text-cyber-text outline-none focus:outline-none"
                  sideOffset={8}
                >
                  <DropdownMenuItem
                    onClick={() => navigate('/user-center')}
                    className="cursor-pointer hover:bg-cyber-cyan/15 hover:text-cyber-cyan focus:bg-cyber-cyan/15 focus:text-cyber-cyan transition-colors duration-200 font-mono outline-none focus:outline-none"
                  >
                    <User className="mr-2 h-4 w-4" />
                    <span>{t('nav.userCenter')}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer hover:bg-cyber-cyan/15 hover:text-cyber-cyan focus:bg-cyber-cyan/15 focus:text-cyber-cyan transition-colors duration-200 font-mono outline-none focus:outline-none"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t('nav.logout')}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="relative z-10 flex-1">
        <Outlet />
      </main>

      {/* 页脚 */}
      <CyberFooter theme="cyber" className="relative z-10" />
    </div>
  );
};

export default AppLayout;