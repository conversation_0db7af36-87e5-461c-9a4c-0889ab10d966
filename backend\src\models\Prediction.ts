import mongoose, { Document, Schema } from 'mongoose';

/**
 * 预测数据接口
 * 定义预测数据文档的结构和类型
 */
export interface IPrediction extends Document {
  symbol: string;           // 交易对符号（例如：BTCUSDT）
  predictionTime: number;   // 预测生成的时间戳（毫秒）
  targetStartTime: number;  // 预测目标时间段的开始时间戳（毫秒）
  targetEndTime: number;    // 预测目标时间段的结束时间戳（毫秒）
  sourceKlineTime: number;  // 用于预测的1分钟K线时间戳（毫秒）
  open: string;             // 预测开盘价
  high: string;             // 预测最高价
  low: string;              // 预测最低价
  close: string;            // 预测收盘价
  isActive: boolean;        // 是否为最新的预测
  createdAt: Date;          // 记录创建时间
}

// 删除之前定义的模型（如果存在）以确保索引被正确重置
try {
  if (mongoose.models.Prediction) {
    delete mongoose.models.Prediction;
  }
} catch (error) {
  console.error('删除旧模型时出错:', error);
}

/**
 * 预测数据模式
 * 定义MongoDB中预测数据集合的结构
 */
const PredictionSchema: Schema = new Schema({
  symbol: { type: String, required: true, index: true },           // 交易对符号
  predictionTime: { type: Number, required: true },                // 预测生成时间
  targetStartTime: { type: Number, required: true, index: true },  // 目标开始时间
  targetEndTime: { type: Number, required: true },                 // 目标结束时间
  sourceKlineTime: { type: Number, required: true },               // 源K线时间
  open: { type: String, required: true },                          // 开盘价
  high: { type: String, required: true },                          // 最高价
  low: { type: String, required: true },                           // 最低价
  close: { type: String, required: true },                         // 收盘价
  isActive: { type: Boolean, default: true },                      // 是否活跃
  createdAt: { type: Date, default: Date.now }                     // 创建时间
}, {
  timestamps: true,    // 自动添加createdAt和updatedAt字段
  versionKey: false    // 不使用版本字段（__v）
});

// 清除之前的索引并重新定义新索引
PredictionSchema.index({ symbol: 1, targetStartTime: 1 }, { unique: true });

// 添加前置钩子，确保所有必须字段都有值
PredictionSchema.pre('save', function(next) {
  // 确保必要的时间戳字段不为null
  if (!this.targetStartTime || !this.targetEndTime || !this.sourceKlineTime) {
    return next(new Error('时间戳字段不能为null'));
  }
  next();
});

// 创建一个初始化函数，用于清理旧索引
async function initializePredictionModel() {
  const model = mongoose.model<IPrediction>('Prediction', PredictionSchema);
  
  try {
    // 获取集合实例
    const collection = model.collection;
    
    // 获取当前所有索引
    const indexes = await collection.indexes();
    console.log('当前预测集合的索引:', indexes);
    
    // 查找并删除旧索引
    for (const index of indexes) {
      if (index.name && 
         (index.name.includes('sourceTimestamp') || 
          index.name.includes('targetTimestamp'))) {
        console.log(`删除旧索引: ${index.name}`);
        await collection.dropIndex(index.name);
      }
    }
    
    console.log('预测模型初始化完成，旧索引已删除');
  } catch (error) {
    console.error('初始化预测模型时出错:', error);
  }
  
  return model;
}

// 导出预测数据模型
export default mongoose.models.Prediction || 
  mongoose.model<IPrediction>('Prediction', PredictionSchema); 