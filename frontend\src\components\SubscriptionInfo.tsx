import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from './ui/button';
import { getSubscriptionInfo, getPaymentHistory, SubscriptionInfo as SubscriptionInfoType, PaymentHistory } from '../api/subscription';

import { CreditCard, Clock, Calendar, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { formatDate } from '../utils/dateFormatter';

// 订阅信息组件
const SubscriptionInfo: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [subscription, setSubscription] = useState<SubscriptionInfoType | null>(null);
  const [payments, setPayments] = useState<PaymentHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // 获取订阅信息和支付历史
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // 并行请求数据
        const [subscriptionResponse, paymentsResponse] = await Promise.all([
          getSubscriptionInfo(),
          getPaymentHistory()
        ]);
        
        setSubscription(subscriptionResponse.subscription);
        setPayments(paymentsResponse.payments);
      } catch (error) {
        console.error('获取订阅数据失败:', error);
        setError(t('subscription.loadDataError'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // 状态对应的颜色和文案
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'active':
        return { label: t('subscription.active'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-green/20 text-cyber-green border border-cyber-green/30 font-mono' };
      case 'trial':
        return { label: t('subscription.trial'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-purple/20 text-cyber-purple border border-cyber-purple/30 font-mono' };
      case 'finished':
        return { label: t('subscription.finished'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-green/20 text-cyber-green border border-cyber-green/30 font-mono' };
      case 'pending':
        return { label: t('subscription.pending'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-purple/20 text-cyber-purple border border-cyber-purple/30 font-mono' };
      case 'failed':
        return { label: t('subscription.failed'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-pink/20 text-cyber-pink border border-cyber-pink/30 font-mono' };
      case 'expired':
        return { label: t('subscription.expired'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-pink/20 text-cyber-pink border border-cyber-pink/30 font-mono' };
      default:
        return { label: t('subscription.inactive'), color: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-muted/20 text-cyber-muted border border-cyber-muted/30 font-mono' };
    }
  };

  // 日期格式化工具
  const formatSubscriptionDate = (dateString: string | null | undefined) => {
    if (!dateString) return t('subscription.none');
    return formatDate(dateString, i18n.language);
  };

  // 时间格式化工具（显示完整的日期和时间）
  const formatPaymentTime = (dateString: string | null | undefined) => {
    if (!dateString) return t('subscription.none');
    const date = new Date(dateString);
    return date.toLocaleString(i18n.language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: i18n.language === 'en'
    });
  };

  // 订阅类型转换
  const getPlanName = (type: string | null | undefined) => {
    switch (type) {
      case 'monthly':
        return t('subscription.monthly');
      case 'quarterly':
        return t('subscription.quarterly');
      case 'yearly':
        return t('subscription.yearly');
      case 'trial':
        return t('subscription.trial');
      default:
        return t('subscription.none');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-cyber-cyan" />
        <span className="ml-2 text-cyber-muted font-mono">{t('common.loading')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-cyber-pink/10 border border-cyber-pink/30 text-cyber-pink rounded-xl font-mono backdrop-blur-sm">
        <AlertCircle className="h-5 w-5 mb-2" />
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 订阅状态卡片 */}
      <div className="border border-cyber-cyan/30 rounded-xl p-5 bg-cyber-dark/50 backdrop-blur-sm">
        <div className="flex justify-between items-start">
          <div className="w-full">
            <h3 className="text-lg font-semibold mb-4 text-cyber-cyan font-mono">{t('subscription.subscriptionStatusCard')}</h3>

            <dl className="flex flex-wrap justify-between items-center">
              <div className="flex flex-col">
                <dt className="text-sm text-cyber-muted mb-1 flex items-center font-mono">
                  <Clock className="w-4 h-4 mr-1" />{t('subscription.status')}
                </dt>
                <dd>
                  <span
                    className={getStatusInfo(subscription?.status || 'inactive').color}
                  >
                    {getStatusInfo(subscription?.status || 'inactive').label}
                  </span>
                </dd>
              </div>

              <div className="flex flex-col">
                <dt className="text-sm text-cyber-muted mb-1 flex items-center font-mono">
                  <CreditCard className="w-4 h-4 mr-1" />{t('subscription.type')}
                </dt>
                <dd className="font-medium text-cyber-text font-mono">{getPlanName(subscription?.type)}</dd>
              </div>

              <div className="flex flex-col">
                <dt className="text-sm text-cyber-muted mb-1 flex items-center font-mono">
                  <Calendar className="w-4 h-4 mr-1" />{t('subscription.expiryDate')}
                </dt>
                <dd className="font-medium text-cyber-text font-mono">{formatSubscriptionDate(subscription?.endDate)}</dd>
              </div>

              <div className="flex flex-col">
                <dt className="text-sm text-cyber-muted mb-1 flex items-center font-mono">
                  <Clock className="w-4 h-4 mr-1" />{t('subscription.remainingDays')}
                </dt>
                <dd className="font-medium text-cyber-text font-mono">
                  {subscription?.daysRemaining ? `${subscription.daysRemaining} ${t('user.days')}` : `0 ${t('user.days')}`}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {subscription?.status !== 'active' && (
          <div className="mt-5 pt-5 border-t border-cyber-cyan/20">
            <Button
              className="w-full sm:w-auto bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-mono transition-all duration-200"
              onClick={() => navigate('/subscribe')}
            >
              {subscription?.status === 'trial' ? t('subscription.upgradeNow') : t('subscription.startSubscription')}
            </Button>
          </div>
        )}
      </div>

      {/* 支付历史 */}
      <div className="border border-cyber-cyan/30 rounded-xl p-5 bg-cyber-dark/50 backdrop-blur-sm">
        <h3 className="text-lg font-semibold mb-4 text-cyber-cyan font-mono">{t('subscription.paymentHistory')}</h3>

        {payments.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-cyber-cyan/10 border-b border-cyber-cyan/20">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('subscription.subscriptionPlan')}</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('subscription.amount')}</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('subscription.status')}</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('subscription.time')}</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-cyber-cyan/20">
                {payments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-cyber-cyan/5 transition-colors">
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-cyber-text font-mono">
                      {getPlanName(payment.plan)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-cyber-text font-mono">
                      ${payment.amount}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <span
                        className={getStatusInfo(payment.status).color}
                      >
                        {getStatusInfo(payment.status).label}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-cyber-muted font-mono">
                      {formatPaymentTime(payment.createdAt)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-4 rounded-xl text-center border border-cyber-cyan/20 bg-cyber-dark/30">
            <p className="text-cyber-muted text-sm font-mono">{t('subscription.noPaymentRecords')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionInfo; 