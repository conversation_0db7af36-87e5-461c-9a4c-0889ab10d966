import React, { useState } from 'react';
import FeedbackForm from './FeedbackForm';
import FeedbackList from './FeedbackList';

const UserFeedback: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // 提交成功后刷新列表
  const handleSubmitSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      <FeedbackForm onSuccess={handleSubmitSuccess} />
      <FeedbackList refreshTrigger={refreshTrigger} />
    </div>
  );
};

export default UserFeedback;
