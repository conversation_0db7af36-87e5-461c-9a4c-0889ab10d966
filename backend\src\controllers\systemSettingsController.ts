import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import axios from 'axios';

// 配置内存存储，用于处理文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制5MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许上传图片
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp', 'image/x-icon'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型，仅支持图片文件'));
    }
  }
});

/**
 * 获取系统设置
 * GET /api/admin/settings
 */
export const getSystemSettings = async (req: Request, res: Response) => {
  try {
    const settings = await systemSettingsService.getSystemSettings();

    // 处理敏感信息脱敏
    let paymentSettings = null;
    if (settings.paymentSettings) {
      paymentSettings = {
        ...settings.paymentSettings,
        // 对API密钥进行脱敏处理
        apiKey: settings.paymentSettings.apiKey ?
          `${settings.paymentSettings.apiKey.substring(0, 4)}****${settings.paymentSettings.apiKey.slice(-4)}` : '',
        apiSecretKey: settings.paymentSettings.apiSecretKey ?
          `${settings.paymentSettings.apiSecretKey.substring(0, 4)}****${settings.paymentSettings.apiSecretKey.slice(-4)}` : ''
      };
    }

    // 处理翻译设置脱敏
    let translationSettings = null;
    if (settings.translationSettings) {
      translationSettings = {
        ...settings.translationSettings,
        // 对API密钥进行脱敏处理
        deeplApiKey: settings.translationSettings.deeplApiKey ?
          `${settings.translationSettings.deeplApiKey.substring(0, 4)}****${settings.translationSettings.deeplApiKey.slice(-4)}` : '',
        googleApiKey: settings.translationSettings.googleApiKey ?
          `${settings.translationSettings.googleApiKey.substring(0, 4)}****${settings.translationSettings.googleApiKey.slice(-4)}` : ''
      };
    }

    // 处理邮件设置（隐藏敏感信息）
    const emailSettings = {
      ...settings.emailSettings,
      resend: {
        ...settings.emailSettings.resend,
        apiKey: settings.emailSettings.resend.apiKey ? '***已配置***' : ''
      },
      brevo: {
        ...settings.emailSettings.brevo,
        apiKey: settings.emailSettings.brevo.apiKey ? '***已配置***' : ''
      },
      mailersend: {
        ...settings.emailSettings.mailersend,
        apiKey: settings.emailSettings.mailersend.apiKey ? '***已配置***' : ''
      }
    };

    res.json({
      success: true,
      settings: {
        siteInfo: settings.siteInfo,
        userSettings: settings.userSettings,
        subscriptionPrices: settings.subscriptionPrices,
        paymentSettings: paymentSettings,
        securitySettings: settings.securitySettings,
        translationSettings: translationSettings,
        emailSettings: emailSettings,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('获取系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新系统设置
 * PUT /api/admin/settings
 */
export const updateSystemSettings = async (req: Request, res: Response) => {
  try {
    const { siteInfo, userSettings, subscriptionPrices, paymentSettings: reqPaymentSettings, securitySettings, translationSettings: reqTranslationSettings, emailSettings: reqEmailSettings } = req.body;

    // 验证请求体
    // 用户设置验证
    if (userSettings) {
      if (userSettings.trialDays !== undefined && (isNaN(userSettings.trialDays) || userSettings.trialDays < 0)) {
        return res.status(400).json({
          success: false,
          message: '试用天数必须是非负数'
        });
      }

      if (userSettings.inviteRewardDays !== undefined && (isNaN(userSettings.inviteRewardDays) || userSettings.inviteRewardDays < 0)) {
        return res.status(400).json({
          success: false,
          message: '邀请奖励天数必须是非负数'
        });
      }

      if (userSettings.passwordMinLength !== undefined && (isNaN(userSettings.passwordMinLength) || userSettings.passwordMinLength < 4)) {
        return res.status(400).json({
          success: false,
          message: '密码最小长度不能小于4'
        });
      }
    }

    // 订阅价格验证
    if (subscriptionPrices) {
      if (
        (subscriptionPrices.monthly !== undefined && (isNaN(subscriptionPrices.monthly) || subscriptionPrices.monthly < 0)) ||
        (subscriptionPrices.quarterly !== undefined && (isNaN(subscriptionPrices.quarterly) || subscriptionPrices.quarterly < 0)) ||
        (subscriptionPrices.yearly !== undefined && (isNaN(subscriptionPrices.yearly) || subscriptionPrices.yearly < 0))
      ) {
        return res.status(400).json({
          success: false,
          message: '订阅价格必须是非负数'
        });
      }
    }

    // 支付设置验证
    if (reqPaymentSettings) {
      if (reqPaymentSettings.failureLimit !== undefined && (isNaN(reqPaymentSettings.failureLimit) || reqPaymentSettings.failureLimit < 1)) {
        return res.status(400).json({
          success: false,
          message: '支付失败限制次数必须是正整数'
        });
      }

      if (reqPaymentSettings.failureLockTime !== undefined && (isNaN(reqPaymentSettings.failureLockTime) || reqPaymentSettings.failureLockTime < 1)) {
        return res.status(400).json({
          success: false,
          message: '支付失败锁定时间必须是正整数'
        });
      }
    }

    // 安全设置验证
    if (securitySettings) {
      if (securitySettings.binanceApiTimeout !== undefined && (isNaN(securitySettings.binanceApiTimeout) || securitySettings.binanceApiTimeout < 5000)) {
        return res.status(400).json({
          success: false,
          message: '币安API超时时间必须大于5000毫秒'
        });
      }

      if (securitySettings.binanceApiRetryTimes !== undefined && (isNaN(securitySettings.binanceApiRetryTimes) || securitySettings.binanceApiRetryTimes < 1)) {
        return res.status(400).json({
          success: false,
          message: '币安API重试次数必须是正整数'
        });
      }

      if (securitySettings.loginFailLimit !== undefined && (isNaN(securitySettings.loginFailLimit) || securitySettings.loginFailLimit < 1)) {
        return res.status(400).json({
          success: false,
          message: '登录失败限制次数必须是正整数'
        });
      }

      if (securitySettings.loginLockTime !== undefined && (isNaN(securitySettings.loginLockTime) || securitySettings.loginLockTime < 0.5)) {
        return res.status(400).json({
          success: false,
          message: '登录失败锁定时间必须大于0.5小时'
        });
      }
    }

    // 验证翻译设置
    if (reqTranslationSettings) {
      if (reqTranslationSettings.preferredService && !['deepl', 'google'].includes(reqTranslationSettings.preferredService)) {
        return res.status(400).json({
          success: false,
          message: '首选翻译服务必须是 deepl 或 google'
        });
      }
    }

    // 验证邮件设置
    if (reqEmailSettings) {
      if (reqEmailSettings.provider && !['resend', 'brevo', 'mailersend'].includes(reqEmailSettings.provider)) {
        return res.status(400).json({
          success: false,
          message: '邮件服务商必须是 resend、brevo 或 mailersend'
        });
      }

      if (reqEmailSettings.fallbackProvider && !['resend', 'brevo', 'mailersend'].includes(reqEmailSettings.fallbackProvider)) {
        return res.status(400).json({
          success: false,
          message: '备用邮件服务商必须是 resend、brevo 或 mailersend'
        });
      }

      if (reqEmailSettings.retryTimes !== undefined && (isNaN(reqEmailSettings.retryTimes) || reqEmailSettings.retryTimes < 1 || reqEmailSettings.retryTimes > 10)) {
        return res.status(400).json({
          success: false,
          message: '重试次数必须在1-10之间'
        });
      }

      if (reqEmailSettings.timeout !== undefined && (isNaN(reqEmailSettings.timeout) || reqEmailSettings.timeout < 5000 || reqEmailSettings.timeout > 120000)) {
        return res.status(400).json({
          success: false,
          message: '超时时间必须在5000-120000毫秒之间'
        });
      }

      if (reqEmailSettings.fromEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(reqEmailSettings.fromEmail)) {
        return res.status(400).json({
          success: false,
          message: '发送邮箱格式不正确'
        });
      }
    }

    // 处理邮件设置（保留现有密钥）
    let emailSettings = reqEmailSettings;
    if (reqEmailSettings) {
      const currentSettings = await systemSettingsService.getSystemSettings();

      // 处理 Resend API 密钥
      if (reqEmailSettings.resend && (!reqEmailSettings.resend.apiKey || reqEmailSettings.resend.apiKey === '***已配置***')) {
        emailSettings = {
          ...emailSettings,
          resend: {
            ...emailSettings.resend,
            apiKey: currentSettings.emailSettings.resend.apiKey
          }
        };
      }

      // 处理 Brevo API 密钥
      if (reqEmailSettings.brevo && (!reqEmailSettings.brevo.apiKey || reqEmailSettings.brevo.apiKey === '***已配置***')) {
        emailSettings = {
          ...emailSettings,
          brevo: {
            ...emailSettings.brevo,
            apiKey: currentSettings.emailSettings.brevo.apiKey
          }
        };
      }

      // 处理 MailerSend API 密钥
      if (reqEmailSettings.mailersend && (!reqEmailSettings.mailersend.apiKey || reqEmailSettings.mailersend.apiKey === '***已配置***')) {
        emailSettings = {
          ...emailSettings,
          mailersend: {
            ...emailSettings.mailersend,
            apiKey: currentSettings.emailSettings.mailersend.apiKey
          }
        };
      }
    }

    // 构建更新对象
    const updateData: any = {};
    if (siteInfo) updateData.siteInfo = siteInfo;
    if (userSettings) updateData.userSettings = userSettings;
    if (subscriptionPrices) updateData.subscriptionPrices = subscriptionPrices;
    if (reqPaymentSettings) updateData.paymentSettings = reqPaymentSettings;
    if (securitySettings) updateData.securitySettings = securitySettings;
    if (reqTranslationSettings) updateData.translationSettings = reqTranslationSettings;
    if (emailSettings) updateData.emailSettings = emailSettings;

    // 更新设置
    const userId = req.user?._id;
    const updatedSettings = await systemSettingsService.updateSystemSettings(updateData, userId?.toString());

    // 强制清理所有相关缓存
    const configService = require('../services/configService').default;
    configService.clearCache();
    console.log('系统设置更新完成，缓存已清理');

    // 处理敏感信息脱敏
    let paymentSettings = null;
    if (updatedSettings.paymentSettings) {
      paymentSettings = {
        ...updatedSettings.paymentSettings,
        // 对API密钥进行脱敏处理
        apiKey: updatedSettings.paymentSettings.apiKey ?
          `${updatedSettings.paymentSettings.apiKey.substring(0, 4)}****${updatedSettings.paymentSettings.apiKey.slice(-4)}` : '',
        apiSecretKey: updatedSettings.paymentSettings.apiSecretKey ?
          `${updatedSettings.paymentSettings.apiSecretKey.substring(0, 4)}****${updatedSettings.paymentSettings.apiSecretKey.slice(-4)}` : ''
      };
    }

    // 处理翻译设置脱敏
    let translationSettings = null;
    if (updatedSettings.translationSettings) {
      translationSettings = {
        ...updatedSettings.translationSettings,
        // 对API密钥进行脱敏处理
        deeplApiKey: updatedSettings.translationSettings.deeplApiKey ?
          `${updatedSettings.translationSettings.deeplApiKey.substring(0, 4)}****${updatedSettings.translationSettings.deeplApiKey.slice(-4)}` : '',
        googleApiKey: updatedSettings.translationSettings.googleApiKey ?
          `${updatedSettings.translationSettings.googleApiKey.substring(0, 4)}****${updatedSettings.translationSettings.googleApiKey.slice(-4)}` : ''
      };
    }

    // 处理邮件设置脱敏
    let emailSettingsResponse = null;
    if (updatedSettings.emailSettings) {
      emailSettingsResponse = {
        ...updatedSettings.emailSettings,
        resend: {
          ...updatedSettings.emailSettings.resend,
          apiKey: updatedSettings.emailSettings.resend.apiKey ? '***已配置***' : ''
        },
        brevo: {
          ...updatedSettings.emailSettings.brevo,
          apiKey: updatedSettings.emailSettings.brevo.apiKey ? '***已配置***' : ''
        },
        mailersend: {
          ...updatedSettings.emailSettings.mailersend,
          apiKey: updatedSettings.emailSettings.mailersend.apiKey ? '***已配置***' : ''
        }
      };
    }

    res.json({
      success: true,
      settings: {
        siteInfo: updatedSettings.siteInfo,
        userSettings: updatedSettings.userSettings,
        subscriptionPrices: updatedSettings.subscriptionPrices,
        paymentSettings: paymentSettings,
        securitySettings: updatedSettings.securitySettings,
        translationSettings: translationSettings,
        emailSettings: emailSettingsResponse,
        updatedAt: updatedSettings.updatedAt
      }
    });
  } catch (error) {
    console.error('更新系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};







/**
 * 上传文档图片
 * POST /api/admin/docs/upload-image
 */
export const uploadDocumentImage = async (req: Request, res: Response) => {
  // 使用multer中间件处理单个文件上传
  const uploadSingle = upload.single('image');

  uploadSingle(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未提供文件'
      });
    }

    try {
      // 保存文件并获取URL
      const imageUrl = await systemSettingsService.saveImageFile(
        req.file.buffer,
        req.file.originalname,
        'document'
      );

      res.json({
        success: true,
        imageUrl,
        message: '图片上传成功'
      });
    } catch (error) {
      console.error('上传文档图片错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器错误，请稍后重试'
      });
    }
  });
};

/**
 * 测试邮件服务连接
 * POST /api/admin/settings/test-email-connection
 */
export const testEmailConnection = async (req: Request, res: Response) => {
  try {
    const { provider, apiKey } = req.body;

    if (!provider || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '请提供邮件服务商和API密钥'
      });
    }

    if (!['resend', 'brevo', 'mailersend'].includes(provider)) {
      return res.status(400).json({
        success: false,
        message: '不支持的邮件服务商'
      });
    }

    let testResult = false;
    let errorMessage = '';

    switch (provider) {
        case 'resend':
          // 测试 Resend API 连接
          try {
            const resendResponse = await axios.get('https://api.resend.com/domains', {
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              },
              timeout: 10000
            });
            testResult = resendResponse.status === 200;
          } catch (error: any) {
            testResult = false;
            if (error.response) {
              errorMessage = `Resend API 错误: ${error.response.status} ${error.response.data}`;
            } else {
              errorMessage = `Resend 连接错误: ${error.message}`;
            }
          }
          break;

        case 'brevo':
          // 测试 Brevo API 连接
          try {
            const brevoResponse = await axios.get('https://api.brevo.com/v3/account', {
              headers: {
                'api-key': apiKey,
                'Content-Type': 'application/json'
              },
              timeout: 10000
            });
            testResult = brevoResponse.status === 200;
          } catch (error: any) {
            testResult = false;
            if (error.response) {
              errorMessage = `Brevo API 错误: ${error.response.status} ${error.response.data}`;
            } else {
              errorMessage = `Brevo 连接错误: ${error.message}`;
            }
          }
          break;

        case 'mailersend':
          // 测试 MailerSend API 连接
          try {
            const mailersendResponse = await axios.get('https://api.mailersend.com/v1/me', {
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              },
              timeout: 10000
            });
            testResult = mailersendResponse.status === 200;
          } catch (error: any) {
            testResult = false;
            if (error.response) {
              errorMessage = `MailerSend API 错误: ${error.response.status} ${error.response.data}`;
            } else {
              errorMessage = `MailerSend 连接错误: ${error.message}`;
            }
          }
          break;
    }

    res.json({
      success: testResult,
      message: testResult ? '连接测试成功' : errorMessage,
      provider: provider
    });

  } catch (error) {
    console.error('测试邮件连接错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getSystemSettings,
  updateSystemSettings,
  uploadDocumentImage,
  testEmailConnection
};