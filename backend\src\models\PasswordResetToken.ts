import mongoose, { Document, Schema } from 'mongoose';

export interface IPasswordResetToken extends Document {
  userId: mongoose.Types.ObjectId;
  token: string;
  isUsed: boolean;
  createdAt: Date;
  expiresAt: Date;
  usedAt?: Date;
}

export interface IPasswordResetTokenModel extends mongoose.Model<IPasswordResetToken> {
  createResetToken(userId: mongoose.Types.ObjectId, token: string, expiresAt: Date): Promise<IPasswordResetToken>;
  validateAndUseToken(token: string): Promise<IPasswordResetToken | null>;
}

const passwordResetTokenSchema = new Schema<IPasswordResetToken>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  token: {
    type: String,
    required: true,
    unique: true
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    required: true
  },
  usedAt: {
    type: Date
  }
});

// 创建索引
// token字段已通过unique: true自动创建唯一索引，无需重复创建
passwordResetTokenSchema.index({ userId: 1 });
passwordResetTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// 静态方法：创建新的重置token
passwordResetTokenSchema.statics.createResetToken = async function(
  userId: mongoose.Types.ObjectId,
  token: string,
  expiresAt: Date
) {
  // 先将该用户的所有未使用token标记为已使用
  await this.updateMany(
    { userId, isUsed: false },
    { isUsed: true, usedAt: new Date() }
  );

  // 创建新的token记录
  return this.create({
    userId,
    token,
    expiresAt,
    isUsed: false
  });
};

// 静态方法：验证并使用token
passwordResetTokenSchema.statics.validateAndUseToken = async function(token: string) {
  const tokenRecord = await this.findOne({
    token,
    isUsed: false,
    expiresAt: { $gt: new Date() }
  });

  if (!tokenRecord) {
    return null;
  }

  // 标记token为已使用
  tokenRecord.isUsed = true;
  tokenRecord.usedAt = new Date();
  await tokenRecord.save();

  return tokenRecord;
};

const PasswordResetToken = mongoose.model<IPasswordResetToken, IPasswordResetTokenModel>('PasswordResetToken', passwordResetTokenSchema);

export default PasswordResetToken;
