import mongoose from 'mongoose';
import SystemSettings, { SystemSettingsDocument } from '../models/SystemSettings';

/**
 * 配置服务
 * 提供系统配置的获取、缓存和更新功能
 */
class ConfigService {
  private static instance: ConfigService;
  private settings: SystemSettingsDocument | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 缓存有效期：5分钟

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取ConfigService单例
   */
  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * 初始化配置服务
   * 预加载配置到缓存中
   */
  public async init(): Promise<void> {
    try {
      await this.loadSettings();
      console.log('配置服务初始化成功');
    } catch (error) {
      console.error('配置服务初始化失败:', error);
    }
  }

  /**
   * 加载系统设置
   * 从数据库获取最新设置并更新缓存
   */
  private async loadSettings(): Promise<SystemSettingsDocument> {
    try {
      const settings = await SystemSettings.getSettings();
      this.settings = settings;
      this.lastFetchTime = Date.now();
      return settings;
    } catch (error) {
      console.error('加载系统设置失败:', error);
      throw error;
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return (
      this.settings !== null &&
      Date.now() - this.lastFetchTime < this.CACHE_TTL
    );
  }

  /**
   * 获取系统设置
   * 优先从缓存获取，缓存失效则从数据库重新加载
   */
  public async getSettings(): Promise<SystemSettingsDocument> {
    if (!this.isCacheValid()) {
      return this.loadSettings();
    }
    return this.settings as SystemSettingsDocument;
  }

  /**
   * 清除缓存
   * 在更新设置后调用，确保下次获取时从数据库加载最新设置
   */
  public clearCache(): void {
    this.settings = null;
    this.lastFetchTime = 0;
  }

  /**
   * 获取指定配置项的值
   * @param key 配置项路径，支持点号分隔的嵌套路径，如 'siteInfo.siteName'
   * @param defaultValue 默认值，当配置项不存在时返回
   */
  public async get<T>(key: string, defaultValue?: T): Promise<T> {
    const settings = await this.getSettings();

    // 处理嵌套路径
    const parts = key.split('.');
    let value: any = settings;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return defaultValue as T;
      }
    }

    return (value as T) ?? defaultValue as T;
  }

  /**
   * 更新系统设置
   * @param settings 新的系统设置
   * @param userId 更新人ID
   */
  public async updateSettings(
    settings: Partial<SystemSettingsDocument>,
    userId?: string
  ): Promise<SystemSettingsDocument> {
    try {
      // 获取当前设置
      const currentSettings = await this.getSettings();

      // 更新设置
      Object.assign(currentSettings, settings);

      // 更新元数据
      currentSettings.updatedAt = new Date();
      if (userId) {
        currentSettings.updatedBy = new mongoose.Types.ObjectId(userId);
      }

      // 保存到数据库
      await currentSettings.save();

      // 清除缓存
      this.clearCache();

      return currentSettings;
    } catch (error) {
      console.error('更新系统设置失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export default ConfigService.getInstance();
