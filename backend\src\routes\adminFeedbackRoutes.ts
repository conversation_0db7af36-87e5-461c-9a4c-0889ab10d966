import express from 'express';
import feedbackController from '../controllers/feedbackController';

const router = express.Router();

// 获取所有反馈列表
router.get('/', feedbackController.getAllFeedbacks);

// 获取反馈详情（管理员专用）
router.get('/:id', feedbackController.getAdminFeedbackDetail);

// 回复反馈
router.post('/:id/reply', feedbackController.replyFeedback);

// 更新反馈状态
router.patch('/:id/status', feedbackController.updateFeedbackStatus);

export default router;
