import { Request, Response } from 'express';
import solarTermPredictionService from '../services/solarTermPredictionService';
import logger from '../utils/logger';

/**
 * 节气预测折线控制器
 * 处理与节气预测折线相关的请求
 */
class SolarTermPredictionController {
  /**
   * 获取预测折线数据
   * 支持基于预测点数量的分页加载
   *
   * @param req Express请求对象
   * @param res Express响应对象
   */
  async getRecentPredictions(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user; // 从可选认证中间件获取用户信息

      // 检查用户权限 - 只有认证用户且非普通用户才能获取预测折线数据
      // 如果用户未认证或权限不足，返回空数组但记录日志
      if (!user) {
        res.status(200).json([]);
        return;
      }

      if (user.role === 'normal') {
        res.status(200).json([]);
        return;
      }

      const symbol = req.query.symbol as string || 'BTCUSDT';
      const endTime = req.query.endTime ? parseInt(req.query.endTime as string) : undefined;
      const limit = req.query.limit ?
        Math.min(2000, Math.max(1, parseInt(req.query.limit as string))) : 480;



      // 调用服务层获取预测数据
      const predictions = await solarTermPredictionService.getPaginatedPredictions(symbol, endTime, limit);


      res.status(200).json(predictions);
    } catch (error) {
      console.error('获取预测折线数据失败:', error);
      res.status(500).json({ message: '获取预测折线数据失败', error: (error as Error).message });
    }
  }
  
  // 注意：以下方法已删除，因为前端未使用且已通过定时任务自动化：
  // - triggerPrediction: 手动触发节气预测折线生成
  // - checkPredictionIntegrity: 触发预测折线完整性检查
}

export default new SolarTermPredictionController(); 