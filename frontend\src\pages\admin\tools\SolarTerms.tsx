import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import { <PERSON><PERSON>, <PERSON> } from '../../../components/admin/ui';
import { useToast } from '../../../components/ui/use-toast';
import { FileText, Edit, Save, X, Play, RefreshCw, AlertCircle, Copy } from 'lucide-react';
import { adminApiInstance } from '../../../api/admin';

interface ScriptExecutionLog {
  timestamp: string;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
}

const SolarTerms: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [fileContent, setFileContent] = useState<string>('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [updateYears, setUpdateYears] = useState<number>(2);
  const [executionLogs, setExecutionLogs] = useState<ScriptExecutionLog[]>([]);
  const [showLogs, setShowLogs] = useState(false);

  // 获取节气文件内容
  const fetchFileContent = async () => {
    try {
      setIsLoading(true);
      const response = await adminApiInstance.get('/admin/tools/solar-terms');
      
      if (response.data.fileContent) {
        setFileContent(response.data.fileContent);
        setEditedContent(response.data.fileContent);
      }
      
      setIsLoading(false);
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '获取文件内容失败';
      toast({
        title: '获取文件内容失败',
        description: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 添加执行日志
  const addLog = (message: string, type: ScriptExecutionLog['type'] = 'info') => {
    const log: ScriptExecutionLog = {
      timestamp: new Date().toLocaleString(),
      message,
      type
    };
    setExecutionLogs(prev => [log, ...prev.slice(0, 49)]);
  };

  // 执行节气数据更新脚本
  const updateSolarTerms = async () => {
    try {
      setIsUpdating(true);
      setShowLogs(true);
      
      addLog(`开始执行节气数据更新脚本，获取未来 ${updateYears} 年数据...`, 'info');
      
      const response = await adminApiInstance.post('/admin/tools/solar-terms/update', {
        years: updateYears
      });

      addLog('节气数据更新脚本执行成功', 'success');
      
      if (response.data.output) {
        const outputLines = response.data.output.split('\n').filter((line: string) => line.trim());
        outputLines.forEach((line: string) => {
          if (line.includes('✅') || line.includes('成功')) {
            addLog(line, 'success');
          } else if (line.includes('❌') || line.includes('失败')) {
            addLog(line, 'error');
          } else if (line.includes('⚠️') || line.includes('警告')) {
            addLog(line, 'warning');
          } else {
            addLog(line, 'info');
          }
        });
      }

      toast({
        title: '节气数据更新成功',
        description: `已更新未来 ${updateYears} 年的节气数据`,
        variant: 'default'
      });

      // 重新获取文件内容
      await fetchFileContent();
      setIsUpdating(false);
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '更新节气数据失败';
      addLog(`脚本执行失败: ${message}`, 'error');
      
      toast({
        title: '更新节气数据失败',
        description: message,
        variant: 'destructive'
      });
      setIsUpdating(false);
    }
  };

  // 保存编辑的文件内容
  const saveFileContent = async () => {
    try {
      await adminApiInstance.put('/admin/tools/solar-terms/file', {
        content: editedContent
      });

      toast({
        title: '文件保存成功',
        description: '节气数据文件已成功保存',
        variant: 'default'
      });

      setFileContent(editedContent);
      setIsEditing(false);
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '保存文件失败';
      toast({
        title: '保存文件失败',
        description: message,
        variant: 'destructive'
      });
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditedContent(fileContent);
    setIsEditing(false);
  };

  // 复制文件内容
  const copyContent = () => {
    navigator.clipboard.writeText(fileContent);
    toast({
      title: '复制成功',
      description: '文件内容已复制到剪贴板',
      variant: 'default'
    });
  };

  useEffect(() => {
    fetchFileContent();
  }, []);

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">节气管理</h1>
          <p className="text-gray-600 mt-1">管理节气数据文件和自动更新脚本</p>
        </div>

        <div className="space-y-6">
          {/* 脚本执行控制 */}
          <Card>
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Play className="h-5 w-5" />
                节气数据自动更新
              </h2>
              
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                  <label className="text-sm text-gray-600">获取年数:</label>
                  <select
                    value={updateYears}
                    onChange={(e) => setUpdateYears(Number(e.target.value))}
                    className="px-3 py-1 border border-gray-300 rounded text-sm"
                    disabled={isUpdating}
                  >
                    <option value={1}>1年</option>
                    <option value={2}>2年</option>
                    <option value={3}>3年</option>
                    <option value={4}>4年</option>
                    <option value={5}>5年</option>
                  </select>
                </div>
                
                <Button
                  onClick={updateSolarTerms}
                  disabled={isUpdating}
                  loading={isUpdating}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  {isUpdating ? '执行中...' : '执行脚本'}
                </Button>
                
                <Button
                  onClick={() => setShowLogs(!showLogs)}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <AlertCircle className="h-4 w-4" />
                  {showLogs ? '隐藏日志' : '显示日志'}
                </Button>
              </div>
            </div>
          </Card>

          {/* 执行日志 */}
          {showLogs && (
            <Card>
              <div className="mb-4">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  执行日志
                  <span className="text-sm font-normal text-gray-500">({executionLogs.length}条)</span>
                </h2>
              </div>
              
              <div className="max-h-64 overflow-y-auto border rounded-lg bg-gray-50">
                {executionLogs.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    暂无执行日志
                  </div>
                ) : (
                  <div className="p-2 space-y-1">
                    {executionLogs.map((log, index) => (
                      <div
                        key={index}
                        className={`text-xs p-2 rounded font-mono ${
                          log.type === 'success' ? 'bg-green-100 text-green-800' :
                          log.type === 'error' ? 'bg-red-100 text-red-800' :
                          log.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-700'
                        }`}
                      >
                        <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          )}

          {/* 文件内容查看和编辑 */}
          <Card>
            <div className="mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                solarTermData.json 文件内容
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                查看和编辑节气数据常量文件
              </p>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-10">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
              </div>
            ) : isEditing ? (
              /* 编辑模式 */
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-md font-medium text-gray-900">编辑文件内容</h3>
                  <div className="flex gap-2">
                    <Button
                      onClick={saveFileContent}
                      className="flex items-center gap-2"
                      size="sm"
                    >
                      <Save className="h-4 w-4" />
                      保存
                    </Button>
                    <Button
                      onClick={cancelEdit}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <X className="h-4 w-4" />
                      取消
                    </Button>
                  </div>
                </div>
                
                <textarea
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  className="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm"
                  placeholder="编辑文件内容..."
                />
              </div>
            ) : (
              /* 查看模式 */
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-md font-medium text-gray-900">文件内容</h3>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => setIsEditing(true)}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      编辑
                    </Button>
                    <Button
                      onClick={copyContent}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Copy className="h-4 w-4" />
                      复制
                    </Button>
                    <Button
                      onClick={fetchFileContent}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      disabled={isLoading}
                    >
                      <RefreshCw className="h-4 w-4" />
                      刷新
                    </Button>
                  </div>
                </div>
                
                <div className="border rounded-lg bg-gray-50 p-4 max-h-96 overflow-auto">
                  <pre className="text-xs font-mono text-gray-800 whitespace-pre-wrap">
                    {fileContent || '文件内容加载中...'}
                  </pre>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

export default SolarTerms;
