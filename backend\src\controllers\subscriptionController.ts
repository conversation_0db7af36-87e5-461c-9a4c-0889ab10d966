import { Request, Response } from 'express';
import User from '../models/User';
import Payment from '../models/Payment';
import nowPaymentsService from '../services/nowPaymentsService';
import configService from '../services/configService';
import notificationService from '../services/notificationService';

// 获取当前站点URL
const getSiteUrl = (req: Request): string => {
  const protocol = req.get('x-forwarded-proto') || req.protocol;
  const host = req.get('host');
  return `${protocol}://${host}`;
};

// 创建订阅支付
export const createSubscription = async (req: Request, res: Response) => {
  try {
    const { plan } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ message: '未经授权' });
    }

    if (!['monthly', 'quarterly', 'yearly'].includes(plan)) {
      return res.status(400).json({ message: '无效的订阅计划' });
    }

    // 获取用户信息，用于提取邮箱
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: '用户未找到' });
    }
    const userEmail = user.email; // 获取用户邮箱

    // 从配置服务获取支付失败限制次数和锁定时间
    const failureLimit = await configService.get('paymentSettings.failureLimit', 5);
    const failureLockTime = await configService.get('paymentSettings.failureLockTime', 24);

    // 检查用户是否被锁定支付
    const existingPayment = await Payment.findOne({
      userId,
      lockedUntil: { $gt: new Date() }
    });

    if (existingPayment) {
      // 计算剩余锁定时间（分钟）
      const remainingMinutes = Math.ceil((existingPayment.lockedUntil!.getTime() - Date.now()) / (60 * 1000));

      return res.status(403).json({
        message: `由于多次支付失败，您的支付功能已被锁定，请在 ${remainingMinutes} 分钟后重试`
      });
    }

    const siteUrl = getSiteUrl(req);

    // 从配置服务获取回调URL，如果未配置则使用默认值
    let callbackUrl = await configService.get('paymentSettings.callbackUrl', '');
    if (!callbackUrl) {
      callbackUrl = `${siteUrl}/api/subscription/webhook`;
    }

    // 从配置服务获取成功和取消URL，如果未配置则使用默认值
    let successUrl = await configService.get('paymentSettings.successUrl', '');
    let cancelUrl = await configService.get('paymentSettings.cancelUrl', '');

    // 如果未配置成功和取消URL，则使用默认值
    if (!successUrl || !cancelUrl) {
      // 修改成功和取消URL，使其指向前端端口3000
      const frontendUrl = `http://${req.get('host')?.replace('4000', '3000') || 'localhost:3000'}`;

      if (!successUrl) {
        successUrl = `${frontendUrl}/subscription/success`;
      }

      if (!cancelUrl) {
        cancelUrl = `${frontendUrl}/subscription/cancel`;
      }
    }

    // 创建支付
    const paymentResponse = await nowPaymentsService.createPayment(
      userId.toString(),
      plan,
      callbackUrl,
      successUrl,
      cancelUrl
    );

    // 确保payment_id存在
    if (!paymentResponse.payment_id) {
      return res.status(500).json({
        message: '支付创建失败：无法获取支付ID',
        debug: process.env.NODE_ENV === 'development' ? paymentResponse : undefined
      });
    }

    // 从invoice_url中提取invoice ID (如果有)
    let invoiceId = '';
    try {
      const urlParts = paymentResponse.invoice_url.split('/');
      invoiceId = urlParts[urlParts.length - 1] || paymentResponse.payment_id;
    } catch (e) {
      // 如果无法从URL提取，就使用payment_id
      invoiceId = paymentResponse.payment_id;
    }

    // 创建支付记录
    const payment = new Payment({
      userId,
      userEmail, // 添加用户邮箱
      amount: paymentResponse.price_amount,
      currency: paymentResponse.price_currency,
      plan,
      paymentId: paymentResponse.payment_id, // 保证存在
      invoiceId: invoiceId,
      orderId: paymentResponse.order_id,    // 添加订单ID字段
      paymentAddress: paymentResponse.pay_address || '',
      paymentStatus: mapPaymentStatus(paymentResponse.status || 'pending'),
    });

    await payment.save();

    return res.status(200).json({
      success: true,
      payment: {
        id: payment._id,
        paymentId: payment.paymentId,
        invoiceUrl: paymentResponse.invoice_url,
        status: payment.paymentStatus,
        expiresAt: paymentResponse.expiration_at || null
      }
    });
  } catch (error) {
    console.error('创建订阅出错:', error);
    return res.status(500).json({ message: error instanceof Error ? error.message : '创建订阅失败' });
  }
};

export const handlePaymentWebhook = async (req: Request, res: Response) => {
  try {
    console.log('收到回调数据:', req.body);

    const {
      payment_id,
      payment_status,
      order_id,
      transaction_id
    } = req.body;

    let payment = await Payment.findOne({ paymentId: payment_id });
    if (!payment && order_id) {
      payment = await Payment.findOne({ orderId: order_id });
    }

    if (!payment) {
      console.warn('找不到支付记录:', payment_id || order_id);
      return res.status(404).json({ message: '支付记录未找到' });
    }

    const mappedStatus = mapPaymentStatus(payment_status);
    console.log(`原始状态: ${payment_status}, 映射后: ${mappedStatus}`);

    payment.paymentStatus = mappedStatus;
    payment.txId = transaction_id || payment.txId;

    // 处理支付失败情况
    if (['failed', 'expired', 'refunded'].includes(mappedStatus)) {
      // 从配置服务获取支付失败限制次数和锁定时间
      const failureLimit = await configService.get('paymentSettings.failureLimit', 5);
      const failureLockTime = await configService.get('paymentSettings.failureLockTime', 24);

      // 增加失败计数
      payment.failureCount = (payment.failureCount || 0) + 1;

      console.log(`用户 ${payment.userId} 支付失败计数: ${payment.failureCount}/${failureLimit}`);

      // 如果达到失败限制次数，锁定支付
      if (payment.failureCount >= failureLimit) {
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + failureLockTime); // 锁定指定小时
        payment.lockedUntil = lockUntil;

        console.log(`用户 ${payment.userId} 支付已被锁定至 ${lockUntil}`);
      }
    } else if (['confirmed', 'finished'].includes(mappedStatus)) {
      // 支付成功，重置失败计数和锁定时间
      payment.failureCount = 0;
      payment.lockedUntil = undefined;
    }

    await payment.save();

    const isCompleted = ['confirmed', 'finished'].includes(payment_status);

    if (isCompleted) {
      const user = await User.findById(payment.userId);
      if (!user) {
        console.warn('找不到用户:', payment.userId);
        return res.status(404).json({ message: '用户未找到' });
      }

      // 避免重复写入
      if (user.subscription?.status !== 'active') {
        const duration = nowPaymentsService.getPlanDuration(payment.plan);
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + duration);

        user.role = 'subscriber';
        user.subscription = {
          plan: payment.plan as any,
          startDate,
          endDate,
          status: 'active',
          paymentId: payment.paymentId
        };

        await user.save();
        console.log(`✅ 用户 ${user._id} 订阅更新成功`);

        // 创建订阅成功通知
        try {
          await notificationService.createSubscriptionSuccessNotification(
            user._id,
            payment.plan,
            endDate
          );
        } catch (error) {
          console.error('创建订阅成功通知失败:', error);
          // 不影响订阅流程，仅记录错误
        }
      } else {
        console.log(`用户 ${user._id} 已订阅，跳过更新`);
      }
    }

    return res.status(200).json({ message: 'OK' });
  } catch (error) {
    console.error('处理支付回调出错:', error);
    return res.status(500).json({ message: '处理支付回调失败' });
  }
};
// 获取用户订阅信息
export const getUserSubscription = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ message: '未经授权' });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: '用户未找到' });
    }

    // 检查是否在试用期内
    const isInTrialPeriod = user.role === 'trial' && new Date(user.trialEndsAt) > new Date();

    // 检查是否订阅活跃
    const isSubscriptionActive = user.role === 'subscriber' &&
                              user.subscription?.status === 'active' &&
                              new Date(user.subscription?.endDate) > new Date();

    // 计算剩余天数
    let daysRemaining = 0;

    if (isInTrialPeriod) {
      daysRemaining = Math.ceil((new Date(user.trialEndsAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    } else if (isSubscriptionActive && user.subscription) {
      daysRemaining = Math.ceil((new Date(user.subscription.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    }

    return res.status(200).json({
      success: true,
      subscription: {
        status: isSubscriptionActive ? 'active' : (isInTrialPeriod ? 'trial' : 'inactive'),
        type: isSubscriptionActive ? user.subscription?.plan : (isInTrialPeriod ? 'trial' : null),
        daysRemaining,
        endDate: isSubscriptionActive ? user.subscription?.endDate : (isInTrialPeriod ? user.trialEndsAt : null)
      }
    });
  } catch (error) {
    console.error('获取订阅信息出错:', error);
    return res.status(500).json({ message: '获取订阅信息失败' });
  }
};

// 获取用户支付历史
export const getPaymentHistory = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ message: '未经授权' });
    }

    const payments = await Payment.find({ userId }).sort({ createdAt: -1 });

    return res.status(200).json({
      success: true,
      payments: payments.map(payment => ({
        id: payment._id,
        amount: payment.amount,
        currency: payment.currency,
        plan: payment.plan,
        status: payment.paymentStatus,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt
      }))
    });
  } catch (error) {
    console.error('获取支付历史出错:', error);
    return res.status(500).json({ message: '获取支付历史失败' });
  }
};

// 映射NOWPayments API状态到Payment模型状态
const mapPaymentStatus = (nowPaymentsStatus: string): 'pending' | 'confirming' | 'confirmed' | 'sending' | 'partially_paid' | 'finished' | 'failed' | 'refunded' | 'expired' => {
  // 映射规则
  const statusMap: {[key: string]: 'pending' | 'confirming' | 'confirmed' | 'sending' | 'partially_paid' | 'finished' | 'failed' | 'refunded' | 'expired'} = {
    'created': 'pending',  // 创建状态映射为待处理
    'waiting': 'pending',  // 等待支付映射为待处理
    // 其他状态保持原样，如果它们已经匹配
    'pending': 'pending',
    'confirming': 'confirming',
    'confirmed': 'confirmed',
    'sending': 'sending',
    'partially_paid': 'partially_paid',
    'finished': 'finished',
    'failed': 'failed',
    'refunded': 'refunded',
    'expired': 'expired'
  };

  // 返回映射后的状态，如果没有映射规则则默认为'pending'
  return statusMap[nowPaymentsStatus] || 'pending';
};

// 处理支付成功/取消后直接访问后端URL的重定向
export const handlePaymentRedirect = (req: Request, res: Response) => {
  // 获取前端URL
  const frontendUrl = `http://${req.get('host')?.replace('4000', '3000') || 'localhost:3000'}`;

  // 提取路径和查询参数
  const path = req.path; // 例如 /subscription/success
  const queryString = req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : '';

  // 构建完整的重定向URL
  const redirectUrl = `${frontendUrl}${path}${queryString}`;

  console.log(`重定向到前端: ${redirectUrl}`);

  // 将用户重定向到前端
  return res.redirect(redirectUrl);
};

export default {
  createSubscription,
  handlePaymentWebhook,
  getUserSubscription,
  getPaymentHistory,
  handlePaymentRedirect
};