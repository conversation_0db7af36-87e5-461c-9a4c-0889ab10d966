import React, { useState, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import useAdminStore from '../../../store/useAdminStore';
import AdminLayout from '../../../components/admin/AdminLayout';
import { Button, Input, Card, Select } from '../../../components/admin/ui';

const BasicSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    siteInfo: {
      siteName: 'BTC 预测',
      siteDescription: '',
      siteKeywords: '',
      copyright: '',
      socialLinks: {
        twitter: '',
        telegram: '',
        facebook: '',
        instagram: '',
        github: ''
      },
      defaultLanguage: 'zh' as 'zh' | 'en'
    },
    translationSettings: {
      deeplApiKey: '',
      googleApiKey: '',
      preferredService: 'deepl' as 'deepl' | 'google',
      enableTranslation: false
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();
      setFormData({
        siteInfo: response.settings.siteInfo,
        translationSettings: response.settings.translationSettings || {
          deeplApiKey: '',
          googleApiKey: '',
          preferredService: 'deepl',
          enableTranslation: false
        }
      });
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取系统设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      // 处理嵌套属性 (如 siteInfo.siteName)
      const parts = name.split('.');

      if (parts.length === 2) {
        const [section, field] = parts;
        setFormData(prev => ({
          ...prev,
          [section]: {
            ...prev[section as keyof typeof prev],
            [field]: value
          }
        }));
      } else if (parts.length === 3) {
        // 处理更深层嵌套 (如 siteInfo.socialLinks.twitter)
        const [section, subsection, field] = parts;
        setFormData(prev => {
          const sectionObj = prev[section as keyof typeof prev] as any;
          return {
            ...prev,
            [section]: {
              ...sectionObj,
              [subsection]: {
                ...sectionObj[subsection],
                [field]: value
              }
            }
          };
        });
      }
    } else {
      // 处理普通属性
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        siteInfo: formData.siteInfo,
        translationSettings: formData.translationSettings
      });
      toast({
        title: '基础设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新基础设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };



  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">基础设置</h1>
          <p className="text-gray-600 mt-1">管理网站基本信息和配置</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 左侧表单 */}
                <div className="space-y-4">
                  <div className="mb-4">
                    <Input
                      label="网站名称"
                      id="siteName"
                      name="siteInfo.siteName"
                      value={formData.siteInfo.siteName}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="网站描述"
                      id="siteDescription"
                      name="siteInfo.siteDescription"
                      value={formData.siteInfo.siteDescription}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="网站关键词"
                      id="siteKeywords"
                      name="siteInfo.siteKeywords"
                      value={formData.siteInfo.siteKeywords}
                      onChange={handleChange}
                      placeholder="用逗号分隔关键词"
                    />
                  </div>

                  <div className="mb-4">
                    <Input
                      label="版权信息"
                      id="copyright"
                      name="siteInfo.copyright"
                      value={formData.siteInfo.copyright}
                      onChange={handleChange}
                      placeholder="© 2024 网站名称. 保留所有权利."
                    />
                  </div>

                  <div className="mb-4">
                    <Select
                      label="默认语言"
                      id="defaultLanguage"
                      name="siteInfo.defaultLanguage"
                      value={formData.siteInfo.defaultLanguage}
                      onChange={handleChange}
                      options={[
                        { value: 'zh', label: '中文' },
                        { value: 'en', label: '英文' }
                      ]}
                    />
                  </div>
                </div>

                {/* 右侧表单 */}
                <div className="space-y-6">




                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      社交媒体链接
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <label htmlFor="twitter" className="w-20 text-sm text-gray-600">Twitter</label>
                        <Input
                          id="twitter"
                          name="siteInfo.socialLinks.twitter"
                          value={formData.siteInfo.socialLinks.twitter}
                          onChange={handleChange}
                          placeholder="https://twitter.com/youraccount"
                          className="flex-1"
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <label htmlFor="telegram" className="w-20 text-sm text-gray-600">Telegram</label>
                        <Input
                          id="telegram"
                          name="siteInfo.socialLinks.telegram"
                          value={formData.siteInfo.socialLinks.telegram}
                          onChange={handleChange}
                          placeholder="https://t.me/yourchannel"
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  disabled={isSaving}
                  loading={isSaving}
                >
                  {isSaving ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default BasicSettings;
