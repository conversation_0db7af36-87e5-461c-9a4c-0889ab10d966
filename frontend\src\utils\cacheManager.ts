// 缓存项接口
interface CacheItem {
  data: any;
  timestamp: number;  // 缓存时间戳
  accessTime: number; // 最后访问时间
}

// 缓存配置 - 会话级全缓存策略
// 目标：确保用户在会话期间可以无缝滚动到任何已加载的历史位置，无数据断层
// 适用于：实时K线、预测K线、预测折线三种数据类型
const CACHE_CONFIG = {
  MAX_ITEMS: Infinity,              // 会话期间无限制缓存，保持所有已加载数据
  CACHE_TTL: 24 * 60 * 60 * 1000,  // 24小时会话级缓存（实际上只在页面关闭时清理）
  CLEANUP_THRESHOLD: Infinity,      // 永不触发自动清理，保证数据完整性
  VIEWPORT_BUFFER: Infinity,        // 保留所有数据，不基于视图范围清理
  CLEANUP_RATIO: 0                  // 不自动清理任何数据，只在页面关闭/刷新时自动清理
};

// 缓存管理类
class DataCacheManager {
  private cache = new Map<string, CacheItem>();
  private cacheType: string;
  
  constructor(cacheType: string) {
    this.cacheType = cacheType;
  }
  
  // 添加数据到缓存
  addData(data: any[]): any[] {
    const now = Date.now();
    
    // 为每个数据项添加缓存信息
    const cachedData = data.map(item => {
      const key = this.getItemKey(item);
      const existing = this.cache.get(key);
      
      // 如果已存在且未过期，更新访问时间
      if (existing && (now - existing.timestamp) < CACHE_CONFIG.CACHE_TTL) {
        existing.accessTime = now;
        existing.data = item; // 更新数据
        return existing.data;
      }
      
      // 创建新的缓存项
      const cacheItem: CacheItem = {
        data: item,
        timestamp: now,
        accessTime: now
      };
      
      this.cache.set(key, cacheItem);
      return item;
    });
    
    // 检查是否需要清理
    if (this.cache.size > CACHE_CONFIG.CLEANUP_THRESHOLD) {
      this.cleanup();
    }
    
    return cachedData;
  }
  
  // 获取所有有效数据 - 会话级全缓存版本
  // 缓存生命周期：只在页面关闭/刷新时自动清理，会话期间永久保留
  getAllValidData(): any[] {
    const now = Date.now();
    const validData: any[] = [];

    // 会话级全缓存：返回所有数据，不删除任何缓存项
    // 数据将保留直到：1) 页面刷新 2) 页面关闭 3) 浏览器关闭
    this.cache.forEach((item, key) => {
      // 更新访问时间（用于统计）
      item.accessTime = now;
      validData.push(item.data);
    });

    // 按时间排序
    return this.sortData(validData);
  }
  
  // 智能清理 - 在会话级全缓存策略下，此方法不会被自动调用
  // 只有在手动调用 clearAll() 或页面刷新时才会清理缓存
  private cleanup(currentViewRange?: [number, number]) {
    console.log(`[缓存清理] ${this.cacheType} 开始清理，当前缓存: ${this.cache.size} 条`);

    const now = Date.now();
    const itemsToRemove: string[] = [];

    // 1. 清理过期数据
    this.cache.forEach((item, key) => {
      if ((now - item.timestamp) >= CACHE_CONFIG.CACHE_TTL) {
        itemsToRemove.push(key);
      }
    });

    // 2. 如果还是太多，基于视图范围清理
    if (this.cache.size - itemsToRemove.length > CACHE_CONFIG.MAX_ITEMS && currentViewRange) {
      const [viewStart, viewEnd] = currentViewRange;
      const timeSpan = viewEnd - viewStart;
      const bufferSize = timeSpan * CACHE_CONFIG.VIEWPORT_BUFFER;

      const keepStart = viewStart - bufferSize;
      const keepEnd = viewEnd + bufferSize;

      this.cache.forEach((item, key) => {
        if (!itemsToRemove.includes(key)) {
          const itemTime = this.getItemTime(item.data);
          if (itemTime < keepStart || itemTime > keepEnd) {
            itemsToRemove.push(key);
          }
        }
      });
    }

    // 3. 如果还是太多，清理最久未访问的数据
    if (this.cache.size - itemsToRemove.length > CACHE_CONFIG.MAX_ITEMS) {
      const remainingItems: [string, CacheItem][] = [];
      this.cache.forEach((item, key) => {
        if (!itemsToRemove.includes(key)) {
          remainingItems.push([key, item]);
        }
      });

      // 按访问时间排序
      remainingItems.sort(([, a], [, b]) => a.accessTime - b.accessTime);

      const removeCount = Math.floor(remainingItems.length * CACHE_CONFIG.CLEANUP_RATIO);
      for (let i = 0; i < removeCount; i++) {
        itemsToRemove.push(remainingItems[i][0]);
      }
    }

    // 执行清理
    itemsToRemove.forEach(key => this.cache.delete(key));

    console.log(`[缓存清理] ${this.cacheType} 清理完成，删除 ${itemsToRemove.length} 条，剩余: ${this.cache.size} 条`);
  }
  
  // 获取数据项的键（兼容字符串和数字）
  private getItemKey(item: any): string {
    if (typeof item.time === 'string') {
      return item.time;
    } else if (typeof item.time === 'number') {
      return item.time.toString();
    } else {
      return String(item.time);
    }
  }
  
  // 获取数据项的时间（统一转换为数字）
  private getItemTime(item: any): number {
    if (typeof item.time === 'string') {
      return parseInt(item.time);
    } else if (typeof item.time === 'number') {
      return item.time;
    } else {
      return Number(item.time);
    }
  }
  
  // 排序数据（保持原有逻辑）
  private sortData(data: any[]): any[] {
    return data.sort((a, b) => {
      const timeA = this.getItemTime(a);
      const timeB = this.getItemTime(b);
      return timeA - timeB;
    });
  }
  
  // 获取缓存统计信息
  getCacheStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;

    this.cache.forEach((item) => {
      if ((now - item.timestamp) < CACHE_CONFIG.CACHE_TTL) {
        validCount++;
      } else {
        expiredCount++;
      }
    });

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
      cacheType: this.cacheType
    };
  }
  
  // 强制清理所有缓存
  clearAll() {
    this.cache.clear();
    console.log(`[缓存清理] ${this.cacheType} 强制清理所有缓存`);
  }
}

// 导出缓存管理器工厂
export const createCacheManager = (cacheType: string) => {
  return new DataCacheManager(cacheType);
};

// 获取当前视图范围的工具函数
export const getCurrentViewRange = (chartApi?: any): [number, number] | undefined => {
  if (!chartApi) return undefined;
  
  try {
    const timeScale = chartApi.timeScale();
    const visibleRange = timeScale.getVisibleTimeRange();
    
    if (visibleRange) {
      const start = (visibleRange.from as number) * 1000;
      const end = (visibleRange.to as number) * 1000;
      return [start, end];
    }
  } catch (error) {
    console.warn('获取视图范围失败:', error);
  }
  
  return undefined;
};
