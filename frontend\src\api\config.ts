import axios, { AxiosRequestConfig } from 'axios';
import tokenService from './tokenService';
import i18n from '../i18n';

// 根据环境变量设置API基础URL
export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

// 其他API配置项
export const API_TIMEOUT = 30000; // 请求超时时间（30秒）

// 创建axios实例
const api = axios.create({
  baseURL: API_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true // 确保所有请求都能发送和接收cookie
});

// 请求拦截器，为请求添加token
api.interceptors.request.use(
  (config) => {
    // 定义不需要认证的公共接口
    const publicEndpoints = [
      '/config/user-settings',
      '/public/settings',
      '/config',
      '/health'
    ];

    // 检查是否为公共接口
    const isPublicEndpoint = publicEndpoints.some(endpoint =>
      config.url?.includes(endpoint)
    );

    // 只为非公共接口添加认证头
    if (!isPublicEndpoint) {
      // 优先使用内存中的token
      let token = tokenService.getToken();

      if (token) {
        // 检查token有效性
        if (tokenService.isTokenValid(token)) {
          config.headers.Authorization = `Bearer ${token}`;
        } else {
          console.log('检测到无效token，清除并尝试从localStorage恢复');
          tokenService.setToken(null);
          token = null; // 重置token变量
        }
      }

      if (!token) {
        // 如果内存中没有token或token无效，尝试从localStorage加载
        const hasToken = tokenService.loadTokenFromStorage();
        if (hasToken) {
          const restoredToken = tokenService.getToken();
          if (restoredToken && tokenService.isTokenValid(restoredToken)) {
            config.headers.Authorization = `Bearer ${restoredToken}`;
          } else {
            console.log('从localStorage恢复的token也无效');
          }
        }
      }
    }

    // 添加用户语言偏好到请求头
    if (i18n.language) {
      config.headers['Accept-Language'] = i18n.language === 'en' ? 'en-US,en;q=0.9' : 'zh-CN,zh;q=0.9';
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 用于标记是否已在其他拦截器中处理过该请求
const isRetryRequest = (config: AxiosRequestConfig): boolean => {
  return Boolean(config.headers?._retry);
};

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 确保请求对象存在
    if (!originalRequest) {
      console.error('拦截器错误：请求配置不存在');
      return Promise.reject(error);
    }

    // 根据错误码处理错误
    if (error.response) {
      const errorCode = error.response.data?.code;

      // 401错误且有特定的错误码
      if (error.response.status === 401) {
        // 检查是否是登录请求，如果是登录请求则不尝试刷新令牌
        const isLoginRequest = originalRequest.url === '/auth/login';

        if (isLoginRequest) {
          console.log('登录请求失败，不尝试刷新令牌');
          return Promise.reject(error);
        }

        if (errorCode) {
          console.log(`认证错误: ${errorCode} - ${error.response.data?.message || '未知错误'}`);

          // 根据错误码处理不同的认证错误
          switch (errorCode) {
            case 'auth/token-expired':
              console.log('访问令牌已过期，尝试刷新...');
              if (!isRetryRequest(originalRequest)) {
                return handleTokenRefresh(originalRequest);
              }
              break;

            case 'auth/token-invalid':
              console.warn('访问令牌无效，可能需要重新登录');
              if (!isRetryRequest(originalRequest)) {
                return handleTokenRefresh(originalRequest);
              }
              break;

            case 'auth/token-missing':
              console.warn('未提供访问令牌');
              // 也可以尝试刷新，因为用户可能有刷新令牌
              if (!isRetryRequest(originalRequest)) {
                return handleTokenRefresh(originalRequest);
              }
              break;

            case 'auth/user-not-found':
            case 'auth/user-banned':
              console.error(`用户状态异常: ${error.response.data.message}`);
              // 这些情况应该直接退出登录
              // TODO: 跳转到登录页或显示错误消息
              break;

            case 'auth/insufficient-permissions':
              console.error('权限不足，无法访问资源');
              // TODO: 显示权限错误消息
              break;

            case 'auth/refresh-token-invalid':
            case 'auth/refresh-token-missing':
            case 'auth/refresh-token-expired':
              console.error('刷新令牌失效，需要重新登录');
              // 跳转到登录页面
              // TODO: 实现跳转逻辑
              break;

            default:
              // 其他未处理的身份验证错误
              console.error(`未处理的认证错误: ${errorCode}`);
          }
        } else {
          // 无错误码的401错误，尝试刷新令牌（向后兼容）
          console.log('检测到401错误，无错误码，尝试刷新令牌...');
          if (!isRetryRequest(originalRequest)) {
            return handleTokenRefresh(originalRequest);
          }
        }
      }

      // 打印错误信息，便于调试
      console.log(`API错误: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
    } else if (error.request) {
      console.log('API请求未收到响应');
    } else {
      console.log('API请求设置出错:', error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * 处理令牌刷新的函数
 */
async function handleTokenRefresh(originalRequest: AxiosRequestConfig): Promise<any> {
  // 标记该请求已处理过，避免循环刷新
  if (originalRequest.headers) {
    originalRequest.headers._retry = true;
  }

  try {
    // 调用刷新令牌接口
    const newToken = await tokenService.refreshToken();

    if (newToken) {
      console.log('令牌刷新成功，重试原请求');
      // 使用新令牌更新请求头
      if (originalRequest.headers) {
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
      }

      // 重新发送失败的请求
      return api(originalRequest);
    } else {
      // 刷新失败，清除令牌
      console.warn('令牌刷新失败，需要重新登录');

      // 可以在这里添加重定向到登录页的逻辑
      // window.location.href = '/login';
      return Promise.reject(new Error('令牌刷新失败'));
    }
  } catch (refreshError) {
    console.error('令牌刷新过程中出错:', refreshError);
    return Promise.reject(refreshError);
  }
}

export default api;