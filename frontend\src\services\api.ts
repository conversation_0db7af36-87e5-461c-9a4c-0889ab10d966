import { KLineData } from '../types/chartTypes';
import { Time } from 'lightweight-charts';
import api from '../api/config'; // 使用统一的axios实例

/**
 * 获取30分钟K线数据
 * 从后端API获取比特币的30分钟K线数据
 * @param {number} endTime 可选参数，获取指定时间戳之前的数据
 * @returns {Promise<KLineData[]>} 返回格式化的K线数据数组
 */
export const fetchThirtyMinKlineData = async (endTime?: number): Promise<KLineData[]> => {
  try {
    let url = `/thirty-min-klines`;

    // 如果提供了endTime参数，添加到查询字符串中
    if (endTime) {
      url += `?endTime=${endTime}`;
    }

    const response = await api.get(url);
    
    // 格式化K线数据
    const formattedData = response.data.map((item: any) => ({
      time: item.time,
      open: Number(item.open),
      high: Number(item.high),
      low: Number(item.low),
      close: Number(item.close),
      volume: Number(item.volume),
      fromDatabase: true // 标记数据来源为数据库
    }));
    
    // 按时间升序排序
    formattedData.sort((a: any, b: any) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    console.log('API获取并排序后的30分钟K线数据:', formattedData.length, '条');
    return formattedData;
  } catch (error) {
    console.error('获取30分钟K线数据时出错:', error);
    throw error;
  }
};
/**
 * 获取预测数据
 * 从后端API获取比特币价格预测数据
 * @param {number} endTime 可选参数，获取指定时间戳之前的预测数据
 * @returns {Promise<any>} 返回格式化的预测数据数组
 */
export const fetchPredictions = async (endTime?: number) => {
  try {
    let url = `/predictions`;

    // 如果提供了endTime参数，添加到查询字符串中
    if (endTime) {
      url += `?endTime=${endTime}`;
    }

    const response = await api.get(url);

    // 格式化并确保数据按时间升序排序
    const formattedData = response.data.map((item: any) => ({
      time: item.time,
      open: Number(item.open),
      high: Number(item.high),
      low: Number(item.low),
      close: Number(item.close),
      isActive: item.isActive,
    }));

    // 按时间升序排序
    formattedData.sort((a: any, b: any) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });

    console.log('API获取预测数据:', formattedData.length, '条');
    return formattedData;
  } catch (error) {
    console.error('获取预测数据时出错:', error);
    throw error;
  }
};





/**
 * 获取预测折线数据
 * 从后端API获取指定数量的预测折线数据，支持分页加载
 * @param {number} endTime 可选参数，获取指定时间戳之前的预测数据
 * @param {number} limit 可选参数，获取的预测点数量，默认480个
 * @returns {Promise<any>} 返回预测折线数据数组
 */
export const fetchPredictionLines = async (endTime?: number, limit?: number) => {
  try {
    let url = `/solar-term-predictions`;
    const params = new URLSearchParams();

    // 添加查询参数
    if (endTime) {
      params.append('endTime', endTime.toString());
    }
    if (limit) {
      params.append('limit', limit.toString());
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await api.get(url);

    if (!response.data || !Array.isArray(response.data)) {
      console.warn('预测折线数据格式异常:', response.data);
      return [];
    }

    // 确保数据格式正确
    const formattedData = response.data.map((item: any) => ({
      time: typeof item.time === 'string' ? parseInt(item.time) : item.time,
      value: typeof item.value === 'string' ? parseFloat(item.value) : item.value
    }));

    // 按时间升序排序
    formattedData.sort((a: any, b: any) => a.time - b.time);

    const limitText = limit ? `${limit}个点` : '默认';
    console.log('API获取预测折线数据:', formattedData.length, '个预测点', `(${limitText})`, endTime ? `endTime: ${new Date(endTime).toISOString()}` : '');
    return formattedData;
  } catch (error) {
    console.error('获取预测折线数据时出错:', error);
    throw error;
  }
};

// 默认导出统一的axios实例，供其他模块使用
export default api;