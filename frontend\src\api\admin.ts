import axios from 'axios';

// 创建带有基础URL的axios实例
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

const adminApi = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 管理员token内存存储
let adminTokenInMemory: string | null = null;

// 管理员token管理函数
export const setAdminToken = (token: string | null) => {
  adminTokenInMemory = token;
};

export const getAdminToken = (): string | null => {
  return adminTokenInMemory;
};

export const clearAdminToken = () => {
  adminTokenInMemory = null;
};

// 请求拦截器，自动添加认证头
adminApi.interceptors.request.use(
  (config) => {
    if (adminTokenInMemory) {
      config.headers.Authorization = `Bearer ${adminTokenInMemory}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 统一的401错误处理函数
const handleAdminAuthError = () => {
  console.log('管理员token过期，清除认证状态');

  // 清除内存token
  clearAdminToken();

  // 清除localStorage中的管理员信息
  localStorage.removeItem('admin-storage');

  // 设置重定向标志，避免循环
  sessionStorage.setItem('admin_redirecting', 'true');

  // 重定向到管理员登录页
  window.location.href = '/admin/login';
};

// 响应拦截器，统一处理401错误
adminApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // 检查当前URL是否已经是管理员登录页
      const currentPath = window.location.pathname;

      if (currentPath !== '/admin/login') {
        handleAdminAuthError();
      }

      return Promise.reject(new Error('管理员认证已过期，请重新登录'));
    }
    return Promise.reject(error);
  }
);

// 管理员登录
export const adminLogin = async (email: string, password: string) => {
  try {
    const response = await adminApi.post('/admin/login', { email, password });

    if (response.data.success && response.data.accessToken) {
      // 存储token到内存
      setAdminToken(response.data.accessToken);
      return response.data;
    } else {
      throw new Error('登录响应格式错误');
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '登录失败');
    }
    throw error;
  }
};

// 获取用户列表
export const getUsers = async (page = 1, limit = 10, search = '', filters = {}) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    if (search) {
      params.append('search', search);
    }

    const response = await adminApi.get(`/admin/users?${params}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取用户列表失败');
    }
    throw error;
  }
};

// 更新用户角色
export const updateUserRole = async (userId: string, role: string) => {
  try {
    const response = await adminApi.patch(`/admin/users/${userId}`, { role });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新用户角色失败');
    }
    throw error;
  }
};

// 创建用户
export const createUser = async (userData: { email: string; password: string; role?: string; isVerified?: boolean }) => {
  try {
    const response = await adminApi.post('/admin/users', userData);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '创建用户失败');
    }
    throw error;
  }
};

// 删除用户
export const deleteUser = async (userId: string) => {
  try {
    const response = await adminApi.delete(`/admin/users/${userId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '删除用户失败');
    }
    throw error;
  }
};

// 封禁用户
export const banUser = async (userId: string) => {
  try {
    const response = await adminApi.post(`/admin/users/${userId}/ban`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '封禁用户失败');
    }
    throw error;
  }
};

// 解封用户
export const unbanUser = async (userId: string) => {
  try {
    const response = await adminApi.post(`/admin/users/${userId}/unban`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '解封用户失败');
    }
    throw error;
  }
};

// 获取系统设置
export const getSystemSettings = async () => {
  try {
    const response = await adminApi.get('/admin/settings');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取系统设置失败');
    }
    throw error;
  }
};

// 更新系统设置
export const updateSystemSettings = async (settings: any) => {
  try {
    const response = await adminApi.put('/admin/settings', settings);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新系统设置失败');
    }
    throw error;
  }
};

// 获取支付记录
export const getPayments = async (page = 1, limit = 10, search = '', filters = {}) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    if (search) {
      params.append('search', search);
    }

    const response = await adminApi.get(`/admin/payments?${params}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付记录失败');
    }
    throw error;
  }
};

// 获取支付统计数据
export const getPaymentStats = async () => {
  try {
    const response = await adminApi.get('/admin/payments/stats');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付统计数据失败');
    }
    throw error;
  }
};

// 获取支付详情
export const getPaymentDetails = async (paymentId: string) => {
  try {
    const response = await adminApi.get(`/admin/payments/${paymentId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付详情失败');
    }
    throw error;
  }
};

// 更新支付状态
export const updatePaymentStatus = async (paymentId: string, paymentStatus: string) => {
  try {
    const response = await adminApi.patch(`/admin/payments/${paymentId}`, { paymentStatus });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新支付状态失败');
    }
    throw error;
  }
};



// 更新用户订阅
export const updateUserSubscription = async (userId: string, subscriptionData: any) => {
  try {
    const response = await adminApi.patch(`/admin/users/${userId}/subscription`, subscriptionData);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新用户订阅失败');
    }
    throw error;
  }
};

// 导出axios实例供直接使用
export { adminApi as adminApiInstance };

export default {
  adminLogin,
  getUsers,
  updateUserRole,
  createUser,
  deleteUser,
  banUser,
  unbanUser,
  getSystemSettings,
  updateSystemSettings,
  getPayments,
  getPaymentStats,
  getPaymentDetails,
  updatePaymentStatus,
  updateUserSubscription,
  setAdminToken,
  getAdminToken,
  clearAdminToken
};