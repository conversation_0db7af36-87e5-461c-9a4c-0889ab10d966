// 使用统一的管理员API客户端
import { adminApiInstance } from './admin';
import axios from 'axios';

// 获取反馈详情（管理员专用）
export const getAdminFeedbackDetail = async (id: string) => {
  try {
    const response = await adminApiInstance.get(`/admin/feedback/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取反馈详情失败');
    }
    throw error;
  }
};

// 获取所有反馈列表
export const getAllFeedbacks = async (
  page = 1,
  limit = 10,
  status?: string,
  search?: string
) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(status && { status }),
      ...(search && { search })
    });

    const response = await adminApiInstance.get(`/admin/feedback?${params}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取反馈列表失败');
    }
    throw error;
  }
};

// 回复反馈
export const replyFeedback = async (
  id: string,
  data: { reply: string; status?: string }
) => {
  try {
    const response = await adminApiInstance.post(`/admin/feedback/${id}/reply`, data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '回复反馈失败');
    }
    throw error;
  }
};

// 更新反馈状态
export const updateFeedbackStatus = async (
  id: string,
  status: 'pending' | 'processing' | 'replied'
) => {
  try {
    const response = await adminApiInstance.patch(`/admin/feedback/${id}/status`, {
      status
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新反馈状态失败');
    }
    throw error;
  }
};

export default {
  getAdminFeedbackDetail,
  getAllFeedbacks,
  replyFeedback,
  updateFeedbackStatus
};
