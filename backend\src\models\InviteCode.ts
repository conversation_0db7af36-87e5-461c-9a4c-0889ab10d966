import mongoose, { Document, Model, Schema } from 'mongoose';

/**
 * 邀请码文档接口
 */
export interface InviteCodeDocument extends Document {
  code: string;
  createdBy: mongoose.Types.ObjectId;
  usedBy?: mongoose.Types.ObjectId;
  usedAt?: Date;
  createdAt: Date;
}

/**
 * 邀请码模型接口 - 包含静态方法
 */
export interface InviteCodeModel extends Model<InviteCodeDocument> {
  generateInviteCode(): string;
  generateCodesForUser(userId: mongoose.Types.ObjectId, count?: number): Promise<InviteCodeDocument[]>;
}

// 邀请码模型Schema
const InviteCodeSchema = new Schema<InviteCodeDocument>({
  // 邀请码文本
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // 创建者
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 使用者（可选）
  usedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // 使用时间（可选）
  usedAt: {
    type: Date,
    default: null
  },
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * 生成随机邀请码的辅助函数
 */
function generateRandomCode(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  
  // 生成8位长度的随机邀请码
  for (let i = 0; i < 8; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters.charAt(randomIndex);
  }
  
  // 每2位增加一个连字符，使邀请码更易读
  return code.match(/.{1,2}/g)!.join('-');
}

/**
 * 生成随机邀请码
 */
InviteCodeSchema.static('generateInviteCode', function(): string {
  return generateRandomCode();
});

/**
 * 为用户生成多个邀请码
 */
InviteCodeSchema.static('generateCodesForUser', async function(
  userId: mongoose.Types.ObjectId, 
  count: number = 5
): Promise<Document[]> {
  const inviteCodes: Document[] = [];
  
  for (let i = 0; i < count; i++) {
    let code = generateRandomCode();
    let isUnique = false;
    
    // 确保生成的邀请码是唯一的
    while (!isUnique) {
      code = generateRandomCode();
      // 检查数据库中是否已存在该邀请码
      const existingCode = await this.findOne({ code });
      if (!existingCode) {
        isUnique = true;
      }
    }
    
    const inviteCode = await this.create({
      code,
      createdBy: userId
    });
    
    inviteCodes.push(inviteCode);
  }
  
  return inviteCodes;
});

// 创建并导出模型
const InviteCode = mongoose.model<InviteCodeDocument>('InviteCode', InviteCodeSchema);

// 将模型断言为包含静态方法的类型
export default InviteCode as InviteCodeModel; 