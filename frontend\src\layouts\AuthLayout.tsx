import React from 'react';
import { Link, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const AuthLayout: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card flex flex-col relative overflow-hidden">
      {/* Animated Background Elements - 网格背景 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(0, 245, 255, 0.3)" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-8 relative z-10">
        <Outlet />
      </main>

      {/* Footer with Legal Links */}
      <footer className="relative z-10 py-6">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 text-sm text-cyber-muted font-mono">
            <Link
              to="/privacy"
              className="hover:text-cyber-cyan transition-colors duration-200"
            >
              {t('footer.privacy')}
            </Link>
            <span className="text-cyber-border">|</span>
            <Link
              to="/terms"
              className="hover:text-cyber-cyan transition-colors duration-200"
            >
              {t('footer.terms')}
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AuthLayout;
