import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';

/**
 * 获取公共配置信息
 * 返回网站基本信息等公共配置
 * GET /api/config
 */
export const getPublicConfig = async (req: Request, res: Response) => {
  try {
    // 获取网站基本信息
    const settings = await systemSettingsService.getSystemSettings();
    
    // 返回公共配置
    res.json({
      success: true,
      config: {
        siteInfo: {
          siteName: settings.siteInfo.siteName,
          siteDescription: settings.siteInfo.siteDescription,
          siteKeywords: settings.siteInfo.siteKeywords,
          copyright: settings.siteInfo.copyright,
          socialLinks: settings.siteInfo.socialLinks,
          defaultLanguage: settings.siteInfo.defaultLanguage
        },
        // 添加水印配置
        watermark: {
          siteDomain: settings.securitySettings.siteDomain || ''
        }
      }
    });
  } catch (error) {
    console.error('获取公共配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getPublicConfig
};
