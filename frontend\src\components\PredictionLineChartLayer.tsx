import React, { useCallback, useRef, useEffect } from 'react';
import { ISeriesApi, Range, Time } from 'lightweight-charts';
import { usePredictionLineDataSSE } from '../hooks/usePredictionLineDataSSE';

interface PredictionLineChartLayerProps {
  lineSeries: ISeriesApi<'Area'> | null;
  chartApi: any;
  isVisible: boolean;
}

const PredictionLineChartLayer: React.FC<PredictionLineChartLayerProps> = ({
  lineSeries,
  chartApi,
  isVisible
}) => {
  const isLoadingMoreRef = useRef(false);
  const oldestLoadedTimeRef = useRef<number | null>(null);
  const lastErrorRef = useRef<string | null>(null); // 添加错误缓存

  // 使用SSE数据Hook
  const {
    predictionLineData,
    dataError,
    connectionError,
    loadMoreHistory
  } = usePredictionLineDataSSE();

  // 处理数据格式化（始终执行，不受可见性影响）
  const formattedDataRef = useRef<any[]>([]);

  useEffect(() => {
    if (predictionLineData.length === 0) return;

    try {
      // 格式化数据为图表格式并去重
      const dataMap = new Map();

      predictionLineData.forEach((item: any) => {
        // 将毫秒级时间戳转换为秒级时间戳（图表库要求）
        const timeInSeconds = Math.floor(item.time / 1000);
        const timeKey = timeInSeconds;

        // 如果时间戳已存在，保留最新的数据
        if (!dataMap.has(timeKey) || dataMap.get(timeKey).value < item.value) {
          dataMap.set(timeKey, {
            time: timeInSeconds as Time,
            value: item.value
          });
        }
      });

      // 转换为数组并按时间排序
      const formattedData = Array.from(dataMap.values());
      formattedData.sort((a, b) => {
        const timeA = typeof a.time === 'number' ? a.time : new Date(a.time as string).getTime() / 1000;
        const timeB = typeof b.time === 'number' ? b.time : new Date(b.time as string).getTime() / 1000;
        return timeA - timeB;
      });

      // 保存格式化后的数据
      formattedDataRef.current = formattedData;

      // 更新最早时间记录
      if (formattedData.length > 0) {
        const firstTime = formattedData[0].time as number; // 已经是秒级时间戳
        oldestLoadedTimeRef.current = firstTime * 1000; // 转换为毫秒供历史加载使用
      }

      console.log(`预测折线数据已处理: ${formattedData.length} 个数据点（去重后）`);

      // 如果当前可见，立即更新图表
      if (lineSeries && isVisible) {
        lineSeries.setData(formattedData);
        console.log(`预测折线图表数据已更新: ${formattedData.length} 个数据点`);
      }
    } catch (error) {
      console.error('处理预测折线数据失败:', error);
    }
  }, [predictionLineData, lineSeries, isVisible]);

  // 可见性变化时更新图表数据
  useEffect(() => {
    if (!lineSeries || !isVisible || formattedDataRef.current.length === 0) return;

    try {
      // 使用已格式化的数据更新图表
      lineSeries.setData(formattedDataRef.current);
      console.log(`预测折线图表数据已更新（可见性变化）: ${formattedDataRef.current.length} 个数据点`);
    } catch (error) {
      console.error('更新预测折线图表数据失败:', error);
    }
  }, [lineSeries, isVisible]);

  // 时间范围变化处理 - 用于滚动加载
  const handleTimeRangeChange = useCallback((timeRange: Range<Time> | null) => {
    if (!timeRange || isLoadingMoreRef.current || !chartApi || !loadMoreHistory) return;

    // 检查是否滚动到了左边缘
    const visibleRangeStart = timeRange.from as number;
    const visibleTimeSpan = (timeRange.to as number) - visibleRangeStart;

    // 如果视图最左侧时间接近已加载数据的最早时间的5%，触发加载更多
    const leftEdgeThreshold = visibleTimeSpan * 0.05;
    const isNearLeftEdge = oldestLoadedTimeRef.current &&
        (visibleRangeStart * 1000 - leftEdgeThreshold <= oldestLoadedTimeRef.current);

    if (isNearLeftEdge && oldestLoadedTimeRef.current) {
      console.log('预测折线接近左边界，开始加载更多历史数据');
      isLoadingMoreRef.current = true;

      // 触发加载更多历史数据
      loadMoreHistory(oldestLoadedTimeRef.current);

      // 设置延时重置加载状态
      setTimeout(() => {
        isLoadingMoreRef.current = false;
      }, 2000);
    }
  }, [chartApi, loadMoreHistory]);

  // 注册时间范围变化监听
  useEffect(() => {
    if (!chartApi) return;

    const timeScale = chartApi.timeScale();
    timeScale.subscribeVisibleTimeRangeChange(handleTimeRangeChange);

    return () => {
      timeScale.unsubscribeVisibleTimeRangeChange(handleTimeRangeChange);
    };
  }, [chartApi, handleTimeRangeChange]);

  // 错误处理 - 只在错误变化时输出日志
  useEffect(() => {
    const currentError = dataError || connectionError;
    if (currentError && currentError !== lastErrorRef.current) {
      console.error('预测折线数据错误:', currentError);
      lastErrorRef.current = currentError;
    } else if (!currentError && lastErrorRef.current) {
      lastErrorRef.current = null;
    }
  }, [dataError, connectionError]);

  return null; // 这是一个数据层组件，不渲染UI
};

export default PredictionLineChartLayer;