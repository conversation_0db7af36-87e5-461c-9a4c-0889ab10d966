import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getSubscriptionInfo } from '../api/subscription';
import { Button } from '../components/ui/button';
import { CheckCircle, AlertCircle, Loader2, ArrowRight } from 'lucide-react';
import { formatDate } from '../utils/dateFormatter';

const SubscriptionSuccess: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();

  // 解析URL参数获取支付ID
  const paymentId = new URLSearchParams(location.search).get('NP_id');

  // 提取订阅计划类型
  const getPlanName = (type: string | null | undefined) => {
    switch (type) {
      case 'monthly':
        return t('subscription.monthly');
      case 'quarterly':
        return t('subscription.quarterly');
      case 'yearly':
        return t('subscription.yearly');
      default:
        return t('subscription.subscriptionPlan');
    }
  };

  // 获取最新的订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        setLoading(true);

        // 获取最新订阅信息
        const response = await getSubscriptionInfo();
        setSubscriptionInfo(response.subscription);
        setError(null);
      } catch (err) {
        console.error('获取订阅信息失败:', err);
        setError('无法获取订阅信息，请稍后在用户中心查看');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionInfo();
  }, []);
  
  if (loading) {
    return (
      <div className="h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card flex flex-col relative overflow-hidden">
        <main className="flex-1 flex items-center justify-center px-4 py-8 relative z-10">
          <div className="flex flex-col items-center justify-center p-6">
            <Loader2 className="h-12 w-12 text-cyber-cyan animate-spin mb-4" />
            <h2 className="text-xl font-semibold mb-2 text-cyber-text font-sans">{t('subscription.processingSubscription')}</h2>
            <p className="text-cyber-muted text-center max-w-md font-mono">
              {t('subscription.processingDesc')}
            </p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card flex flex-col relative overflow-hidden">
        <main className="flex-1 flex items-center justify-center px-4 py-8 relative z-10">
          <div className="flex flex-col items-center justify-center p-6">
            <div className="bg-cyber-pink/10 text-cyber-pink p-4 rounded-xl mb-6 flex items-start border border-cyber-pink/30 backdrop-blur-sm">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <p className="font-mono">{error}</p>
            </div>
            <Button
              onClick={() => navigate('/dashboard')}
              className="bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-mono"
            >
              {t('subscription.backToAccountCenter')}
            </Button>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card flex flex-col relative overflow-hidden">
      <main className="flex-1 flex items-center justify-center px-4 py-8 relative z-10">
        <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 relative overflow-hidden">
          {/* Card glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
          <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

          <div className="relative z-10">
            <div className="text-center mb-8">
              <CheckCircle className="h-16 w-16 text-cyber-green mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-2 text-cyber-text font-sans">{t('subscription.subscriptionSuccess')}</h1>
              <p className="text-cyber-muted font-mono">
                {t('subscription.subscriptionSuccessDesc')}
              </p>
            </div>

            {subscriptionInfo && (
              <div className="bg-cyber-dark/30 border border-cyber-cyan/20 rounded-xl p-6 mb-6 backdrop-blur-sm">
                <h2 className="font-semibold text-lg mb-4 text-cyber-text font-sans">{t('subscription.subscriptionDetails')}</h2>
                <dl className="grid gap-4">
                  <div className="grid grid-cols-3 gap-1">
                    <dt className="text-cyber-muted font-mono text-sm">{t('subscription.subscriptionType')}</dt>
                    <dd className="col-span-2 font-medium text-cyber-text font-mono">{getPlanName(subscriptionInfo.type)}</dd>
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    <dt className="text-cyber-muted font-mono text-sm">{t('subscription.expiresAt')}</dt>
                    <dd className="col-span-2 font-medium text-cyber-text font-mono">{subscriptionInfo.endDate ? formatDate(subscriptionInfo.endDate, i18n.language) : t('subscription.none')}</dd>
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    <dt className="text-cyber-muted font-mono text-sm">{t('subscription.subscriptionStatus')}</dt>
                    <dd className="col-span-2 font-medium">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-cyber-green/20 text-cyber-green border border-cyber-green/30 font-mono">
                        {t('subscription.active')}
                      </span>
                    </dd>
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    <dt className="text-cyber-muted font-mono text-sm">{t('subscription.paymentId')}</dt>
                    <dd className="col-span-2 font-medium text-cyber-text font-mono text-sm truncate">
                      {paymentId || t('subscription.none')}
                    </dd>
                  </div>
                </dl>
              </div>
            )}

            <div className="flex flex-col gap-3">
              <Button
                onClick={() => navigate('/dashboard')}
                className="w-full bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-mono transition-all duration-200"
              >
                {t('subscription.goToAccountCenter')} <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SubscriptionSuccess; 