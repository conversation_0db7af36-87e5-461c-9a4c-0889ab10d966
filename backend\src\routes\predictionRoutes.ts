import { Router } from 'express';
import predictionController from '../controllers/predictionController';
import { optionalAuthenticate } from '../middlewares/authMiddleware';

// 创建路由实例
const router = Router();

// 获取预测数据路由 - 添加权限控制以保护预测K线数据
// GET /api/predictions
router.get('/', optionalAuthenticate, predictionController.getRecentPredictions);

// 注意：触发预测生成路由已删除，因为：
// 1. 前端没有使用此接口
// 2. 预测生成已通过定时任务自动化
// 3. 避免未授权用户触发系统资源消耗

// 导出路由
export default router;