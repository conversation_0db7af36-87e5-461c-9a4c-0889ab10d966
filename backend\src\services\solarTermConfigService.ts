import SolarTermConfig from '../models/SolarTermConfig';
import logger from '../utils/logger';

/**
 * 节气预测内部配置服务
 * 用于存储和管理虚拟价格转换比例等敏感配置
 * 注意：此服务仅供后端内部使用，前端API不应暴露相关接口
 */
class SolarTermConfigService {
  /**
   * 获取周期转换比例
   * @param symbol 交易对符号
   * @param cycleKey 周期标识
   * @returns 转换比例，如果不存在返回null
   */
  static async getCycleRatio(symbol: string, cycleKey: string): Promise<number | null> {
    try {
      const config = await SolarTermConfig.findOne({ symbol, cycleKey });
      return config?.conversionRatio || null;
    } catch (error) {
      logger.error('[内部配置] 获取转换比例失败', error);
      return null;
    }
  }

  /**
   * 设置周期转换比例
   * @param symbol 交易对符号
   * @param cycleKey 周期标识
   * @param conversionRatio 转换比例
   * @param virtualBase 虚拟基准价格
   */
  static async setCycleRatio(
    symbol: string, 
    cycleKey: string, 
    conversionRatio: number,
    virtualBase: number
  ): Promise<void> {
    try {
      await SolarTermConfig.findOneAndUpdate(
        { symbol, cycleKey },
        { 
          conversionRatio, 
          virtualBase,
          updatedAt: new Date() 
        },
        { upsert: true } // 不存在则创建
      );
      
      logger.info(`[内部配置] 保存周期${cycleKey}配置`, {
        cycleKey,
        conversionRatio: conversionRatio.toFixed(6),
        virtualBase: virtualBase.toFixed(2)
      });
    } catch (error) {
      logger.error('[内部配置] 保存转换比例失败', error);
      throw error;
    }
  }

  /**
   * 获取周期虚拟基准价格
   * @param symbol 交易对符号
   * @param cycleKey 周期标识
   * @returns 虚拟基准价格，如果不存在返回null
   */
  static async getCycleVirtualBase(symbol: string, cycleKey: string): Promise<number | null> {
    try {
      const config = await SolarTermConfig.findOne({ symbol, cycleKey });
      return config?.virtualBase || null;
    } catch (error) {
      logger.error('[内部配置] 获取虚拟基准失败', error);
      return null;
    }
  }

  /**
   * 清理过期配置
   * @param monthsAgo 清理多少个月前的配置，默认3个月
   */
  static async cleanExpiredConfigs(monthsAgo: number = 3): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - monthsAgo);
      
      const result = await SolarTermConfig.deleteMany({
        createdAt: { $lt: cutoffDate }
      });
      
      logger.info(`[内部配置] 清理过期配置`, { deletedCount: result.deletedCount });
    } catch (error) {
      logger.error('[内部配置] 清理过期配置失败', error);
    }
  }

  /**
   * 设置当前转换比例（全局唯一，基于节气日更新）
   * @param symbol 交易对符号
   * @param ratio 转换比例
   */
  static async setCurrentRatio(symbol: string, ratio: number): Promise<void> {
    try {
      const configKey = `${symbol}_current`;

      await SolarTermConfig.findOneAndUpdate(
        { cycleKey: configKey, symbol },
        {
          conversionRatio: ratio,
          virtualBase: 0, // 不再需要存储虚拟基准
          updatedAt: new Date()
        },
        { upsert: true }
      );

      logger.info(`[内部配置] 更新转换比例`, { symbol, ratio: ratio.toFixed(6) });
    } catch (error) {
      logger.error('[内部配置] 设置转换比例失败', error);
      throw error;
    }
  }

  /**
   * 获取当前转换比例
   * @param symbol 交易对符号
   * @returns 转换比例，如果不存在返回null
   */
  static async getCurrentRatio(symbol: string): Promise<number | null> {
    try {
      const configKey = `${symbol}_current`;

      const config = await SolarTermConfig.findOne({ cycleKey: configKey, symbol });
      return config?.conversionRatio || null;
    } catch (error) {
      logger.error('[内部配置] 获取转换比例失败', error);
      return null;
    }
  }

  /**
   * 获取所有配置（仅用于调试）
   */
  static async getAllConfigs(): Promise<any[]> {
    try {
      const configs = await SolarTermConfig.find({}).sort({ createdAt: -1 });
      return configs.map(config => ({
        cycleKey: config.cycleKey,
        symbol: config.symbol,
        virtualBase: config.virtualBase,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
        // 注意：不返回 conversionRatio 敏感信息
      }));
    } catch (error) {
      logger.error('[内部配置] 获取所有配置失败', error);
      return [];
    }
  }
}

export default SolarTermConfigService;
