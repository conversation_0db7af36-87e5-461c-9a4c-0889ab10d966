/**
 * 为node-cron库提供类型声明
 */
declare module 'node-cron' {
  /**
   * 调度函数，根据cron表达式安排任务执行
   * 
   * @param expression cron表达式
   * @param task 要执行的任务函数
   * @param options 可选的配置选项
   * @returns 定时任务对象
   */
  export function schedule(
    expression: string,
    task: Function,
    options?: {
      scheduled?: boolean;
      timezone?: string;
    }
  ): {
    start: () => void;
    stop: () => void;
  };

  /**
   * 验证cron表达式
   * 
   * @param expression 要验证的cron表达式
   * @returns 表达式是否有效
   */
  export function validate(expression: string): boolean;
} 