import React, { useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import TimezoneSelector from './TimezoneSelector';

interface ChartInfoBarProps {
  symbol: string;        // 交易对名称  // 图表周期
  className?: string;    // 可选的额外CSS类名
  chartType?: 'candlestick' | 'line'; // 图表类型
  onChartTypeChange?: (type: 'candlestick' | 'line') => void; // 切换图表类型的回调函数
}

/**
 * 图表顶部信息栏组件
 * 显示交易对名称和图表周期
 */
const ChartInfoBar: React.FC<ChartInfoBarProps> = ({
  symbol = "BTC/USDT",
  className = "",
  chartType = "candlestick",
  onChartTypeChange
}) => {
  const { t } = useTranslation();
  // 全屏状态引用
  const [isFullscreen, setIsFullscreen] = useState(false);
  // 图表容器引用
  const chartContainerRef = useRef<HTMLElement | null>(null);

  // 在组件挂载时获取图表容器
  useEffect(() => {
    // 获取最近的父级图表容器元素
    const findChartContainer = () => {
      // 查找父级 .chart-composite 元素
      const container = document.querySelector('.chart-composite') as HTMLElement;
      if (container) {
        chartContainerRef.current = container;
      }
    };

    findChartContainer();

    // 监听全屏变化事件
    const handleFullscreenChange = () => {
      setIsFullscreen(Boolean(document.fullscreenElement));
    };

    // 监听ESC键退出全屏
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        // ESC键被按下且当前处于全屏模式，无需手动调用退出全屏
        // 浏览器会自动处理退出全屏操作
        console.log('检测到ESC键退出全屏');
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!chartContainerRef.current) return;

    if (!isFullscreen) {
      // 进入全屏模式前先存储当前位置
      console.log('进入全屏模式');
      
      // 使用requestAnimationFrame确保DOM操作在同一帧内完成
      requestAnimationFrame(() => {
        chartContainerRef.current?.requestFullscreen()
          .catch(err => {
            console.error(`全屏模式错误: ${err.message}`);
          });
      });
    } else {
      // 退出全屏模式
      console.log('退出全屏模式');
      if (document.exitFullscreen) {
        // 使用requestAnimationFrame确保DOM操作在同一帧内完成
        requestAnimationFrame(() => {
          document.exitFullscreen()
            .catch(err => {
              console.error(`退出全屏模式错误: ${err.message}`);
            });
        });
      }
    }
  };

  // 线形图图标 SVG
  const LineChartIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 17L9 11L13 15L21 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
  
  // K线图图标 SVG
  const CandlestickIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="4" y="6" width="4" height="12" stroke="currentColor" strokeWidth="2"/>
      <rect x="10" y="9" width="4" height="9" fill="currentColor"/>
      <rect x="16" y="4" width="4" height="14" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  // 全屏图标 SVG
  const FullscreenIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" 
        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // 退出全屏图标 SVG
  const ExitFullscreenIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" 
        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // 切换到K线图
  const switchToCandlestick = () => {
    if (onChartTypeChange && chartType !== 'candlestick') {
      onChartTypeChange('candlestick');
    }
  };

  // 切换到折线图
  const switchToLine = () => {
    if (onChartTypeChange && chartType !== 'line') {
      onChartTypeChange('line');
    }
  };

  return (
    <div className={`chart-info-bar absolute top-0 left-0 w-full h-auto min-h-[44px] flex flex-wrap items-center justify-between bg-[rgba(18,20,24,0.9)] backdrop-blur-md text-cyber-text px-2 py-1 text-base font-medium border-b border-cyber-cyan/30 z-10 font-mono ${className}`}>
      {/* 左侧信息 */}
      <div className="flex items-center flex-wrap">
        <div className="font-bold text-lg">{symbol}</div>

        &nbsp;{/* 半透明竖线分隔符 */}
        <div className="mx-2 h-5 w-px bg-cyber-cyan/30"></div>
        
        {/* 图表类型图标组 */}
        <div className="flex items-center space-x-2 ml-1">
          <div
            className={`flex items-center p-1 rounded cursor-pointer transition-colors hover:bg-cyber-cyan/10 hover:text-cyber-cyan ${chartType === 'candlestick' ? 'text-cyber-cyan' : 'text-cyber-muted'}`}
            onClick={switchToCandlestick}
          >
            <CandlestickIcon />
            <span className="ml-1 text-sm md:inline hidden">SNAPK.30m</span>
          </div>

          {/* 图表类型选项之间的分隔线 */}
          <div className="h-5 w-px bg-cyber-cyan/30"></div>

          <div
            className={`flex items-center p-1 rounded cursor-pointer transition-colors hover:bg-cyber-cyan/10 hover:text-cyber-cyan ${chartType === 'line' ? 'text-cyber-cyan' : 'text-cyber-muted'}`}
            onClick={switchToLine}
          >
            <LineChartIcon />
            <span className="ml-1 text-sm md:inline hidden">TWAVE.30m</span>
          </div>
        </div>
      </div>

      {/* 右侧功能区域 */}
      <div className="flex items-center space-x-2 relative z-50">
        {/* 时区选择器 */}
        <TimezoneSelector />
        
        {/* 全屏按钮 */}
        <div
          className="flex items-center p-1 rounded cursor-pointer transition-colors hover:bg-cyber-cyan/10 hover:text-cyber-cyan text-cyber-muted"
          onClick={toggleFullscreen}
          title={isFullscreen ? t('chart.exitFullscreen') : t('chart.fullscreen')}
        >
          {isFullscreen ? <ExitFullscreenIcon /> : <FullscreenIcon />}
        </div>
      </div>
    </div>
  );
};

export default ChartInfoBar; 