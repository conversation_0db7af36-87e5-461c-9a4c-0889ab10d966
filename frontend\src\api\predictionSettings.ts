import api from './config';

/**
 * 预测图设置接口
 */
export interface PredictionSettings {
  enableKlinePrediction: boolean;
  enableLinePrediction: boolean;
}

/**
 * API响应接口
 */
interface PredictionSettingsResponse {
  success: boolean;
  settings: PredictionSettings;
}

/**
 * 获取预测图显示设置
 * @returns 预测图设置
 */
export const getPredictionSettings = async (): Promise<PredictionSettings> => {
  try {
    const response = await api.get<PredictionSettingsResponse>('/config/prediction-settings');
    
    if (response.data.success) {
      return response.data.settings;
    } else {
      throw new Error('获取预测图设置失败');
    }
  } catch (error) {
    console.error('获取预测图设置失败:', error);
    // 返回默认设置
    return {
      enableKlinePrediction: true,
      enableLinePrediction: true
    };
  }
};

export default {
  getPredictionSettings
};
