/**
 * 预测K线数据清理脚本
 * 
 * 使用方法:
 * node cleanPredictionKlines.js [选项]
 * 
 * 选项:
 *   --all                           删除所有预测K线数据
 *   --symbol=XXX                    指定交易对，例如: --symbol=BTCUSDT
 *   --before=YYYY-MM-DD            删除指定日期之前的数据
 *   --after=YYYY-MM-DD             删除指定日期之后的数据
 *   --range=YYYY-MM-DD,YYYY-MM-DD  删除指定时间段的数据
 *   --help                          显示帮助信息
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// 解析命令行参数
const args = process.argv.slice(2);
const options = parseArguments(args);

// 显示帮助信息
if (options.help) {
  console.log(`
预测K线数据清理脚本

使用方法:
node cleanPredictionKlines.js [选项]

选项:
  --all                           删除所有预测K线数据
  --symbol=XXX                    指定交易对，例如: --symbol=BTCUSDT
  --before=YYYY-MM-DD            删除指定日期之前的数据
  --after=YYYY-MM-DD             删除指定日期之后的数据
  --range=YYYY-MM-DD,YYYY-MM-DD  删除指定时间段的数据
  --help                          显示帮助信息

示例:
  node cleanPredictionKlines.js --all
  node cleanPredictionKlines.js --all --symbol=BTCUSDT
  node cleanPredictionKlines.js --before=2024-05-01
  node cleanPredictionKlines.js --after=2024-01-01 --symbol=BTCUSDT
  node cleanPredictionKlines.js --range=2024-01-01,2024-12-31
  `);
  process.exit(0);
}

// 检查是否有删除条件
if (!options.all && !options.symbol && !options.before && !options.after && !options.range) {
  console.error('错误: 必须指定至少一个删除条件或使用 --all 删除所有数据');
  console.log('使用 --help 查看帮助信息');
  process.exit(1);
}

// 获取MongoDB连接URI
const MONGO_URI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction';

// 预测模型定义 - 确保与实际项目中定义完全一致
const predictionSchema = new mongoose.Schema({
  symbol: { type: String, required: true, index: true },
  predictionTime: { type: Number, required: true },
  targetStartTime: { type: Number, required: true, index: true },
  targetEndTime: { type: Number, required: true },
  sourceKlineTime: { type: Number, required: true },
  open: { type: String, required: true },
  high: { type: String, required: true },
  low: { type: String, required: true },
  close: { type: String, required: true },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  versionKey: false
});

// 添加与项目中相同的索引
predictionSchema.index({ symbol: 1, targetStartTime: 1 }, { unique: true });

// 创建模型
const Prediction = mongoose.model('Prediction', predictionSchema);

// 连接到MongoDB并执行清理
mongoose.connect(MONGO_URI)
  .then(() => {
    console.log('已连接到MongoDB数据库');
    return cleanPredictionKlines(options);
  })
  .then(result => {
    console.log(`成功删除 ${result.deletedCount} 条预测K线数据`);
    mongoose.disconnect();
  })
  .catch(error => {
    console.error('删除数据时出错:', error);
    mongoose.disconnect();
    process.exit(1);
  });

/**
 * 清理预测K线数据
 * @param {Object} options 删除选项
 * @returns {Promise<Object>} 删除结果
 */
async function cleanPredictionKlines(options) {
  // 构建查询条件
  const query = {};
  
  // 按交易对过滤
  if (options.symbol) {
    query.symbol = options.symbol;
  }
  
  // 按日期过滤 - 使用 targetStartTime 字段
  if (options.before || options.after || options.range) {
    query.targetStartTime = {};
    
    if (options.range) {
      // 处理时间段过滤
      const [startDate, endDate] = options.range.split(',');
      if (startDate && endDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        query.targetStartTime.$gte = start.getTime();
        query.targetStartTime.$lte = end.getTime();
      }
    } else {
      if (options.before) {
        const beforeDate = new Date(options.before);
        beforeDate.setHours(23, 59, 59, 999);
        query.targetStartTime.$lt = beforeDate.getTime();
      }
      
      if (options.after) {
        const afterDate = new Date(options.after);
        afterDate.setHours(0, 0, 0, 0);
        query.targetStartTime.$gt = afterDate.getTime();
      }
    }
  }
  
  // 删除前显示将要删除的数据
  const count = await Prediction.countDocuments(query);
  console.log(`将要删除 ${count} 条预测K线数据`);
  
  // 显示查询条件
  console.log('查询条件:', JSON.stringify(query, null, 2));
  
  // 确认删除
  if (count > 0) {
    const confirm = await promptConfirmation(`确定要删除这 ${count} 条数据吗? (y/n) `);
    if (!confirm) {
      console.log('操作已取消');
      return { deletedCount: 0 };
    }
  } else {
    console.log('没有符合条件的数据需要删除');
    return { deletedCount: 0 };
  }
  
  // 执行删除操作
  const result = await Prediction.deleteMany(query);
  return result;
}

/**
 * 解析命令行参数
 * @param {string[]} args 命令行参数数组
 * @returns {Object} 解析后的选项对象
 */
function parseArguments(args) {
  const options = {
    all: false,
    symbol: null,
    before: null,
    after: null,
    range: null,
    help: false
  };
  
  for (const arg of args) {
    if (arg === '--all') {
      options.all = true;
    } else if (arg === '--help') {
      options.help = true;
    } else if (arg.startsWith('--symbol=')) {
      options.symbol = arg.split('=')[1];
    } else if (arg.startsWith('--before=')) {
      options.before = arg.split('=')[1];
    } else if (arg.startsWith('--after=')) {
      options.after = arg.split('=')[1];
    } else if (arg.startsWith('--range=')) {
      options.range = arg.split('=')[1];
    }
  }
  
  return options;
}

/**
 * 控制台提示确认
 * @param {string} question 提示问题
 * @returns {Promise<boolean>} 是否确认
 */
function promptConfirmation(question) {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise(resolve => {
    readline.question(question, answer => {
      readline.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}
