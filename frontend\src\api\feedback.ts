import axios from 'axios';
import api from './config'; // 使用统一的axios实例
import i18n from '../i18n';

// 获取翻译的错误消息
const getErrorMessage = (key: string, fallback: string) => {
  return i18n.t(key, fallback);
};

// 提交反馈
export const submitFeedback = async (feedbackData: { title: string; content: string }) => {
  try {
    // 统一的axios实例会自动添加认证头
    const response = await api.post('/feedback', feedbackData);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.submitFeedbackFailed', '提交反馈失败'));
    }
    throw error;
  }
};

// 获取当前用户的反馈列表
export const getUserFeedbacks = async (page = 1, limit = 10) => {
  try {
    // 统一的axios实例会自动添加认证头
    const response = await api.get(`/feedback?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.getFeedbackListFailed', '获取反馈列表失败'));
    }
    throw error;
  }
};

// 获取反馈详情
export const getFeedbackDetail = async (id: string) => {
  try {
    // 统一的axios实例会自动添加认证头
    const response = await api.get(`/feedback/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.getFeedbackDetailFailed', '获取反馈详情失败'));
    }
    throw error;
  }
};

export default {
  submitFeedback,
  getUserFeedbacks,
  getFeedbackDetail
};
