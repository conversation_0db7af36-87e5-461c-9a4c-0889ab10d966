import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import systemSettingsService from '../services/systemSettingsService';
import logger from './logger';
import PasswordResetToken from '../models/PasswordResetToken';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

dotenv.config();

const JWT_RESET_SECRET = process.env.JWT_RESET_SECRET || 'fallback-reset-secret';

// API URLs
const RESEND_API_URL = 'https://api.resend.com/emails';
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';
const MAILERSEND_API_URL = 'https://api.mailersend.com/v1/email';



/**
 * 获取邮件配置
 */
const getEmailConfig = async () => {
  try {
    const settings = await systemSettingsService.getSystemSettings();
    return settings.emailSettings;
  } catch (error) {
    logger.error('获取邮件配置失败', error);
    // 返回默认配置
    return {
      provider: 'resend' as const,
      resend: {
        apiKey: process.env.RESEND_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: true
      },
      brevo: {
        apiKey: process.env.BREVO_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: true
      },
      mailersend: {
        apiKey: process.env.MAILERSEND_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: false
      },
      retryTimes: 2,
      timeout: 30000,
      fallbackProvider: 'brevo' as 'resend' | 'brevo' | 'mailersend',
      enableLogs: true
    };
  }
};

/**
 * 使用Resend API发送邮件
 */
const sendEmailWithResend = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(RESEND_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: fromEmail,
        to: [to],
        subject: subject,
        html: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Resend API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('Resend API 发送成功:', result.id);
    return true;
  } catch (error) {
    console.error('Resend API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 使用Brevo API发送邮件
 */
const sendEmailWithBrevo = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(BREVO_API_URL, {
      method: 'POST',
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sender: { email: fromEmail },
        to: [{ email: to }],
        subject: subject,
        htmlContent: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Brevo API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('Brevo API 发送成功:', result.messageId);
    return true;
  } catch (error) {
    console.error('Brevo API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 使用MailerSend API发送邮件
 */
const sendEmailWithMailerSend = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(MAILERSEND_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: { email: fromEmail },
        to: [{ email: to }],
        subject: subject,
        html: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('MailerSend API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('MailerSend API 发送成功:', result.id);
    return true;
  } catch (error) {
    console.error('MailerSend API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 统一邮件发送函数 - 支持多个邮件服务商
 */
const sendEmail = async (to: string, subject: string, html: string): Promise<boolean> => {
  try {
    // 获取邮件配置
    const emailConfig = await getEmailConfig();

    // 检查主要服务商配置
    const primaryProvider = emailConfig.provider;
    const primaryConfig = emailConfig[primaryProvider];

    if (!primaryConfig.apiKey || !primaryConfig.enabled) {
      console.error(`邮件发送失败: ${primaryProvider} 未配置或未启用`);

      // 尝试使用备用服务商
      if (emailConfig.fallbackProvider) {
        const fallbackConfig = emailConfig[emailConfig.fallbackProvider];
        if (fallbackConfig.apiKey && fallbackConfig.enabled) {
          console.log(`切换到备用服务商: ${emailConfig.fallbackProvider}`);
          return await sendEmailWithProvider(to, subject, html, emailConfig.fallbackProvider, fallbackConfig.apiKey, fallbackConfig.fromEmail);
        }
      }

      return false;
    }

    // 使用主要服务商发送
    let result = await sendEmailWithProvider(to, subject, html, primaryProvider, primaryConfig.apiKey, primaryConfig.fromEmail);

    // 如果主要服务商失败，尝试备用服务商
    if (!result && emailConfig.fallbackProvider) {
      const fallbackConfig = emailConfig[emailConfig.fallbackProvider];
      if (fallbackConfig.apiKey && fallbackConfig.enabled) {
        console.log(`主服务商失败，切换到备用服务商: ${emailConfig.fallbackProvider}`);
        result = await sendEmailWithProvider(to, subject, html, emailConfig.fallbackProvider, fallbackConfig.apiKey, fallbackConfig.fromEmail);
      }
    }

    return result;
  } catch (error) {
    logger.error('邮件发送失败', error);
    return false;
  }
};

/**
 * 根据服务商发送邮件
 */
const sendEmailWithProvider = async (to: string, subject: string, html: string, provider: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  switch (provider) {
    case 'resend':
      return await sendEmailWithResend(to, subject, html, apiKey, fromEmail);
    case 'brevo':
      return await sendEmailWithBrevo(to, subject, html, apiKey, fromEmail);
    case 'mailersend':
      return await sendEmailWithMailerSend(to, subject, html, apiKey, fromEmail);
    default:
      console.error(`不支持的邮件服务商: ${provider}`);
      return false;
  }
};

// 邮箱验证token功能已移除，现在使用验证码验证方式

/**
 * 生成密码重置 token
 */
export const generatePasswordResetToken = (userId: string): string => {
  return jwt.sign(
    {
      userId,
      purpose: 'password-reset',
      jti: uuidv4() // 添加唯一标识，支持token撤销
    },
    JWT_RESET_SECRET, // 使用专用密钥
    { expiresIn: '1h' }
  );
};

// 邮箱验证邮件功能已移除，现在使用验证码验证方式

/**
 * 发送密码重置邮件
 */
export const sendPasswordResetEmail = async (email: string, userId: string, language: 'zh' | 'en' = 'zh'): Promise<boolean> => {
  try {
    // 获取系统设置
    const settings = await systemSettingsService.getSystemSettings();
    const siteName = settings.siteInfo.siteName || 'HAEHUB';
    const copyright = settings.siteInfo.copyright || 'Copyright © 2025 Haehub Inc. All rights reserved.';

    // 生成重置密码 token
    const resetToken = generatePasswordResetToken(userId);

    // 保存token到数据库（一次性使用）
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1小时后过期
    await PasswordResetToken.createResetToken(
      new mongoose.Types.ObjectId(userId),
      resetToken,
      expiresAt
    );

    // 构建重置密码链接（包含语言参数）
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}&lang=${language}`;

    // 构建邮件内容（支持多语言）
    const content = {
      zh: {
        subject: `${siteName} - 重置您的密码`,
        title: '密码重置',
        greeting: '密码重置请求',
        instruction: '您收到这封邮件是因为您（或其他人）请求重置您的账号密码。请点击下方按钮重置密码：',
        buttonText: '重置密码',
        importantNote: '安全提示：',
        validityNote: '此链接将在 1 小时后失效',
        securityNote: '如果您没有请求重置密码，请忽略此邮件',
        unchangedNote: '您的密码将保持不变，直到您点击上方按钮',
        fallbackText: '如果按钮无法点击，请复制以下链接到浏览器地址栏：',
        footer: `此邮件由系统自动发送，请勿回复<br>${copyright}`
      },
      en: {
        subject: `${siteName} - Reset Your Password`,
        title: 'Password Reset',
        greeting: 'Password Reset Request',
        instruction: 'You received this email because you (or someone else) requested to reset your account password. Please click the button below to reset your password:',
        buttonText: 'Reset Password',
        importantNote: 'Security Notice:',
        validityNote: 'This link will expire in 1 hour',
        securityNote: 'If you did not request a password reset, please ignore this email',
        unchangedNote: 'Your password will remain unchanged until you click the button above',
        fallbackText: 'If the button doesn\'t work, please copy the following link to your browser:',
        footer: `This email is sent automatically by the system, please do not reply<br>${copyright}`
      }
    };

    const text = content[language];
    const subject = text.subject;

    const html = `
      <!DOCTYPE html>
      <html lang="${language}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px;">

          <!-- Header -->
          <div style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #333333; margin: 0 0 8px 0; font-size: 24px; font-weight: 600;">
              ${siteName}
            </h1>
            <p style="color: #666666; margin: 0; font-size: 16px;">
              ${text.title}
            </p>
          </div>

          <!-- Main content -->
          <div style="margin-bottom: 40px;">
            <p style="color: #333333; font-size: 16px; line-height: 1.5; margin: 0 0 20px 0;">
              ${text.greeting}
            </p>
            <p style="color: #333333; font-size: 16px; line-height: 1.5; margin: 0 0 30px 0;">
              ${text.instruction}
            </p>

            <!-- Reset button -->
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}"
                 style="background-color: #dc2626;
                        color: white;
                        padding: 12px 24px;
                        text-decoration: none;
                        border-radius: 6px;
                        display: inline-block;
                        font-size: 16px;
                        font-weight: 500;">
                ${text.buttonText}
              </a>
            </div>

            <!-- Important notice -->
            <div style="background-color: #f8f9fa;
                        border-radius: 8px;
                        padding: 20px;
                        margin: 30px 0;">
              <p style="color: #333333; margin: 0 0 12px 0; font-size: 14px; font-weight: 600;">
                ${text.importantNote}
              </p>
              <ul style="color: #666666; margin: 0; padding: 0 0 0 20px; font-size: 14px; line-height: 1.6;">
                <li style="margin-bottom: 4px;">
                  ${text.validityNote}
                </li>
                <li style="margin-bottom: 4px;">
                  ${text.securityNote}
                </li>
                <li style="margin-bottom: 0;">
                  ${text.unchangedNote}
                </li>
              </ul>
            </div>

            <!-- Fallback link -->
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 30px 0;">
              <p style="color: #666666; font-size: 12px; margin: 0 0 8px 0;">
                ${text.fallbackText}
              </p>
              <div style="word-break: break-all; word-wrap: break-word; padding: 12px; background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 12px; color: #333333;">
                ${resetUrl}
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="color: #999999; font-size: 12px; margin: 0; line-height: 1.5;">
              ${text.footer}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    // 使用统一邮件发送函数
    return await sendEmail(email, subject, html);
  } catch (error) {
    logger.error('发送密码重置邮件失败', error);
    return false;
  }
};

// 邮箱验证token验证功能已移除，现在使用验证码验证方式

/**
 * 验证密码重置 token（仅验证，不标记为已使用）
 */
export const checkPasswordResetToken = async (token: string): Promise<{ userId: string } | null> => {
  try {
    // 首先验证JWT token的有效性
    const decoded = jwt.verify(token, JWT_RESET_SECRET) as { userId: string; purpose: string; jti?: string };

    // 验证 token 的用途是否为密码重置
    if (decoded.purpose !== 'password-reset') {
      return null;
    }

    // 仅检查token是否有效，不标记为已使用
    const tokenRecord = await PasswordResetToken.findOne({
      token,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!tokenRecord) {
      return null; // token不存在、已使用或已过期
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 验证密码重置 token（验证并标记为已使用）
 */
export const verifyPasswordResetToken = async (token: string): Promise<{ userId: string } | null> => {
  try {
    // 首先验证JWT token的有效性
    const decoded = jwt.verify(token, JWT_RESET_SECRET) as { userId: string; purpose: string; jti?: string };

    // 验证 token 的用途是否为密码重置
    if (decoded.purpose !== 'password-reset') {
      return null;
    }

    // 验证并使用一次性token（从数据库中检查并标记为已使用）
    const tokenRecord = await PasswordResetToken.validateAndUseToken(token);

    if (!tokenRecord) {
      return null; // token不存在、已使用或已过期
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 获取邮件模板内容
 */
const getEmailTemplate = async (code: string, purpose: 'email-verification', language: 'zh' | 'en' = 'zh') => {
  // 获取系统设置中的网站名称和版权信息
  const settings = await systemSettingsService.getSystemSettings();
  const siteName = settings.siteInfo.siteName || 'HAEHUB';
  const copyright = settings.siteInfo.copyright || 'Copyright © 2025 Haehub Inc. All rights reserved.';
  const templates = {
    zh: {
      'email-verification': {
        subject: `${siteName} - 邮箱验证码`,
        platformName: siteName,
        title: '邮箱验证',
        greeting: '感谢您注册我们的服务',
        instruction: '请使用以下验证码完成邮箱验证：',
        importantNote: '重要提示：',
        validityNote: '验证码有效期为 <strong>10 分钟</strong>',
        securityNote: '请勿将验证码告诉他人',
        ignoreNote: '如果您没有注册我们的服务，请忽略此邮件',
        footer: `此邮件由系统自动发送，请勿回复<br>${copyright}`
      },
      // 密码重置不使用验证码，使用专门的 sendPasswordResetEmail 函数
    },
    en: {
      'email-verification': {
        subject: `${siteName} - Email Verification Code`,
        platformName: siteName,
        title: 'Email Verification',
        greeting: 'Thank you for registering with our service',
        instruction: 'Please use the following verification code to complete email verification:',
        importantNote: 'Important Notice:',
        validityNote: 'Verification code is valid for <strong>10 minutes</strong>',
        securityNote: 'Do not share this verification code with others',
        ignoreNote: 'If you did not register with our service, please ignore this email',
        footer: `This email is sent automatically by the system, please do not reply<br>${copyright}`
      },
      // 密码重置不使用验证码，使用专门的 sendPasswordResetEmail 函数
    }
  };

  const template = templates[language][purpose];

  return {
    subject: template.subject,
    html: `
      <!DOCTYPE html>
      <html lang="${language}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${template.subject}</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px;">

          <!-- Header -->
          <div style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #333333; margin: 0 0 8px 0; font-size: 24px; font-weight: 600;">
              ${template.platformName}
            </h1>
            <p style="color: #666666; margin: 0; font-size: 16px;">
              ${template.title}
            </p>
          </div>

          <!-- Main content -->
          <div style="margin-bottom: 40px;">
            <p style="color: #333333; font-size: 16px; line-height: 1.5; margin: 0 0 20px 0;">
              ${template.greeting}
            </p>
            <p style="color: #333333; font-size: 16px; line-height: 1.5; margin: 0 0 30px 0;">
              ${template.instruction}
            </p>

            <!-- Verification code -->
            <div style="text-align: center; margin: 30px 0;">
              <div style="font-size: 32px;
                         font-weight: bold;
                         color: #333333;
                         letter-spacing: 4px;
                         font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                         margin: 0;">
                ${code}
              </div>
            </div>

            <!-- Important notice -->
            <div style="background-color: #f8f9fa;
                        border-radius: 8px;
                        padding: 20px;
                        margin: 30px 0;">
              <p style="color: #333333; margin: 0 0 12px 0; font-size: 14px; font-weight: 600;">
                ${template.importantNote}
              </p>
              <ul style="color: #666666; margin: 0; padding: 0 0 0 20px; font-size: 14px; line-height: 1.6;">
                <li style="margin-bottom: 4px;">
                  ${template.validityNote}
                </li>
                <li style="margin-bottom: 4px;">
                  ${template.securityNote}
                </li>
                <li style="margin-bottom: 0;">
                  ${template.ignoreNote}
                </li>
              </ul>
            </div>
          </div>

          <!-- Footer -->
          <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="color: #999999; font-size: 12px; margin: 0; line-height: 1.5;">
              ${template.footer}
            </p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

/**
 * 发送验证码邮件
 */
export const sendVerificationCodeEmail = async (email: string, code: string, purpose: 'email-verification' = 'email-verification', language: 'zh' | 'en' = 'zh'): Promise<boolean> => {
  try {
    const { subject, html } = await getEmailTemplate(code, purpose, language);

    // 使用统一邮件发送函数
    const result = await sendEmail(email, subject, html);

    if (result) {
      logger.info(`验证码邮件发送成功: ${email}, 用途: ${purpose}`);
    } else {
      logger.error(`验证码邮件发送失败: ${email}, 用途: ${purpose}`);
    }

    return result;
  } catch (error) {
    logger.error('发送验证码邮件失败', error);
    return false;
  }
};

export default sendVerificationCodeEmail;