import { Router } from 'express';
import klineSSEService from '../services/klineSSEService';
import klineDataRefreshService from '../services/klineDataRefreshService';
import klineMemoryCache from '../services/klineMemoryCache';
import sseEventManager from '../services/sseEventManager';
import logger from '../utils/logger';

// 创建路由实例
const router = Router();

/**
 * K线SSE连接端点
 * GET /api/kline-sse/stream
 * 
 * 建立SSE连接，接收实时K线数据推送
 * 支持token参数进行用户身份验证（可选）
 */
router.get('/stream', async (req, res) => {
  try {
    await klineSSEService.createConnection(req, res);
  } catch (error) {
    logger.error('创建K线SSE连接失败', error);
    res.status(500).json({ error: '创建SSE连接失败' });
  }
});

/**
 * K线SSE服务状态查询
 * GET /api/kline-sse/status
 * 
 * 获取SSE服务的运行状态和统计信息
 */
router.get('/status', (_req, res) => {
  try {
    const refreshServiceStatus = klineDataRefreshService.getStatus();
    const cacheStats = klineMemoryCache.getStats();
    const sseStats = klineSSEService.getStats();
    const eventStats = sseEventManager.getStats();

    const status = {
      service: {
        name: '统一SSE服务',
        status: 'running',
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      },
      dataRefresh: {
        isRunning: refreshServiceStatus.isRunning,
        refreshInterval: refreshServiceStatus.refreshInterval,
        cacheTTL: refreshServiceStatus.cacheTTL,
        lastRefreshTime: refreshServiceStatus.lastRefreshTime
      },
      cache: cacheStats,
      sse: sseStats,
      events: eventStats,
      performance: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    };

    res.json(status);
  } catch (error) {
    logger.error('获取K线SSE状态失败', error);
    res.status(500).json({ error: '获取状态失败' });
  }
});



// 导出路由
export default router;
