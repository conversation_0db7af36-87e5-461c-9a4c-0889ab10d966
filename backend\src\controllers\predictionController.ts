import { Request, Response } from 'express';
import predictionService from '../services/predictionService';

/**
 * 预测数据控制器类
 * 处理与预测数据相关的HTTP请求
 */
class PredictionController {
  /**
   * 获取最近的预测数据
   * 获取并返回指定交易对的最近预测数据
   *
   * @param req HTTP请求对象
   * @param res HTTP响应对象
   */
  async getRecentPredictions(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user; // 从可选认证中间件获取用户信息

      // 检查用户权限 - 只有认证用户且非普通用户才能获取预测K线数据
      // 与预测折线保持一致的权限控制
      if (!user) {
        res.status(200).json([]);
        return;
      }

      if (user.role === 'normal') {
        res.status(200).json([]);
        return;
      }

      // 获取查询参数，设置默认值
      const symbol = req.query.symbol as string || 'BTCUSDT';

      // 确保limit是有效的数字，默认值480
      const limit = req.query.limit ?
        Math.min(1000, Math.max(1, parseInt(req.query.limit as string))) : 480;

      const endTime = req.query.endTime ? parseInt(req.query.endTime as string) : undefined;



      // 获取预测数据
      const predictions = await predictionService.getRecentPredictions(symbol, limit, endTime);

      // 转换为前端所需格式
      const formattedPredictions = predictions.map(prediction => ({
        time: parseInt(String(prediction.targetStartTime)) / 1000,  // 转换为秒级时间戳
        open: parseFloat(prediction.open),                          // 转换为数字
        high: parseFloat(prediction.high),
        low: parseFloat(prediction.low),
        close: parseFloat(prediction.close),
        isActive: prediction.isActive,                              // 是否为活跃预测
        targetStartTime: prediction.targetStartTime,                // 目标开始时间
        targetEndTime: prediction.targetEndTime,                    // 目标结束时间
        predictionTime: prediction.predictionTime                   // 预测生成时间
      }));



      // 返回JSON格式响应
      res.json(formattedPredictions);
    } catch (error) {
      console.error('获取预测数据时出错:', error);
      res.status(500).json({ error: '获取预测数据失败' });
    }
  }

  // 注意：triggerPrediction 方法已删除
  // 原因：前端未使用此接口，预测生成已通过定时任务自动化
}

// 导出控制器实例
export default new PredictionController(); 