/**
 * 用户类型定义
 */
export interface User {
  _id: string;
  email: string;
  username?: string;
  role: 'trial' | 'normal' | 'subscriber' | 'admin' | 'banned';
  trialEndsAt: string;
  isVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
  lastActiveAt?: string;
  isOnline?: boolean;
  invitedBy?: string;
  subscription?: {
    plan: 'monthly' | 'quarterly' | 'yearly';
    startDate: string;
    endDate: string;
    status: 'active' | 'expired' | 'cancelled';
    paymentId?: string;
  };
  rewardHistory?: Array<{
    type: 'invite' | 'other';
    days: number;
    grantedAt: string;
    reason: string;
  }>;
}

/**
 * 用户状态接口
 */
export interface UserState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isInitializing: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string, inviteCode?: string) => Promise<void>;
  // verifyEmail方法已移除，现在使用验证码验证方式
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  changePassword: (oldPassword: string, newPassword: string) => Promise<void>;
  fetchUserInfo: () => Promise<void>;
  clearError: () => void;
  refreshToken: () => Promise<boolean>;
}
