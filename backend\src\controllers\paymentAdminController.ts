import { Request, Response } from 'express';
import Payment from '../models/Payment';
import User from '../models/User';
import mongoose from 'mongoose';

/**
 * 获取所有支付记录（分页）
 * GET /api/admin/payments
 */
export const getPayments = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string || '';
    const status = req.query.status as string;
    const plan = req.query.plan as string;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const filter: any = {};
    
    // 搜索用户邮箱
    if (search) {
      filter.userEmail = { $regex: search, $options: 'i' };
    }
    
    // 筛选支付状态
    if (status) {
      filter.paymentStatus = status;
    }
    
    // 筛选订阅计划
    if (plan) {
      filter.plan = plan;
    }
    
    // 筛选日期范围
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1); // 包含结束日期
        filter.createdAt.$lt = endDateObj;
      }
    }

    // 获取支付记录总数
    const total = await Payment.countDocuments(filter);

    // 获取支付记录
    const payments = await Payment.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    res.json({
      success: true,
      payments,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取支付记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取支付详情
 * GET /api/admin/payments/:id
 */
export const getPaymentDetails = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的支付ID'
      });
    }

    // 获取支付记录
    const payment = await Payment.findById(id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: '支付记录不存在'
      });
    }

    // 获取关联的用户信息
    const user = await User.findById(payment.userId).select('_id email username role');

    res.json({
      success: true,
      payment,
      user
    });
  } catch (error) {
    console.error('获取支付详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新支付状态
 * PATCH /api/admin/payments/:id
 */
export const updatePaymentStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { paymentStatus } = req.body;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的支付ID'
      });
    }

    // 验证状态
    const validStatuses = ['pending', 'confirming', 'confirmed', 'sending', 'partially_paid', 'finished', 'failed', 'refunded', 'expired'];
    if (!validStatuses.includes(paymentStatus)) {
      return res.status(400).json({
        success: false,
        message: '无效的支付状态'
      });
    }

    // 获取支付记录
    const payment = await Payment.findById(id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: '支付记录不存在'
      });
    }

    // 更新状态
    payment.paymentStatus = paymentStatus;
    payment.updatedAt = new Date();
    await payment.save();

    // 如果支付状态为完成，更新用户订阅
    if (['confirmed', 'finished'].includes(paymentStatus) && !['confirmed', 'finished'].includes(payment.paymentStatus)) {
      const user = await User.findById(payment.userId);
      if (user) {
        const duration = getDurationDays(payment.plan);
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + duration);

        user.role = 'subscriber';
        user.subscription = {
          plan: payment.plan as any,
          startDate,
          endDate,
          status: 'active',
          paymentId: payment.paymentId
        };

        await user.save();
      }
    }

    res.json({
      success: true,
      payment
    });
  } catch (error) {
    console.error('更新支付状态错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取支付统计数据
 * GET /api/admin/payments/stats
 */
export const getPaymentStats = async (req: Request, res: Response) => {
  try {
    // 获取总支付数
    const totalPayments = await Payment.countDocuments();
    
    // 获取成功支付数
    const successfulPayments = await Payment.countDocuments({
      paymentStatus: { $in: ['confirmed', 'finished'] }
    });
    
    // 获取待处理支付数
    const pendingPayments = await Payment.countDocuments({
      paymentStatus: { $in: ['pending', 'confirming', 'sending', 'partially_paid'] }
    });
    
    // 获取失败支付数
    const failedPayments = await Payment.countDocuments({
      paymentStatus: { $in: ['failed', 'refunded', 'expired'] }
    });
    
    // 获取总收入
    const totalRevenue = await Payment.aggregate([
      {
        $match: {
          paymentStatus: { $in: ['confirmed', 'finished'] }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    // 获取各订阅计划的支付数量
    const planStats = await Payment.aggregate([
      {
        $match: {
          paymentStatus: { $in: ['confirmed', 'finished'] }
        }
      },
      {
        $group: {
          _id: '$plan',
          count: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      }
    ]);
    
    // 获取最近30天的支付趋势
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const dailyStats = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 },
          revenue: { $sum: '$amount' },
          successful: {
            $sum: {
              $cond: [
                { $in: ['$paymentStatus', ['confirmed', 'finished']] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1,
          '_id.day': 1
        }
      }
    ]);
    
    // 格式化日期数据
    const dailyTrends = dailyStats.map(day => ({
      date: `${day._id.year}-${day._id.month.toString().padStart(2, '0')}-${day._id.day.toString().padStart(2, '0')}`,
      count: day.count,
      revenue: day.revenue,
      successful: day.successful
    }));

    res.json({
      success: true,
      stats: {
        totalPayments,
        successfulPayments,
        pendingPayments,
        failedPayments,
        totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
        planStats,
        dailyTrends
      }
    });
  } catch (error) {
    console.error('获取支付统计错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

// 辅助函数：获取订阅计划的天数
const getDurationDays = (plan: string): number => {
  switch (plan) {
    case 'monthly':
      return 30;
    case 'quarterly':
      return 90;
    case 'yearly':
      return 365;
    default:
      return 0;
  }
};

export default {
  getPayments,
  getPaymentDetails,
  updatePaymentStatus,
  getPaymentStats
}; 