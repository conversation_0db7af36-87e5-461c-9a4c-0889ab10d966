import { useState, useCallback, useRef, useEffect } from 'react';
import { fetchPredictions } from '../services/api';
import { createCacheManager } from '../utils/cacheManager';
import { useSSEConnection } from './useSSEConnection';
import useUserStore from '../store/useUserStore';

interface PredictionDataState {
  data: any[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

/**
 * 预测K线数据获取和管理Hook (SSE版本)
 * 使用SSE实时推送获取预测K线数据，替代轮询方式
 * @returns 预测K线数据状态和操作方法
 */
export const usePredictionDataSSE = () => {
  // 缓存管理器
  const cacheManagerRef = useRef(createCacheManager('预测K线'));
  const cacheManager = cacheManagerRef.current;

  // 状态管理
  const [state, setState] = useState<PredictionDataState>({
    data: [],
    isLoading: true,
    isLoadingMore: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 用户认证状态
  const { isAuthenticated, user } = useUserStore();

  // 使用统一SSE连接获取数据库更新通知
  const {
    connectionStatus,
    error: sseError,
    databaseUpdateTrigger
  } = useSSEConnection();

  // 权限状态缓存
  const permissionStatusRef = useRef<{ hasPermission: boolean; lastUser: string | null }>({
    hasPermission: false,
    lastUser: null
  });

  // 检查用户是否有预测K线访问权限
  const hasPermission = useCallback(() => {
    const currentUserKey = isAuthenticated && user ? `${user.email}-${user.role}` : null;

    // 如果用户状态没有变化，直接返回缓存结果
    if (permissionStatusRef.current.lastUser === currentUserKey) {
      return permissionStatusRef.current.hasPermission;
    }

    let hasAccess = false;

    if (!isAuthenticated || !user) {
      if (permissionStatusRef.current.lastUser !== null) {
        console.log('[预测K线权限] 用户未认证');
      }
    } else if (user.role === 'normal') {
      if (permissionStatusRef.current.lastUser !== currentUserKey) {
        console.log(`[预测K线权限] 用户 ${user.email} 权限不足 (${user.role})`);
      }
    } else {
      hasAccess = true;
      if (permissionStatusRef.current.lastUser !== currentUserKey) {
        console.log(`[预测K线权限] 用户 ${user.email} 有权限访问 (${user.role})`);
      }
    }

    // 更新缓存
    permissionStatusRef.current = {
      hasPermission: hasAccess,
      lastUser: currentUserKey
    };

    return hasAccess;
  }, [isAuthenticated, user]);

  // 初始加载预测数据
  const loadData = useCallback(async () => {
    if (isRefreshingRef.current) return;

    // 检查权限
    if (!hasPermission()) {
      console.log('[预测K线] 用户无权限，跳过数据加载');
      setState(prev => ({
        ...prev,
        data: [],
        isLoading: false,
        error: null
      }));
      return;
    }

    isRefreshingRef.current = true;
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const predictionsResponse = await fetchPredictions();
      if (predictionsResponse && Array.isArray(predictionsResponse) && predictionsResponse.length > 0) {
        console.log(`获取到 ${predictionsResponse.length} 条预测数据`);

        // 与现有数据合并去重
        setState(prev => {
          const existingData = cacheManager.getAllValidData();
          const uniqueMap = new Map();

          // 先添加现有数据
          existingData.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });

          // 再添加新数据，覆盖重复时间戳
          predictionsResponse.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });

          // 排序并更新缓存
          const deduplicatedData = Array.from(uniqueMap.values()).sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });

          console.log(`预测K线数据去重: ${existingData.length} + ${predictionsResponse.length} -> ${deduplicatedData.length} 条`);

          // 清理缓存并添加去重后的数据
          cacheManager.clearAll();
          const cachedData = cacheManager.addData(deduplicatedData);
          console.log('预测K线缓存统计:', cacheManager.getCacheStats());

          return {
            ...prev,
            data: cachedData,
            error: null
          };
        });
      } else {
        console.warn('获取到的预测数据为空或无效');
        setState(prev => ({
          ...prev,
          data: [],
          error: null
        }));
      }
    } catch (err) {
      console.error('预测数据加载失败:', err);
      setState(prev => ({
        ...prev,
        error: '获取预测数据失败'
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
      isRefreshingRef.current = false;
    }
  }, [hasPermission, cacheManager]);

  // SSE预测数据推送已移除，改为仅通过数据库更新通知重新加载数据

  // 加载更多历史预测数据
  const loadMoreHistory = useCallback(async (oldestTime?: number) => {
    if (state.isLoadingMore || !hasPermission()) return;

    setState(prev => ({ ...prev, isLoadingMore: true }));

    try {
      let endTime: number;

      if (oldestTime) {
        // 如果提供了oldestTime参数，使用该参数
        endTime = oldestTime;
      } else {
        // 否则从当前数据中计算最早时间
        const currentData = cacheManager.getAllValidData();
        if (currentData.length === 0) {
          setState(prev => ({ ...prev, isLoadingMore: false }));
          return;
        }

        // 获取最早的时间戳作为endTime
        endTime = Math.min(...currentData.map(item => {
          const time = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
          return time * 1000; // 转换为毫秒
        }));
      }

      console.log(`加载更多预测数据，endTime: ${new Date(endTime).toISOString()}`);

      const moreData = await fetchPredictions(endTime);
      if (moreData && Array.isArray(moreData) && moreData.length > 0) {
        console.log(`加载到 ${moreData.length} 条更多预测数据`);

        // 合并数据
        const currentData = cacheManager.getAllValidData();
        const allData = [...moreData, ...currentData];
        const cachedData = cacheManager.addData(allData);

        setState(prev => ({
          ...prev,
          data: cachedData
        }));
      } else {
        console.log('没有更多预测数据可加载');
      }
    } catch (err) {
      console.error('加载历史预测数据失败:', err);
    } finally {
      setState(prev => ({ ...prev, isLoadingMore: false }));
    }
  }, [state.isLoadingMore, hasPermission, cacheManager]);

  // SSE预测数据监听已移除

  // 首次加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 监听数据库更新通知，智能合并新数据
  useEffect(() => {
    if (databaseUpdateTrigger > 0) {
      console.log('[预测K线] 收到数据库更新通知，重新加载数据');
      loadData(); // 重新加载数据以获取数据库中的新预测数据
    }
  }, [databaseUpdateTrigger, loadData]);

  // 监听用户状态变化，当用户重新获得权限时重新加载数据
  useEffect(() => {
    if (hasPermission() && state.data.length === 0 && !state.isLoading) {
      console.log('[预测K线] 用户权限状态变化，重新加载数据');
      loadData();
    }
  }, [hasPermission, state.data.length, state.isLoading, loadData]);

  return {
    predictionData: state.data,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,

    // 分离错误状态
    dataError: state.error,        // 业务数据错误
    connectionError: sseError,     // SSE连接错误
    error: state.error || sseError, // 保持向后兼容

    loadData,
    loadMoreHistory,

    // SSE相关状态
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    hasPermission: hasPermission()
  };
};
