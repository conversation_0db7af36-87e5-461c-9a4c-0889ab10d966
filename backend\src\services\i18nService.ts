import fs from 'fs';
import path from 'path';

// 翻译数据类型
interface TranslationData {
  [key: string]: string | TranslationData;
}

// 支持的语言
type SupportedLanguage = 'zh' | 'en';

/**
 * 国际化翻译服务
 * 负责加载翻译文件和提供翻译功能
 */
class I18nService {
  private translations: Record<SupportedLanguage, TranslationData> = {
    zh: {},
    en: {}
  };

  private loaded = false;

  /**
   * 初始化翻译服务，加载翻译文件
   */
  public async init(): Promise<void> {
    if (this.loaded) return;

    try {
      // 加载中文翻译
      const zhPath = path.join(__dirname, '../i18n/zh.json');
      const zhContent = fs.readFileSync(zhPath, 'utf-8');
      this.translations.zh = JSON.parse(zhContent);

      // 加载英文翻译
      const enPath = path.join(__dirname, '../i18n/en.json');
      const enContent = fs.readFileSync(enPath, 'utf-8');
      this.translations.en = JSON.parse(enContent);

      this.loaded = true;
      console.log('I18n service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize i18n service:', error);
      // 即使加载失败也标记为已加载，避免重复尝试
      this.loaded = true;
    }
  }

  /**
   * 获取翻译文本
   * @param key 翻译键，支持点号分隔的嵌套路径，如 'auth.login_success'
   * @param language 语言代码
   * @param params 参数对象，用于替换翻译文本中的占位符
   * @returns 翻译后的文本
   */
  public t(key: string, language: SupportedLanguage = 'zh', params?: Record<string, any>): string {
    // 确保翻译数据已加载
    if (!this.loaded) {
      console.warn('I18n service not initialized, using key as fallback');
      return key;
    }

    // 获取指定语言的翻译数据
    const langData = this.translations[language];
    if (!langData) {
      console.warn(`Language '${language}' not supported, falling back to key`);
      return key;
    }

    // 通过点号分隔的路径获取翻译文本
    const translation = this.getNestedValue(langData, key);
    
    if (typeof translation !== 'string') {
      console.warn(`Translation not found for key '${key}' in language '${language}'`);
      return key;
    }

    // 如果有参数，替换占位符
    if (params) {
      return this.interpolate(translation, params);
    }

    return translation;
  }

  /**
   * 从嵌套对象中获取值
   * @param obj 对象
   * @param path 点号分隔的路径
   * @returns 值或undefined
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 替换字符串中的占位符
   * @param template 模板字符串
   * @param params 参数对象
   * @returns 替换后的字符串
   */
  private interpolate(template: string, params: Record<string, any>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  /**
   * 检查翻译键是否存在
   * @param key 翻译键
   * @param language 语言代码
   * @returns 是否存在
   */
  public hasTranslation(key: string, language: SupportedLanguage = 'zh'): boolean {
    if (!this.loaded) return false;
    
    const langData = this.translations[language];
    if (!langData) return false;

    const translation = this.getNestedValue(langData, key);
    return typeof translation === 'string';
  }

  /**
   * 获取所有支持的语言
   * @returns 支持的语言数组
   */
  public getSupportedLanguages(): SupportedLanguage[] {
    return ['zh', 'en'];
  }
}

// 创建单例实例
const i18nService = new I18nService();

export default i18nService;
export { SupportedLanguage };
