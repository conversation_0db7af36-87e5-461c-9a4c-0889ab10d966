import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import configService from '../../services/configService';

const DocsFooter: React.FC = () => {
  const { t } = useTranslation();
  const [copyright, setCopyright] = useState('');

  useEffect(() => {
    const loadCopyright = async () => {
      try {
        const copyrightText = await configService.getCopyright();
        setCopyright(copyrightText);
      } catch (error) {
        console.error('获取版权信息失败:', error);
        // 使用默认版权信息
        const currentYear = new Date().getFullYear();
        setCopyright(`© ${currentYear} ${t('app.title')}.`);
      }
    };
    loadCopyright();
  }, [t]);

  return (
    <footer className="bg-white border-t border-gray-200 py-6 mt-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <p className="text-sm text-gray-500">
            {copyright}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default DocsFooter;
