import React, { useState, useEffect } from 'react';
import useAdminStore from '../../store/useAdminStore';
import { useToast } from '../ui/use-toast';

interface User {
  _id: string;
  email: string;
  subscription?: {
    plan?: string;
    status?: string;
    endDate?: string;
  };
}

interface SubscriptionEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  user: User | null;
}

const SubscriptionEditModal: React.FC<SubscriptionEditModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  user
}) => {
  const { updateUserSubscription } = useAdminStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // 表单数据
  const [formData, setFormData] = useState({
    plan: 'monthly',
    status: 'active',
    endDate: ''
  });

  // 当用户数据更新时，设置表单初始值
  useEffect(() => {
    if (user && user.subscription) {
      setFormData({
        plan: user.subscription.plan || 'monthly',
        status: user.subscription.status || 'active',
        endDate: user.subscription.endDate ? new Date(user.subscription.endDate).toISOString().split('T')[0] : getDefaultEndDate('monthly')
      });
    } else {
      // 默认值
      setFormData({
        plan: 'monthly',
        status: 'active',
        endDate: getDefaultEndDate('monthly')
      });
    }
  }, [user]);

  // 获取默认的结束日期（基于计划类型）
  const getDefaultEndDate = (planType: string) => {
    const now = new Date();
    let endDate = new Date(now);
    
    switch(planType) {
      case 'monthly':
        endDate.setMonth(now.getMonth() + 1);
        break;
      case 'quarterly':
        endDate.setMonth(now.getMonth() + 3);
        break;
      case 'yearly':
        endDate.setFullYear(now.getFullYear() + 1);
        break;
      default:
        endDate.setMonth(now.getMonth() + 1);
    }
    
    return endDate.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
  };

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'plan') {
      // 如果改变了计划类型，自动更新结束日期
      setFormData(prev => ({
        ...prev,
        [name]: value,
        endDate: getDefaultEndDate(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) return;
    
    try {
      setIsLoading(true);
      setError('');
      
      await updateUserSubscription(user._id, formData);
      
      toast({
        title: '用户订阅已更新',
        variant: 'default'
      });
      setIsLoading(false);
      onSuccess();
      onClose();
    } catch (err) {
      const message = err instanceof Error ? err.message : '更新订阅失败';
      setError(message);
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-background-card rounded-lg shadow-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-content-primary">
            {user?.subscription ? '编辑订阅' : '添加订阅'}
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-error/10 border-l-4 border-red-500 text-red-700 p-4 mb-4">
            <p>{error}</p>
          </div>
        )}

        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            为用户 <span className="font-semibold">{user?.email}</span> 设置订阅信息
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
              订阅计划
            </label>
            <select
              name="plan"
              value={formData.plan}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
            >
              <option value="monthly">月度</option>
              <option value="quarterly">季度</option>
              <option value="yearly">年度</option>
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
              订阅状态
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
            >
              <option value="active">有效</option>
              <option value="expired">已过期</option>
              <option value="cancelled">已取消</option>
            </select>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
              结束日期
            </label>
            <input
              type="date"
              name="endDate"
              value={formData.endDate}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SubscriptionEditModal; 