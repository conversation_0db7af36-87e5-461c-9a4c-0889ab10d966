import React, { useCallback, useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ChartComposite from './chart/ChartComposite';
import { IChartApi, ISeriesApi } from 'lightweight-charts';
import { useKLineData } from '../hooks/useKLineData';
import { usePredictionDataSSE } from '../hooks/usePredictionDataSSE';
import { useChartType } from '../hooks/useChartType';
import { usePredictionSettings } from '../hooks/usePredictionSettings';

// 组件属性接口定义
interface MainKlineChartProps {
  showLoadingIndicator?: boolean; // 是否显示加载指示器
  symbol?: string; // 交易对名称
}

/**
 * 主K线图表组件
 * 使用SSE实时推送获取K线数据和预测数据
 */
const MainKlineChart: React.FC<MainKlineChartProps> = ({
  showLoadingIndicator = true,
  symbol = 'BTC/USDT',
}) => {
  const { t } = useTranslation(); // 国际化翻译钩子
  
  // 使用SSE获取K线数据
  const {
    candleData,
    isLoading,
    isLoadingMoreCandles,
    error,
    loadMoreHistory: loadMoreHistoryCandleData
  } = useKLineData();
  
  // 使用SSE Hook管理预测数据
  const {
    predictionData,
    isLoadingMore: isLoadingMorePredictions,
    loadMoreHistory: loadMoreHistoryData
  } = usePredictionDataSSE();
  
  // 使用自定义Hook管理图表类型
  const {
    chartType,
    showPrediction,
    showPredictionLine,
    handleChartTypeChange
  } = useChartType();

  // 使用自定义Hook获取预测图设置
  const { settings: predictionSettings } = usePredictionSettings();
  
  // 图表引用 - 不再需要LineChartService
  const chartApiRef = useRef<IChartApi | null>(null);
  const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  
  // 状态标志
  const [isChartReady, setIsChartReady] = useState(false);
  const [hasData, setHasData] = useState(false);

  // 监控数据状态变化
  useEffect(() => {
    setHasData(candleData.length > 0);
  }, [candleData]);

  // 图表就绪处理函数
  const handleChartReady = useCallback((chart: IChartApi, mainSeries: ISeriesApi<"Candlestick">) => {
    console.log('图表实例就绪');
    
    // 保存图表实例和主K线系列引用
    chartApiRef.current = chart;
    mainSeriesRef.current = mainSeries;
    setIsChartReady(true);
    
    // 不再需要初始化LineChartService，由图表管理器处理
  }, []);

  // 渲染图表组件
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <div className="flex space-x-2">
          {isLoading && showLoadingIndicator && (
            <span className="text-cyber-cyan animate-pulse font-mono">
              {t('loading')}...
            </span>
          )}
          {(isLoadingMorePredictions || isLoadingMoreCandles) && showLoadingIndicator && (
            <span className="text-cyber-cyan animate-pulse font-mono">
              {t('loading')} {t('chart.moreHistory')}...
            </span>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-cyber-pink/10 border border-cyber-pink/30 text-cyber-pink p-4 mb-4 rounded-xl font-mono backdrop-blur-sm">
          {error}
        </div>
      )}

      {/* 图表组件 */}
      <ChartComposite
        candleData={candleData}
        predictionData={predictionData}
        isLoading={isLoading}
        onLoadMoreHistory={loadMoreHistoryData}
        onLoadMoreCandleHistory={loadMoreHistoryCandleData}
        onChartReady={handleChartReady}
        showPrediction={showPrediction && predictionSettings.enableKlinePrediction}
        showPredictionLine={showPredictionLine && predictionSettings.enableLinePrediction}
        chartType={chartType}
        symbol={symbol}
        onChartTypeChange={handleChartTypeChange}
      />
    </div>
  );
};

export default MainKlineChart; 