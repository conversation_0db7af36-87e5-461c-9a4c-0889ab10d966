import VerificationCode, { VerificationCodeDocument } from '../models/VerificationCode';
import systemSettingsService from './systemSettingsService';
import logger from '../utils/logger';

export interface VerificationCodeResult {
  success: boolean;
  code?: string;
  message?: string;
  remainingAttempts?: number;
  expiresAt?: Date;
}

export interface VerificationResult {
  success: boolean;
  message?: string;
  remainingAttempts?: number;
}

class VerificationCodeService {
  /**
   * 生成6位数字验证码
   */
  generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 获取验证码配置
   */
  private async getVerificationConfig() {
    try {
      const settings = await systemSettingsService.getSystemSettings();
      return {
        codeLength: settings.verificationSettings.codeLength,
        expireMinutes: settings.verificationSettings.expireMinutes,
        maxAttempts: settings.verificationSettings.maxAttempts,
        resendIntervalSeconds: settings.verificationSettings.resendIntervalSeconds
      };
    } catch (error) {
      logger.error('获取验证码配置失败', error);
      // 返回默认配置
      return {
        codeLength: 6,
        expireMinutes: 10,
        maxAttempts: 5,
        resendIntervalSeconds: 60
      };
    }
  }

  /**
   * 创建验证码记录
   */
  async createVerificationCode(
    email: string, 
    purpose: 'email-verification' | 'password-reset', 
    userId?: string
  ): Promise<VerificationCodeResult> {
    try {
      const config = await this.getVerificationConfig();
      
      // 检查发送频率限制
      const rateLimitCheck = await this.checkRateLimit(email, purpose);
      if (!rateLimitCheck.success) {
        return rateLimitCheck;
      }

      // 生成验证码
      const code = this.generateCode();
      
      // 计算过期时间
      const expiresAt = new Date(Date.now() + config.expireMinutes * 60 * 1000);

      // 先将该邮箱和用途的旧验证码标记为已使用
      await VerificationCode.updateMany(
        {
          email: email.toLowerCase(),
          purpose,
          isUsed: false
        },
        {
          isUsed: true,
          updatedAt: new Date()
        }
      );

      // 创建新的验证码记录
      const verificationCode = new VerificationCode({
        email: email.toLowerCase(),
        code,
        purpose,
        userId: userId ? userId : undefined,
        expiresAt,
        maxAttempts: config.maxAttempts,
        attempts: 0,
        isUsed: false
      });

      await verificationCode.save();

      logger.info(`验证码已创建: ${email}, 用途: ${purpose}, 过期时间: ${expiresAt}`);

      return {
        success: true,
        code,
        expiresAt,
        message: '验证码已生成'
      };
    } catch (error) {
      logger.error('创建验证码失败', error);
      return {
        success: false,
        message: '创建验证码失败，请稍后重试'
      };
    }
  }

  /**
   * 验证验证码
   */
  async verifyCode(
    email: string, 
    code: string, 
    purpose: 'email-verification' | 'password-reset'
  ): Promise<VerificationResult> {
    try {
      // 查找有效的验证码
      const verificationCode = await VerificationCode.findValidCode(
        email.toLowerCase(), 
        code, 
        purpose
      );

      if (!verificationCode) {
        logger.warn(`验证码验证失败: ${email}, 验证码: ${code}, 用途: ${purpose} - 验证码不存在或已过期`);
        return {
          success: false,
          message: '验证码无效或已过期'
        };
      }

      // 检查是否已达到最大尝试次数
      if (verificationCode.isMaxAttemptsReached()) {
        logger.warn(`验证码验证失败: ${email} - 已达到最大尝试次数`);
        return {
          success: false,
          message: '验证码尝试次数已达上限，请重新获取验证码'
        };
      }

      // 检查验证码是否正确
      if (verificationCode.code !== code) {
        // 增加尝试次数
        await verificationCode.incrementAttempts();
        
        const remainingAttempts = verificationCode.maxAttempts - verificationCode.attempts;
        
        logger.warn(`验证码验证失败: ${email} - 验证码错误，剩余尝试次数: ${remainingAttempts}`);
        
        return {
          success: false,
          message: `验证码错误，还可尝试 ${remainingAttempts} 次`,
          remainingAttempts
        };
      }

      // 验证成功，标记为已使用
      await verificationCode.markAsUsed();

      logger.info(`验证码验证成功: ${email}, 用途: ${purpose}`);

      return {
        success: true,
        message: '验证码验证成功'
      };
    } catch (error) {
      logger.error('验证验证码失败', error);
      return {
        success: false,
        message: '验证失败，请稍后重试'
      };
    }
  }

  /**
   * 检查发送频率限制
   */
  async checkRateLimit(
    email: string, 
    purpose: 'email-verification' | 'password-reset'
  ): Promise<VerificationCodeResult> {
    try {
      const config = await this.getVerificationConfig();
      const limitMinutes = config.resendIntervalSeconds / 60;

      const recentCode = await VerificationCode.checkRateLimit(
        email.toLowerCase(), 
        purpose, 
        limitMinutes
      );

      if (recentCode) {
        const waitTime = Math.ceil(
          (recentCode.createdAt.getTime() + config.resendIntervalSeconds * 1000 - Date.now()) / 1000
        );
        
        if (waitTime > 0) {
          logger.warn(`发送频率限制: ${email} - 需等待 ${waitTime} 秒`);
          return {
            success: false,
            message: `请等待 ${waitTime} 秒后再重新发送验证码`
          };
        }
      }

      return {
        success: true
      };
    } catch (error) {
      logger.error('检查发送频率限制失败', error);
      return {
        success: false,
        message: '检查发送限制失败，请稍后重试'
      };
    }
  }

  /**
   * 清理过期验证码
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const result = await VerificationCode.cleanupExpired();
      logger.info(`清理过期验证码完成，删除了 ${result.deletedCount} 条记录`);
    } catch (error) {
      logger.error('清理过期验证码失败', error);
    }
  }

  /**
   * 获取用户未使用的验证码信息
   */
  async getUserActiveCode(
    email: string, 
    purpose: 'email-verification' | 'password-reset'
  ): Promise<VerificationCodeDocument | null> {
    try {
      return await VerificationCode.findOne({
        email: email.toLowerCase(),
        purpose,
        isUsed: false,
        expiresAt: { $gt: new Date() }
      }).sort({ createdAt: -1 });
    } catch (error) {
      logger.error('获取用户活跃验证码失败', error);
      return null;
    }
  }
}

export default new VerificationCodeService();
