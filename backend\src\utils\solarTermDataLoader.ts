import fs from 'fs';
import path from 'path';

/**
 * 节气数据类型定义
 */
export interface SolarTermData {
  [year: string]: {
    [month: string]: number;
  };
}

/**
 * 节气数据加载器
 * 负责从 JSON 文件动态加载节气数据，支持缓存和热更新
 */
class SolarTermDataLoader {
  private static instance: SolarTermDataLoader;
  private dataCache: SolarTermData | null = null;
  private lastModified: number = 0;
  private readonly dataFilePath: string;

  private constructor() {
    // JSON 数据文件路径 - 优先使用编译后的路径，回退到源码路径
    const distPath = path.resolve(__dirname, '../data/solarTermData.json');
    const srcPath = path.resolve(__dirname, '../../src/data/solarTermData.json');

    // 检查哪个路径存在
    if (fs.existsSync(distPath)) {
      this.dataFilePath = distPath;
      console.log('使用编译后的节气数据文件:', distPath);
    } else if (fs.existsSync(srcPath)) {
      this.dataFilePath = srcPath;
      console.log('使用源码节气数据文件:', srcPath);
    } else {
      // 默认使用 dist 路径，如果不存在会在后续创建
      this.dataFilePath = distPath;
      console.log('节气数据文件不存在，将使用默认路径:', distPath);
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SolarTermDataLoader {
    if (!SolarTermDataLoader.instance) {
      SolarTermDataLoader.instance = new SolarTermDataLoader();
    }
    return SolarTermDataLoader.instance;
  }

  /**
   * 获取节气数据
   * 自动检查文件更新并重新加载
   */
  public getSolarTermData(): SolarTermData {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(this.dataFilePath)) {
        console.error(`节气数据文件不存在: ${this.dataFilePath}`);
        return {};
      }

      // 获取文件修改时间
      const stats = fs.statSync(this.dataFilePath);
      const currentModified = stats.mtime.getTime();

      // 如果文件已更新或缓存为空，重新加载
      if (!this.dataCache || currentModified > this.lastModified) {
        console.log('重新加载节气数据...');
        this.loadDataFromFile();
        this.lastModified = currentModified;
      }

      return this.dataCache || {};
    } catch (error) {
      console.error('获取节气数据失败:', error);
      return {};
    }
  }

  /**
   * 从文件加载数据
   */
  private loadDataFromFile(): void {
    try {
      const fileContent = fs.readFileSync(this.dataFilePath, 'utf-8');
      this.dataCache = JSON.parse(fileContent);
      console.log(`节气数据加载成功，包含 ${Object.keys(this.dataCache || {}).length} 年的数据`);
    } catch (error) {
      console.error('加载节气数据文件失败:', error);
      this.dataCache = {};
    }
  }

  /**
   * 保存数据到文件
   * @param data 节气数据
   */
  public saveSolarTermData(data: SolarTermData): boolean {
    try {
      // 创建备份
      const backupPath = `${this.dataFilePath}.backup.${Date.now()}`;
      if (fs.existsSync(this.dataFilePath)) {
        fs.copyFileSync(this.dataFilePath, backupPath);
      }

      // 写入新数据
      fs.writeFileSync(this.dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
      
      // 清除缓存，强制下次重新加载
      this.dataCache = null;
      this.lastModified = 0;
      
      console.log('节气数据保存成功');
      return true;
    } catch (error) {
      console.error('保存节气数据失败:', error);
      return false;
    }
  }

  /**
   * 获取文件内容（用于管理界面显示）
   */
  public getFileContent(): string {
    try {
      if (!fs.existsSync(this.dataFilePath)) {
        return '{}';
      }
      return fs.readFileSync(this.dataFilePath, 'utf-8');
    } catch (error) {
      console.error('读取文件内容失败:', error);
      return '{}';
    }
  }

  /**
   * 保存文件内容（用于管理界面编辑）
   */
  public saveFileContent(content: string): boolean {
    try {
      // 验证 JSON 格式
      JSON.parse(content);
      
      // 创建备份
      const backupPath = `${this.dataFilePath}.backup.${Date.now()}`;
      if (fs.existsSync(this.dataFilePath)) {
        fs.copyFileSync(this.dataFilePath, backupPath);
      }

      // 写入文件
      fs.writeFileSync(this.dataFilePath, content, 'utf-8');
      
      // 清除缓存
      this.dataCache = null;
      this.lastModified = 0;
      
      console.log('文件内容保存成功');
      return true;
    } catch (error) {
      console.error('保存文件内容失败:', error);
      return false;
    }
  }

  /**
   * 清除缓存（用于强制重新加载）
   */
  public clearCache(): void {
    this.dataCache = null;
    this.lastModified = 0;
  }
}

// 导出单例实例
export const solarTermDataLoader = SolarTermDataLoader.getInstance();
export default solarTermDataLoader;
