import { Router } from 'express';
import solarTermPredictionController from '../controllers/solarTermPredictionController';
import { optionalAuthenticate } from '../middlewares/authMiddleware';

// 创建路由实例
const router = Router();

// 获取节气预测折线数据路由
// GET /api/solar-term-predictions
router.get('/', optionalAuthenticate, solarTermPredictionController.getRecentPredictions);

// 注意：以下触发接口已删除，因为：
// 1. 前端没有使用这些接口
// 2. 预测生成和完整性检查已通过定时任务自动化
// 3. 避免未授权用户触发系统资源消耗
// - POST /api/solar-term-predictions/trigger
// - POST /api/solar-term-predictions/check-integrity

export default router;