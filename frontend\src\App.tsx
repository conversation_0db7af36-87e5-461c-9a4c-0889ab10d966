import React, { Component, ErrorInfo, ReactNode, useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useTokenRefresh } from './hooks/useTokenRefresh';
import './App.css';
import './i18n';
import { useTranslation } from 'react-i18next';
import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Register from './pages/Register';
import EmailVerification from './pages/EmailVerification';
// VerifySuccess页面已移除，现在使用验证码验证方式
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Privacy from './pages/Privacy';
import Terms from './pages/Terms';
import UserCenter from './pages/UserCenter';
import Dashboard from './pages/Dashboard';
import Subscribe from './pages/Subscribe';
import SubscriptionSuccess from './pages/SubscriptionSuccess';
import NotFound from './pages/NotFound';
import PrivateRoute from './routes/PrivateRoute';
import AdminRoute from './routes/AdminRoute';
import useUserStore from './store/useUserStore';
import useAdminStore from './store/useAdminStore';
import PublicLayout from './layouts/PublicLayout';
import AuthLayout from './layouts/AuthLayout';
import AppLayout from './layouts/AppLayout';
import SubscriptionLayout from './layouts/SubscriptionLayout';
import DocsLayout from './layouts/DocsLayout';
import { Toaster } from './components/ui/toaster';
import tokenService from './api/tokenService';
import configService from './services/configService';

// 管理员页面
import AdminLogin from './pages/admin/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminUsers from './pages/admin/Users';

import AdminPayments from './pages/admin/Payments';
import AdminPaymentDetails from './pages/admin/PaymentDetails';
import AdminFeedback from './pages/admin/Feedback';
import AdminFeedbackDetail from './pages/admin/FeedbackDetail';
import Documents from './pages/admin/Documents';
import DocumentEditor from './pages/admin/DocumentEditor';
import SettingsIndex from './pages/admin/settings/SettingsIndex';
import BasicSettings from './pages/admin/settings/BasicSettings';
import UserSettings from './pages/admin/settings/UserSettings';
import PaymentSettings from './pages/admin/settings/PaymentSettings';
import SecuritySettings from './pages/admin/settings/SecuritySettings';
import TranslationSettings from './pages/admin/settings/TranslationSettings';
import EmailSettings from './pages/admin/settings/EmailSettings';
import LogSettings from './pages/admin/settings/LogSettings';

// 管理员工具页面
import DataCleanup from './pages/admin/tools/DataCleanup';
import SolarTerms from './pages/admin/tools/SolarTerms';

// 管理员系统页面
import SystemLogs from './pages/admin/system/Logs';

// 文档页面
import DocsPage from './pages/Docs/DocsPage';

// 错误显示组件（函数组件，可以使用翻译）
const ErrorDisplay: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background text-content-primary">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">{t('common.somethingWentWrong')}</h1>
        <p className="text-content-secondary">{t('common.refreshPageAndTryAgain')}</p>
      </div>
    </div>
  );
};

// 错误边界组件
class ErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean }> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorDisplay />;
    }

    return this.props.children;
  }
}

function AppContent() {
  const { fetchUserInfo, isAuthenticated, isInitializing } = useUserStore();
  const { initialize } = useAdminStore();

  // 应用启动时根据路径选择性初始化
  useEffect(() => {
    // 首先尝试从localStorage加载令牌到内存中
    const hasValidToken = tokenService.loadTokenFromStorage();

    if (hasValidToken) {
      console.log('从localStorage恢复了有效token');
    } else {
      console.log('localStorage中没有有效token，将依赖refreshToken机制');
    }

    // 启动token定期清理机制
    tokenService.startTokenCleanup();

    // 根据路径判断应该初始化哪种角色
    const pathname = window.location.pathname;
    const isAdminPath = pathname.startsWith('/admin');
    const isAdminLoginPage = pathname === '/admin/login';

    // 为了防止循环，检查是否有正在进行的重定向
    const isRedirecting = sessionStorage.getItem('admin_redirecting') === 'true';

    if (isRedirecting) {
      console.log('检测到重定向状态，跳过初始化');
      sessionStorage.removeItem('admin_redirecting');
      return;
    }

    // 根据路径判断应该初始化哪种角色
    if (isAdminPath && !isAdminLoginPage) {
      // 管理员路径：只初始化管理员信息
      console.log('管理员路径：初始化管理员信息');
      initialize();
    } else if (!isAdminPath) {
      // 用户路径（包括首页）：只初始化用户信息
      console.log('用户路径：初始化用户信息');
      fetchUserInfo();
    } else if (isAdminLoginPage) {
      // 管理员登录页：不需要初始化任何信息，也不启动用户相关逻辑
      console.log('管理员登录页，跳过所有自动认证');
    }
  }, [fetchUserInfo, initialize]);

  return (
    <Routes>
      {/* 公开路由 */}
      <Route element={<PublicLayout />}>
        <Route path="/" element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <LandingPage />
        } />
        {/* verify-email路由已移除，现在使用验证码验证方式 */}
      </Route>

      {/* 隐私政策和服务条款 - 根据登录状态使用不同布局 */}
      {isInitializing ? (
        // 初始化期间显示加载状态，避免布局闪烁
        <>
          <Route path="privacy" element={
            <div className="min-h-screen flex items-center justify-center bg-cyber-bg">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
            </div>
          } />
          <Route path="terms" element={
            <div className="min-h-screen flex items-center justify-center bg-cyber-bg">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
            </div>
          } />
        </>
      ) : !isAuthenticated ? (
        <Route element={<PublicLayout />}>
          <Route path="privacy" element={<Privacy />} />
          <Route path="terms" element={<Terms />} />
        </Route>
      ) : (
        <Route element={<PrivateRoute />}>
          <Route element={<AppLayout />}>
            <Route path="privacy" element={<Privacy />} />
            <Route path="terms" element={<Terms />} />
          </Route>
        </Route>
      )}

      {/* 认证路由 - 独立布局 */}
      <Route element={<AuthLayout />}>
        <Route path="login" element={<Login />} />
        <Route path="register" element={<Register />} />
        <Route path="email-verification" element={<EmailVerification />} />
        <Route path="forgot-password" element={<ForgotPassword />} />
        <Route path="reset-password" element={<ResetPassword />} />
      </Route>

      {/* 文档路由 */}
      <Route element={<DocsLayout />}>
        <Route path="docs" element={<DocsPage />} />
        <Route path="docs/article/:id" element={<DocsPage />} />
        <Route path="faq" element={<DocsPage />} />
        <Route path="faq/article/:id" element={<DocsPage />} />
        <Route path="announcement" element={<DocsPage />} />
        <Route path="announcement/article/:id" element={<DocsPage />} />
      </Route>

      {/* 受保护路由 */}
      <Route element={<PrivateRoute />}>
        <Route element={<AppLayout />}>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="user-center" element={<UserCenter />} />
          <Route path="subscribe" element={<Subscribe />} />
        </Route>

        {/* 订阅流程页面 */}
        <Route element={<SubscriptionLayout />}>
          <Route path="subscription/cancel" element={<Navigate to="/subscribe" replace />} />
        </Route>

        {/* 订阅成功页面 - 独立布局，无导航栏 */}
        <Route path="subscription/success" element={<SubscriptionSuccess />} />
      </Route>

      {/* 管理员路由 */}
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route path="/admin" element={<AdminRoute />}>
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users" element={<AdminUsers />} />

        <Route path="settings" element={<SettingsIndex />} />
        <Route path="settings/basic" element={<BasicSettings />} />
        <Route path="settings/user" element={<UserSettings />} />
        <Route path="settings/payment" element={<PaymentSettings />} />
        <Route path="settings/security" element={<SecuritySettings />} />
        <Route path="settings/translation" element={<TranslationSettings />} />
        <Route path="settings/email" element={<EmailSettings />} />
        <Route path="settings/logs" element={<LogSettings />} />
        <Route path="system/logs" element={<SystemLogs />} />
        <Route path="payments" element={<AdminPayments />} />
        <Route path="payments/:id" element={<AdminPaymentDetails />} />
        <Route path="feedback" element={<AdminFeedback />} />
        <Route path="feedback/:id" element={<AdminFeedbackDetail />} />
        <Route path="documents" element={<Documents />} />
        <Route path="documents/create" element={<DocumentEditor />} />
        <Route path="documents/edit/:id" element={<DocumentEditor />} />

        {/* 管理员工具路由 */}
        <Route path="tools/data-cleanup" element={<DataCleanup />} />
        <Route path="tools/solar-terms" element={<SolarTerms />} />
      </Route>

      {/* 404 页面 - 必须放在最后，匹配所有未定义的路由 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// 创建一个高阶组件来处理身份验证
const AuthWrapper = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useUserStore();

  // 检查是否为管理员路径
  const isAdminPath = location.pathname.startsWith('/admin');

  // 只有非管理员路径才使用用户token刷新hook
  if (!isAdminPath) {
    useTokenRefresh();
  }

  // 全局错误处理（仅用户路径）
  useEffect(() => {
    const handleUnauthorized = (event: CustomEvent) => {
      // 管理员路径不处理用户认证失效事件
      if (isAdminPath) {
        return;
      }

      // 只有在用户之前是认证状态时才跳转，避免未登录用户被误跳转
      if (isAuthenticated && !location.pathname.includes('/login')) {
        console.log('检测到认证失效，跳转到登录页:', event.detail);
        navigate('/login', { state: { reason: 'session_expired', from: location.pathname } });
      } else {
        console.log('未认证用户收到unauthorized事件，忽略跳转');
      }
    };

    // 创建自定义事件监听
    window.addEventListener('unauthorized', handleUnauthorized as EventListener);

    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized as EventListener);
    };
  }, [navigate, location, isAuthenticated, isAdminPath]);

  return <>{children}</>;
};

function App() {
  const [isConfigLoaded, setIsConfigLoaded] = useState(false);
  const { i18n } = useTranslation();

  // 设置默认标题（在配置加载之前）
  useEffect(() => {
    // 如果当前标题还是默认的React App，先设置一个临时标题
    if (document.title === 'React App' || document.title === 'BTC 预测') {
      document.title = 'BTC 预测';
    }
  }, []);

  // 加载网站配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        // 获取网站配置
        const config = await configService.getConfig();

        // 更新网站标题
        document.title = config.siteInfo.siteName;

        // 更新网站描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', config.siteInfo.siteDescription);
        }

        // 更新网站关键词
        let metaKeywords = document.querySelector('meta[name="keywords"]');
        if (!metaKeywords) {
          metaKeywords = document.createElement('meta');
          metaKeywords.setAttribute('name', 'keywords');
          document.head.appendChild(metaKeywords);
        }
        metaKeywords.setAttribute('content', config.siteInfo.siteKeywords);



        // 处理语言设置
        const savedLanguage = localStorage.getItem('i18nextLng');
        const userChosenLanguage = localStorage.getItem('userChosenLanguage'); // 用户主动选择的标记

        if (!userChosenLanguage) {
          // 首次访问用户或浏览器自动检测的语言，使用管理员设置的默认语言
          console.log('First-time user, applying admin default language:', config.siteInfo.defaultLanguage);
          await i18n.changeLanguage(config.siteInfo.defaultLanguage);
          // 标记这是管理员设置的默认语言，不是用户选择
          localStorage.setItem('userChosenLanguage', 'false');
        } else if (userChosenLanguage === 'true' && savedLanguage) {
          // 用户之前主动选择过语言，保留其选择
          console.log('User previously chose language, keeping choice:', savedLanguage);
        } else {
          // 其他情况，使用管理员设置的默认语言
          console.log('Applying admin default language:', config.siteInfo.defaultLanguage);
          await i18n.changeLanguage(config.siteInfo.defaultLanguage);
        }

        setIsConfigLoaded(true);
      } catch (error) {
        console.error('Failed to load site config:', error);
        // 即使配置加载失败，也允许应用继续运行
        setIsConfigLoaded(true);
      }
    };

    loadConfig();
  }, [i18n]);

  // 显示加载指示器
  if (!isConfigLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="App">
      <ErrorBoundary>
        <Router>
          <Routes>
            <Route path="/*" element={
              <AuthWrapper>
                <AppContent />
              </AuthWrapper>
            } />
          </Routes>
          <Toaster />
        </Router>
      </ErrorBoundary>
    </div>
  );
}

export default App;
