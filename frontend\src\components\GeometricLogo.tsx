import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { cn } from '@/lib/utils'

interface GeometricLogoProps {
  className?: string
  to?: string
  onClick?: () => void
  siteName?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showText?: boolean
}

const GeometricLogo: React.FC<GeometricLogoProps> = ({
  className,
  to,
  onClick,
  siteName = 'BT',
  size = 'md',
  showText = true
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-lg'
  }

  const LogoSvg = () => (
    <div className={cn(
      "relative flex items-center justify-center",
      sizeClasses[size]
    )}>
      <svg
        viewBox="0 0 48 48"
        className="w-full h-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* 渐变背景定义 */}
        <defs>
          <linearGradient id="logoBackground" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#00f5ff" />
            <stop offset="50%" stopColor="#0066ff" />
            <stop offset="100%" stopColor="#b537f2" />
          </linearGradient>
        </defs>

        {/* 圆角矩形背景 */}
        <rect
          x="0"
          y="0"
          width="48"
          height="48"
          rx="12"
          ry="12"
          fill="url(#logoBackground)"
          className="drop-shadow-lg"
        />

        {/* Logo内容 - 调整位置和大小以适应背景 */}
        <g transform="translate(24, 24) rotate(45) translate(-20, -20) scale(1)">
          {/* 波浪线连接 - 在圆形之前绘制，作为背景 */}
          {/* 水平波浪线：左中 到 右中 */}
          <path d="M 10 20 Q 15 17 20 20 Q 25 23 30 20" stroke="white" strokeWidth="2" fill="none" opacity="0.6" />

          {/* 垂直波浪线：上中 到 下中 */}
          <path d="M 20 10 Q 17 15 20 20 Q 23 25 20 30" stroke="white" strokeWidth="2" fill="none" opacity="0.6" />

          {/* 四边连接线 */}
          {/* 上边：左上大圆 → 上中小圆 → 右上大圆 */}
          <path d="M 10 10 Q 15 8 20 10 Q 25 8 30 10" stroke="white" strokeWidth="2" fill="none" opacity="0.5" />

          {/* 右边：右上大圆 → 右中小圆 → 右下大圆 */}
          <path d="M 30 10 Q 32 15 30 20 Q 32 25 30 30" stroke="white" strokeWidth="2" fill="none" opacity="0.5" />

          {/* 下边：右下大圆 → 下中小圆 → 左下大圆 */}
          <path d="M 30 30 Q 25 32 20 30 Q 15 32 10 30" stroke="white" strokeWidth="2" fill="none" opacity="0.5" />

          {/* 左边：左下大圆 → 左中小圆 → 左上大圆 */}
          <path d="M 10 30 Q 8 25 10 20 Q 8 15 10 10" stroke="white" strokeWidth="2" fill="none" opacity="0.5" />

          {/* 3x3 Grid Layout - Row 1 */}
          {/* Top-left - 大圆，白色 */}
          <circle cx="10" cy="10" r="4" fill="white" />
          {/* Top-center - 小圆，白色 */}
          <circle cx="20" cy="10" r="3" fill="white" />
          {/* Top-right - 大圆，白色 */}
          <circle cx="30" cy="10" r="4" fill="white" />

          {/* 3x3 Grid Layout - Row 2 */}
          {/* Middle-left - 小圆，白色 */}
          <circle cx="10" cy="20" r="3" fill="white" />
          {/* Center - 中心大圆，白色 */}
          <circle cx="20" cy="20" r="6" fill="white" className="drop-shadow-sm" />
          <rect x="17" y="17" width="6" height="6" fill="#0a0a0f" />
          {/* Middle-right - 小圆，白色 */}
          <circle cx="30" cy="20" r="3" fill="white" />

          {/* 3x3 Grid Layout - Row 3 */}
          {/* Bottom-left - 大圆，白色 */}
          <circle cx="10" cy="30" r="4" fill="white" />
          {/* Bottom-center - 小圆，白色 */}
          <circle cx="20" cy="30" r="3" fill="white" />
          {/* Bottom-right - 大圆，白色 */}
          <circle cx="30" cy="30" r="4" fill="white" />
        </g>
      </svg>
    </div>
  )

  const LogoContent = () => (
    <div className={cn(
      "flex items-center group cursor-pointer",
      showText ? "space-x-2" : "",
      className
    )}>
      <LogoSvg />
      {showText && (
        <span className={cn(
          "font-sans font-bold text-white tracking-wider transition-all duration-300",
          textSizeClasses[size]
        )}>
          {siteName}
        </span>
      )}
    </div>
  )

  if (to) {
    return (
      <Link to={to} onClick={onClick}>
        <LogoContent />
      </Link>
    )
  }

  return (
    <div onClick={onClick}>
      <LogoContent />
    </div>
  )
}

export default GeometricLogo
