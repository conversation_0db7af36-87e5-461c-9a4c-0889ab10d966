import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import adminApi, { clearAdminToken } from '../api/admin';

// Token有效性检查函数
const isTokenValid = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    // 检查token是否过期（留5分钟缓冲时间）
    return payload.exp * 1000 > Date.now() + 5 * 60 * 1000;
  } catch {
    return false;
  }
};

// 检查是否为旧格式token（没有jti字段）
const isOldFormatToken = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return !payload.jti; // 旧token没有jti字段
  } catch {
    return true; // 解析失败也认为是旧token
  }
};

export interface AdminUser {
  _id: string;
  email: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
}

interface AdminState {
  user: AdminUser | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isInitializing: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  
  // 用户管理
  fetchUsers: (page: number, limit: number, search: string, filters: any) => Promise<any>;
  updateUserRole: (userId: string, role: string) => Promise<any>;
  createUser: (userData: any) => Promise<any>;
  deleteUser: (userId: string) => Promise<any>;
  banUser: (userId: string) => Promise<any>;
  unbanUser: (userId: string) => Promise<any>;
  
  // 系统设置管理
  getSystemSettings: () => Promise<any>;
  updateSystemSettings: (settings: any) => Promise<any>;
  
  // 支付管理
  getPayments: (page: number, limit: number, search: string, filters: any) => Promise<any>;
  getPaymentDetails: (paymentId: string) => Promise<any>;
  updatePaymentStatus: (paymentId: string, status: string) => Promise<any>;
  getPaymentStats: () => Promise<any>;
  

  
  updateUserSubscription: (userId: string, subscriptionData: any) => Promise<any>;
  
  clearError: () => void;
  initialize: () => void;
}

type AdminPersist = {
  user: AdminUser | null; // 只持久化用户信息
};

const useAdminStore = create(
  persist<AdminState, AdminPersist>(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      isInitializing: true,

      // 初始化检查
      initialize: () => {
        const state = get();
        
        // 如果正在重定向，跳过初始化
        if (sessionStorage.getItem('admin_redirecting')) {
          console.log('检测到管理员重定向标志，跳过初始化');
          sessionStorage.removeItem('admin_redirecting');
          set({ 
            isAuthenticated: false, 
            isInitializing: false,
            isLoading: false,
            user: null,
            token: null
          });
          return;
        }
        
        // 检查用户信息和token有效性
        if (state.user && state.user.role === 'admin') {
          // 检查是否有token
          if (state.token) {
            // 检查是否为旧格式token
            if (isOldFormatToken(state.token)) {
              console.log('检测到旧格式管理员token，清理并要求重新登录');
              // 清理旧token
              adminApi.clearAdminToken();
              set({
                isAuthenticated: false,
                isInitializing: false,
                user: state.user, // 保留用户信息用于显示
                token: null
              });
            } else if (isTokenValid(state.token)) {
              console.log('发现有效的管理员token，恢复认证状态');
              // 将token设置到内存中
              adminApi.setAdminToken(state.token);
              set({
                isAuthenticated: true,
                isInitializing: false,
                user: state.user,
                token: state.token
              });
            } else {
              console.log('发现管理员token已过期，需要重新登录');
              set({
                isAuthenticated: false,
                isInitializing: false,
                user: state.user, // 保留用户信息用于显示
                token: null
              });
            }
          } else {
            console.log('发现管理员用户信息，但没有token，需要重新登录');
            set({
              isAuthenticated: false,
              isInitializing: false,
              user: state.user, // 保留用户信息用于显示
              token: null
            });
          }
        } else {
          console.log('没有找到管理员用户信息');
          set({
            isAuthenticated: false,
            isInitializing: false,
            user: null,
            token: null
          });
        }
      },

      // 管理员登录
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          // 调用登录API（token已在adminApi.adminLogin中设置到内存）
          const response = await adminApi.adminLogin(email, password);

          // 验证用户角色
          if (response.user.role !== 'admin') {
            throw new Error('非管理员账号无法登录');
          }

          // 设置认证状态（token已在内存中）
          set({
            user: response.user,
            token: response.accessToken, // 用于状态显示
            isAuthenticated: true,
            isLoading: false,
            isInitializing: false
          });

          return response;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '登录失败，请稍后重试';
          set({
            isLoading: false,
            error: errorMessage,
            isInitializing: false,
            isAuthenticated: false,
            user: null,
            token: null
          });
          throw error;
        }
      },

      // 管理员登出
      logout: () => {
        // 清除内存token
        clearAdminToken();

        // 清除状态
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });

        console.log('管理员已登出');
      },

      // 获取用户列表
      fetchUsers: async (page = 1, limit = 10, search = '', filters = {}) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getUsers(page, limit, search, filters);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取用户列表失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新用户角色
      updateUserRole: async (userId: string, role: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateUserRole(userId, role);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新用户角色失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 创建用户
      createUser: async (userData: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.createUser(userData);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '创建用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 删除用户
      deleteUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.deleteUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '删除用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 封禁用户
      banUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.banUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '封禁用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 解封用户
      unbanUser: async (userId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.unbanUser(userId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '解封用户失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取系统设置
      getSystemSettings: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getSystemSettings();
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取系统设置失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新系统设置
      updateSystemSettings: async (settings: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateSystemSettings(settings);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新系统设置失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付列表
      getPayments: async (page = 1, limit = 10, search = '', filters = {}) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPayments(page, limit, search, filters);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付列表失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付详情
      getPaymentDetails: async (paymentId: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPaymentDetails(paymentId);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付详情失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 更新支付状态
      updatePaymentStatus: async (paymentId: string, status: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updatePaymentStatus(paymentId, status);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新支付状态失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取支付统计数据
      getPaymentStats: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.getPaymentStats();
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '获取支付统计数据失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },



      // 更新用户订阅
      updateUserSubscription: async (userId: string, subscriptionData: any) => {
        try {
          set({ isLoading: true, error: null });
          const response = await adminApi.updateUserSubscription(userId, subscriptionData);
          set({ isLoading: false });
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error 
            ? error.message 
            : '更新用户订阅失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'admin-storage', // localStorage 中的键名
      partialize: (state) => {
        const s = state as AdminState;
        return {
          user: s.user // 只持久化用户信息，不持久化token
        };
      }
    }
  )
);

export default useAdminStore; 