import mongoose, { Document, Schema } from 'mongoose';

export interface ISolarTermConfig extends Document {
  cycleKey: string;           // 周期标识：BTCUSDT_2025_6_5
  symbol: string;             // 交易对：BTCUSDT
  conversionRatio: number;    // 转换比例（敏感数据）
  virtualBase: number;        // 虚拟基准价格（用于记录）
  createdAt: Date;
  updatedAt: Date;
}

const SolarTermConfigSchema = new Schema<ISolarTermConfig>({
  cycleKey: { type: String, required: true, unique: true },
  symbol: { type: String, required: true },
  conversionRatio: { type: Number, required: true },
  virtualBase: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 创建复合索引
SolarTermConfigSchema.index({ symbol: 1, cycleKey: 1 }, { unique: true });

export default mongoose.model<ISolarTermConfig>('SolarTermConfig', SolarTermConfigSchema);
