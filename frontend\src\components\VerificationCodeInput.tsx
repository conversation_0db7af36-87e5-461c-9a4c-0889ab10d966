import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';
import { useTranslation } from 'react-i18next';

interface VerificationCodeInputProps {
  length?: number;
  onComplete: (code: string) => void;
  onResend?: () => void;
  isLoading?: boolean;
  error?: string | null;
  email?: string;
  resendCountdown?: number;
  disabled?: boolean;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  length = 6,
  onComplete,
  onResend,
  isLoading = false,
  error = null,
  email = '',
  resendCountdown = 0,
  disabled = false
}) => {
  const { t } = useTranslation();
  const [code, setCode] = useState<string[]>(new Array(length).fill(''));
  const [focusedIndex, setFocusedIndex] = useState<number>(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 初始化输入框引用数组
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // 自动聚焦到第一个输入框
  useEffect(() => {
    if (inputRefs.current[0] && !disabled) {
      inputRefs.current[0].focus();
    }
  }, [disabled]);

  // 处理输入变化
  const handleChange = (index: number, value: string) => {
    if (disabled) return;

    // 只允许数字输入
    const numericValue = value.replace(/[^0-9]/g, '');
    
    if (numericValue.length <= 1) {
      const newCode = [...code];
      newCode[index] = numericValue;
      setCode(newCode);

      // 如果输入了数字且不是最后一个输入框，自动聚焦到下一个
      if (numericValue && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
        setFocusedIndex(index + 1);
      }

      // 检查是否完成输入
      const completedCode = newCode.join('');
      if (completedCode.length === length && completedCode.replace(/[^0-9]/g, '').length === length) {
        onComplete(completedCode);
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === 'Backspace') {
      e.preventDefault();
      const newCode = [...code];
      
      if (code[index]) {
        // 如果当前输入框有值，清空当前输入框
        newCode[index] = '';
        setCode(newCode);
      } else if (index > 0) {
        // 如果当前输入框为空，清空前一个输入框并聚焦
        newCode[index - 1] = '';
        setCode(newCode);
        inputRefs.current[index - 1]?.focus();
        setFocusedIndex(index - 1);
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
      setFocusedIndex(index - 1);
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
      setFocusedIndex(index + 1);
    }
  };

  // 处理粘贴事件
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const numericData = pastedData.replace(/[^0-9]/g, '').slice(0, length);
    
    if (numericData.length > 0) {
      const newCode = new Array(length).fill('');
      for (let i = 0; i < numericData.length && i < length; i++) {
        newCode[i] = numericData[i];
      }
      setCode(newCode);
      
      // 聚焦到最后一个填充的输入框或下一个空输入框
      const nextFocusIndex = Math.min(numericData.length, length - 1);
      inputRefs.current[nextFocusIndex]?.focus();
      setFocusedIndex(nextFocusIndex);
      
      // 如果粘贴的数据长度等于验证码长度，触发完成回调
      if (numericData.length === length) {
        onComplete(numericData);
      }
    }
  };

  // 处理聚焦事件
  const handleFocus = (index: number) => {
    setFocusedIndex(index);
  };



  return (
    <div className="w-full max-w-md mx-auto">
      {/* 邮箱提示 */}
      {email && (
        <div className="text-center mb-4">
          <p className="text-sm text-cyber-muted font-mono">
            {t('verification.codeSentTo')} <span className="font-medium text-cyber-cyan">{email}</span>
          </p>
        </div>
      )}

      {/* 验证码输入框 */}
      <div className="flex justify-center space-x-2 mb-4">
        {code.map((digit, index) => (
          <input
            key={index}
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            type="text"
            inputMode="numeric"
            maxLength={1}
            value={digit}
            onChange={(e) => handleChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={handlePaste}
            onFocus={() => handleFocus(index)}
            disabled={disabled || isLoading}
            className={`
              w-12 h-12 text-center text-lg font-bold rounded-xl
              bg-cyber-border/5 border border-cyber-cyan/30 text-cyber-text
              focus:border-cyber-cyan/80 transition-all duration-300 font-mono
              outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0
              ${digit ? 'border-cyber-cyan/80 bg-cyber-cyan/10 text-cyber-cyan' : ''}
              ${focusedIndex === index ? 'border-cyber-cyan/80' : ''}
              ${error ? 'border-red-500' : ''}
              ${disabled || isLoading ? 'bg-cyber-muted/20 cursor-not-allowed opacity-50' : ''}
              placeholder-cyber-muted/50
            `}
            placeholder=""
          />
        ))}
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="text-center mb-4">
          <p className="text-sm text-red-400 font-mono">{error}</p>
        </div>
      )}

      {/* 重发验证码 */}
      {onResend && (
        <div className="text-center">
          {resendCountdown > 0 ? (
            <p className="text-sm text-cyber-muted font-mono">
              {t('verification.resendCodeWithCountdown', { countdown: resendCountdown })}
            </p>
          ) : (
            <button
              onClick={onResend}
              disabled={disabled || isLoading}
              className="text-sm text-cyber-cyan hover:text-cyber-cyan/80 disabled:text-cyber-muted disabled:cursor-not-allowed transition-colors duration-200 font-mono"
            >
              {t('verification.resendCode')}
            </button>
          )}
        </div>
      )}



      {/* 加载状态 */}
      {isLoading && (
        <div className="text-center mt-4">
          <div className="inline-flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
            <span className="text-sm text-content-muted">{t('verification.verifying')}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default VerificationCodeInput;
