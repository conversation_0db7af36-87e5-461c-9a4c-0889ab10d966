import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getDocuments, Document } from '../../api/docs';
import DocsSidebar from '../../components/docs/DocsSidebar';
import MarkdownRenderer from '../../components/docs/MarkdownRenderer';
import { useToast } from '../../components/ui/use-toast';
import { Calendar, FileText } from 'lucide-react';

const DocsPage: React.FC = () => {
  const { id } = useParams<{ id?: string }>();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  const [documents, setDocuments] = useState<Document[]>([]);
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previousDocumentTitle, setPreviousDocumentTitle] = useState<string | null>(null);

  // 根据当前路由确定分类
  const getCurrentCategory = useCallback((): 'guide' | 'faq' | 'announcement' => {
    const path = location.pathname;
    if (path.startsWith('/faq')) return 'faq';
    if (path.startsWith('/announcement')) return 'announcement';
    return 'guide'; // 默认为使用指南
  }, [location.pathname]);

  // 文档标题映射关系（用于语言切换时查找对应文档）
  const getCorrespondingDocumentTitle = useCallback((title: string, targetLanguage: 'zh' | 'en'): string => {
    const titleMappings: Record<string, { zh: string; en: string }> = {
      // 服务条款
      'terms': { zh: '服务条款', en: 'Terms of Service' },
      // 隐私政策
      'privacy': { zh: '隐私政策', en: 'Privacy Policy' },
      // 预测数据相关
      'prediction': { zh: '如何理解预测数据？', en: 'How to Understand Prediction Data?' },
      // 快速开始
      'quickstart': { zh: '快速开始指南', en: 'Quick Start Guide' },
    };

    // 根据当前标题找到对应的映射
    for (const [key, mapping] of Object.entries(titleMappings)) {
      if (mapping.zh === title || mapping.en === title) {
        return mapping[targetLanguage];
      }
    }

    // 如果没有找到映射，返回原标题
    return title;
  }, []);

  // 根据标题查找文档
  const findDocumentByTitle = useCallback((documents: Document[], title: string): Document | null => {
    return documents.find(doc => doc.title === title) || null;
  }, []);

  // 加载文档列表
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setIsLoading(true);
        const currentCategory = getCurrentCategory();

        // 只获取当前分类的文档
        const response = await getDocuments({
          category: currentCategory,
          language: i18n.language as 'zh' | 'en',
          limit: 100
        });

        if (response.success) {
          setDocuments(response.data.documents);

          let documentToShow: Document | null = null;

          // 根据URL参数设置当前文档
          if (id) {
            // 如果有ID参数，查找对应文档
            const foundDocument = response.data.documents.find(doc => doc._id === id);
            if (foundDocument) {
              documentToShow = foundDocument;
            } else {
              // 文档不存在，可能是语言切换导致的
              // 尝试根据之前的文档标题查找对应语言版本
              if (previousDocumentTitle) {
                const targetTitle = getCorrespondingDocumentTitle(previousDocumentTitle, i18n.language as 'zh' | 'en');
                const correspondingDoc = findDocumentByTitle(response.data.documents, targetTitle);

                if (correspondingDoc) {
                  documentToShow = correspondingDoc;
                  // 更新URL到新的文档ID
                  const currentCategory = getCurrentCategory();
                  const newPath = currentCategory === 'guide' ? `/docs/article/${correspondingDoc._id}` : `/${currentCategory}/article/${correspondingDoc._id}`;
                  navigate(newPath, { replace: true });
                } else {
                  // 如果找不到对应文档，显示第一篇
                  if (response.data.documents.length > 0) {
                    const sortedDocs = [...response.data.documents].sort((a, b) => a.order - b.order);
                    documentToShow = sortedDocs[0];
                  }
                }
              } else {
                // 没有之前的文档标题，跳转到分类首页（不显示错误提示，因为可能是正常的URL访问）
                const currentCategory = getCurrentCategory();
                const categoryPath = currentCategory === 'guide' ? '/docs' : `/${currentCategory}`;
                console.log(`文档不存在，跳转到分类首页: ${categoryPath}`);
                navigate(categoryPath, { replace: true });
                return;
              }
            }
          } else {
            // 如果没有ID参数，尝试根据之前的文档标题查找
            if (previousDocumentTitle) {
              const targetTitle = getCorrespondingDocumentTitle(previousDocumentTitle, i18n.language as 'zh' | 'en');
              const correspondingDoc = findDocumentByTitle(response.data.documents, targetTitle);

              if (correspondingDoc) {
                documentToShow = correspondingDoc;
                // 更新URL到新的文档ID
                const currentCategory = getCurrentCategory();
                const newPath = currentCategory === 'guide' ? `/docs/article/${correspondingDoc._id}` : `/${currentCategory}/article/${correspondingDoc._id}`;
                navigate(newPath, { replace: true });
              }
            }

            // 如果没有找到对应文档，显示第一篇文档
            if (!documentToShow && response.data.documents.length > 0) {
              const sortedDocs = [...response.data.documents].sort((a, b) => a.order - b.order);
              documentToShow = sortedDocs[0];
            }
          }

          if (documentToShow) {
            setCurrentDocument(documentToShow);
            // 保存当前文档标题，用于语言切换
            setPreviousDocumentTitle(documentToShow.title);
          }
        }
      } catch (error) {
        console.error('加载文档列表失败:', error);
        toast({
          title: t('common.error', '错误'),
          description: error instanceof Error ? error.message : t('docs.loadError', '加载文档失败'),
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadDocuments();
  }, [i18n.language, id, t, toast, navigate, getCurrentCategory, getCorrespondingDocumentTitle, findDocumentByTitle, previousDocumentTitle]);

  // 当URL参数变化时，立即更新当前文档（无需等待加载完成）
  useEffect(() => {
    if (documents.length > 0) {
      if (id) {
        const foundDocument = documents.find(doc => doc._id === id);
        if (foundDocument) {
          setCurrentDocument(foundDocument);
          // 更新文档标题记录
          setPreviousDocumentTitle(foundDocument.title);
        } else {
          // 文档不存在，跳转到对应分类首页
          const currentCategory = getCurrentCategory();
          const categoryPath = currentCategory === 'guide' ? '/docs' : `/${currentCategory}`;
          navigate(categoryPath, { replace: true });
        }
      } else {
        // 回到分类首页，显示当前分类的第一篇文档
        if (documents.length > 0) {
          // 按order字段排序，显示第一篇文档
          const sortedDocs = [...documents].sort((a, b) => a.order - b.order);
          setCurrentDocument(sortedDocs[0]);
          // 更新文档标题记录
          setPreviousDocumentTitle(sortedDocs[0].title);
        }
      }
    }
  }, [id, documents, navigate, getCurrentCategory]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-6xl mx-auto flex">
        {/* 左侧边栏 */}
        <DocsSidebar
          documents={documents}
          isLoading={isLoading && documents.length === 0}
          selectedDocumentId={currentDocument?._id}
          currentCategory={getCurrentCategory()}
        />

        {/* 右侧内容区域 */}
        <div className="flex-1 relative">
        {isLoading && documents.length === 0 ? (
          <div className="p-8">
            <div className="animate-pulse space-y-4">
              <div className="bg-gray-200 h-8 w-3/4 rounded"></div>
              <div className="bg-gray-200 h-4 w-1/2 rounded"></div>
              <div className="space-y-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="bg-gray-100 h-4 w-full rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : currentDocument ? (
          <div
            key={currentDocument._id}
            className="p-8 transition-opacity duration-150 ease-in-out"
          >
            {/* 文档标题和元信息 */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {currentDocument.title}
              </h1>
              <div className="flex items-center text-sm text-gray-500 space-x-4 mb-6 pb-3 border-b border-gray-200">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {t('docs.lastUpdated', '最后更新')}: {formatDate(currentDocument.updatedAt)}
                  </span>
                </div>
              </div>
            </div>

            {/* 文档内容 */}
            <MarkdownRenderer content={currentDocument.content} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('docs.noDocuments', '暂无文档')}
              </h3>
              <p className="text-gray-500">
                {t('docs.noDocumentsDesc', '当前没有可用的文档内容')}
              </p>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

export default DocsPage;
