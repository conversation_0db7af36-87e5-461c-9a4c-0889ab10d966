import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import { useToast } from '../../../components/ui/use-toast';
import useAdminStore from '../../../store/useAdminStore';
import { Button, Input, Label, Card } from '../../../components/admin/ui';
import { Eye, EyeOff, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { adminApiInstance } from '../../../api/admin';

const EmailSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [testingConnection, setTestingConnection] = useState<string | null>(null);
  
  // 密钥显示状态
  const [showResendKey, setShowResendKey] = useState(false);
  const [showBrevoKey, setShowBrevoKey] = useState(false);
  const [showMailerSendKey, setShowMailerSendKey] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    emailSettings: {
      provider: 'resend' as 'resend' | 'brevo' | 'mailersend',
      resend: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: true
      },
      brevo: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: false
      },
      mailersend: {
        apiKey: '',
        fromEmail: '<EMAIL>',
        enabled: false
      },
      retryTimes: 3,
      timeout: 30000,
      fallbackProvider: undefined as 'resend' | 'brevo' | 'mailersend' | undefined,
      enableLogs: true
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();
      setFormData({
        emailSettings: response.settings.emailSettings || {
          provider: 'resend',
          resend: { apiKey: '', fromEmail: '<EMAIL>', enabled: true },
          brevo: { apiKey: '', fromEmail: '<EMAIL>', enabled: false },
          mailersend: { apiKey: '', fromEmail: '<EMAIL>', enabled: false },
          retryTimes: 3,
          timeout: 30000,
          enableLogs: true
        }
      });
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取邮件设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const [section, provider, field] = name.split('.');

    if (provider && field && section === 'emailSettings') {
      // 处理嵌套的provider配置
      setFormData(prev => ({
        ...prev,
        emailSettings: {
          ...prev.emailSettings,
          [provider]: {
            ...(prev.emailSettings[provider as keyof typeof prev.emailSettings] as any),
            [field]: value
          }
        }
      }));
    } else {
      // 处理普通字段
      const fieldName = name.replace('emailSettings.', '');
      setFormData(prev => ({
        ...prev,
        emailSettings: {
          ...prev.emailSettings,
          [fieldName]: value
        }
      }));
    }
  };

  // 处理数字输入变化
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const fieldName = name.replace('emailSettings.', '');
    setFormData(prev => ({
      ...prev,
      emailSettings: {
        ...prev.emailSettings,
        [fieldName]: Number(value)
      }
    }));
  };

  // 处理开关变化
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    const [section, provider, field] = name.split('.');

    if (provider && field && section === 'emailSettings') {
      // 处理嵌套的provider配置
      setFormData(prev => ({
        ...prev,
        emailSettings: {
          ...prev.emailSettings,
          [provider]: {
            ...(prev.emailSettings[provider as keyof typeof prev.emailSettings] as any),
            [field]: checked
          }
        }
      }));
    } else {
      // 处理普通字段
      const fieldName = name.replace('emailSettings.', '');
      setFormData(prev => ({
        ...prev,
        emailSettings: {
          ...prev.emailSettings,
          [fieldName]: checked
        }
      }));
    }
  };

  // 处理选择器变化
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    const fieldName = name.replace('emailSettings.', '');
    setFormData(prev => ({
      ...prev,
      emailSettings: {
        ...prev.emailSettings,
        [fieldName]: value || undefined
      }
    }));
  };

  // 测试连接
  const testConnection = async (provider: string) => {
    const providerConfig = formData.emailSettings[provider as keyof typeof formData.emailSettings] as any;
    const apiKey = providerConfig?.apiKey;
    
    if (!apiKey || apiKey === '***已配置***') {
      toast({
        title: '请先输入API密钥',
        variant: 'destructive'
      });
      return;
    }

    try {
      setTestingConnection(provider);

      const response = await adminApiInstance.post('/admin/settings/test-email-connection', {
        provider,
        apiKey
      });

      const result = response.data;

      toast({
        title: result.success ? '连接测试成功' : '连接测试失败',
        description: result.message,
        variant: result.success ? 'default' : 'destructive'
      });

    } catch (error: any) {
      let errorMessage = '网络错误，请稍后重试';

      if (error.response) {
        // 服务器返回了错误响应
        errorMessage = error.response.data?.message || `服务器错误: ${error.response.status}`;
      } else if (error.message) {
        // 网络错误或其他错误
        errorMessage = error.message;
      }

      toast({
        title: '测试连接失败',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setTestingConnection(null);
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        emailSettings: formData.emailSettings
      });
      toast({
        title: '邮件设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新邮件设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  // 检查API密钥是否已配置
  const hasResendKey = formData.emailSettings.resend.apiKey && formData.emailSettings.resend.apiKey !== '';
  const hasBrevoKey = formData.emailSettings.brevo.apiKey && formData.emailSettings.brevo.apiKey !== '';
  const hasMailerSendKey = formData.emailSettings.mailersend.apiKey && formData.emailSettings.mailersend.apiKey !== '';

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">邮件设置</h1>
          <p className="text-gray-600 mt-1">配置邮件服务提供商和发送参数</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
              {/* 当前服务商选择 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 text-gray-900">🎯 当前服务商选择</h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <Label htmlFor="provider">邮件服务商</Label>
                    <select
                      id="provider"
                      name="emailSettings.provider"
                      value={formData.emailSettings.provider}
                      onChange={handleSelectChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mt-1"
                    >
                      <option value="resend">Resend (推荐 - 无品牌标识)</option>
                      <option value="brevo">Brevo (9,000封/月)</option>
                      <option value="mailersend">MailerSend (3,000封/月)</option>
                    </select>
                    <p className="text-xs text-gray-700 mt-1">
                      选择当前使用的邮件服务商
                    </p>
                  </div>
                </div>
              </div>



              {/* Resend 设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>📝 Resend 设置</span>
                  {hasResendKey && <CheckCircle className="h-5 w-5 text-green-600" />}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="resendFromEmail">发送邮箱地址</Label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="emailSettings.resend.enabled"
                          checked={formData.emailSettings.resend.enabled}
                          onChange={handleSwitchChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span className="ml-3 text-sm text-gray-700">启用</span>
                      </label>
                    </div>
                    <Input
                      id="resendFromEmail"
                      name="emailSettings.resend.fromEmail"
                      type="email"
                      value={formData.emailSettings.resend.fromEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className="mb-3"
                    />
                    <p className="text-xs text-gray-700 mb-3">
                      Resend 发送邮箱地址，必须是在 Resend 中已验证的域名
                    </p>
                  </div>

                  <div className="mb-4">
                    <Label htmlFor="resendApiKey">API 密钥</Label>
                    <div className="relative">
                      <Input
                        id="resendApiKey"
                        name="emailSettings.resend.apiKey"
                        type={showResendKey ? 'text' : 'password'}
                        value={formData.emailSettings.resend.apiKey}
                        onChange={handleChange}
                        placeholder="输入 Resend API 密钥"
                        className="pr-20"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-600"
                          onClick={() => setShowResendKey(!showResendKey)}
                        >
                          {showResendKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => testConnection('resend')}
                          disabled={testingConnection === 'resend'}
                          className="text-xs px-2 py-1 h-6"
                        >
                          {testingConnection === 'resend' ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            '测试'
                          )}
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-700 mt-1">
                      在 <a href="https://resend.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Resend 控制台</a> 获取您的API密钥
                    </p>
                  </div>
                </div>
              </div>

              {/* Brevo 设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>📝 Brevo 设置</span>
                  {hasBrevoKey && <CheckCircle className="h-5 w-5 text-green-600" />}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="brevoFromEmail">发送邮箱地址</Label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="emailSettings.brevo.enabled"
                          checked={formData.emailSettings.brevo.enabled}
                          onChange={handleSwitchChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span className="ml-3 text-sm text-gray-700">启用</span>
                      </label>
                    </div>
                    <Input
                      id="brevoFromEmail"
                      name="emailSettings.brevo.fromEmail"
                      type="email"
                      value={formData.emailSettings.brevo.fromEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className="mb-3"
                    />
                    <p className="text-xs text-gray-500 mb-3">
                      Brevo 发送邮箱地址，必须是在 Brevo 中已验证的域名
                    </p>
                  </div>

                  <div className="mb-4">
                    <Label htmlFor="brevoApiKey">API 密钥</Label>
                    <div className="relative">
                      <Input
                        id="brevoApiKey"
                        name="emailSettings.brevo.apiKey"
                        type={showBrevoKey ? 'text' : 'password'}
                        value={formData.emailSettings.brevo.apiKey}
                        onChange={handleChange}
                        placeholder="输入 Brevo API 密钥"
                        className="pr-20"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-600"
                          onClick={() => setShowBrevoKey(!showBrevoKey)}
                        >
                          {showBrevoKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => testConnection('brevo')}
                          disabled={testingConnection === 'brevo'}
                          className="text-xs px-2 py-1 h-6"
                        >
                          {testingConnection === 'brevo' ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            '测试'
                          )}
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      在 <a href="https://app.brevo.com/settings/keys/api" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Brevo 控制台</a> 获取您的API密钥
                    </p>
                  </div>
                </div>
              </div>

              {/* MailerSend 设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 flex items-center space-x-2 text-gray-900">
                  <span>📝 MailerSend 设置</span>
                  {hasMailerSendKey && <CheckCircle className="h-5 w-5 text-green-600" />}
                </h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="mailersendFromEmail">发送邮箱地址</Label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="emailSettings.mailersend.enabled"
                          checked={formData.emailSettings.mailersend.enabled}
                          onChange={handleSwitchChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span className="ml-3 text-sm text-gray-700">启用</span>
                      </label>
                    </div>
                    <Input
                      id="mailersendFromEmail"
                      name="emailSettings.mailersend.fromEmail"
                      type="email"
                      value={formData.emailSettings.mailersend.fromEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className="mb-3"
                    />
                    <p className="text-xs text-gray-500 mb-3">
                      MailerSend 发送邮箱地址，必须是在 MailerSend 中已验证的域名
                    </p>
                  </div>

                  <div className="mb-4">
                    <Label htmlFor="mailersendApiKey">API 密钥</Label>
                    <div className="relative">
                      <Input
                        id="mailersendApiKey"
                        name="emailSettings.mailersend.apiKey"
                        type={showMailerSendKey ? 'text' : 'password'}
                        value={formData.emailSettings.mailersend.apiKey}
                        onChange={handleChange}
                        placeholder="输入 MailerSend API 密钥"
                        className="pr-20"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-600"
                          onClick={() => setShowMailerSendKey(!showMailerSendKey)}
                        >
                          {showMailerSendKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => testConnection('mailersend')}
                          disabled={testingConnection === 'mailersend'}
                          className="text-xs px-2 py-1 h-6"
                        >
                          {testingConnection === 'mailersend' ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            '测试'
                          )}
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      在 <a href="https://app.mailersend.com/api-tokens" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">MailerSend 控制台</a> 获取您的API密钥
                    </p>
                  </div>
                </div>
              </div>

              {/* 高级设置 */}
              <div className="mb-6">
                <h2 className="text-lg font-medium mb-4 border-b pb-2 text-gray-900">🔧 高级设置</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-4">
                    <Label htmlFor="retryTimes">重试次数</Label>
                    <Input
                      id="retryTimes"
                      name="emailSettings.retryTimes"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.emailSettings.retryTimes}
                      onChange={handleNumberChange}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      发送失败时的重试次数 (1-10)
                    </p>
                  </div>

                  <div className="mb-4">
                    <Label htmlFor="timeout">超时时间 (毫秒)</Label>
                    <Input
                      id="timeout"
                      name="emailSettings.timeout"
                      type="number"
                      min="5000"
                      max="120000"
                      step="1000"
                      value={formData.emailSettings.timeout}
                      onChange={handleNumberChange}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      API请求超时时间 (5000-120000毫秒)
                    </p>
                  </div>

                  <div className="mb-4">
                    <Label htmlFor="fallbackProvider">备用服务商</Label>
                    <select
                      id="fallbackProvider"
                      name="emailSettings.fallbackProvider"
                      value={formData.emailSettings.fallbackProvider || ''}
                      onChange={handleSelectChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mt-1"
                    >
                      <option value="">无备用服务商</option>
                      <option value="resend">Resend</option>
                      <option value="brevo">Brevo</option>
                      <option value="mailersend">MailerSend</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      主服务商失败时自动切换的备用服务商
                    </p>
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableLogs">启用发送日志</Label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          id="enableLogs"
                          name="emailSettings.enableLogs"
                          checked={formData.emailSettings.enableLogs}
                          onChange={handleSwitchChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      记录邮件发送日志用于调试
                    </p>
                  </div>
                </div>
              </div>

              {/* 保存按钮 */}
              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  disabled={isSaving}
                  loading={isSaving}
                >
                  {isSaving ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default EmailSettings;
