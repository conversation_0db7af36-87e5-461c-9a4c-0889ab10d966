import axios from 'axios';

// API基础URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

// 配置类型定义
export interface SiteInfo {
  siteName: string;
  siteDescription: string;
  siteKeywords: string;
  copyright: string;
  socialLinks: {
    twitter: string;
    telegram: string;
    facebook: string;
    instagram: string;
    github: string;
  };
  defaultLanguage: 'zh' | 'en';
}

export interface PublicConfig {
  siteInfo: SiteInfo;
  watermark: {
    siteDomain: string;
  };
}

/**
 * 配置服务
 * 用于获取和缓存网站基本信息
 */
class ConfigService {
  private static instance: ConfigService;
  private config: PublicConfig | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 缓存有效期：5分钟

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取ConfigService单例
   */
  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return (
      this.config !== null &&
      Date.now() - this.lastFetchTime < this.CACHE_TTL
    );
  }

  /**
   * 从API获取配置
   */
  private async fetchConfig(): Promise<PublicConfig> {
    try {
      const response = await axios.get(`${API_URL}/config`);
      if (response.data.success) {
        this.config = response.data.config;
        this.lastFetchTime = Date.now();
        return this.config as PublicConfig;
      } else {
        throw new Error('获取配置失败');
      }
    } catch (error) {
      console.error('获取配置错误:', error);
      throw error;
    }
  }

  /**
   * 获取公共配置
   * 优先从缓存获取，缓存失效则从API重新加载
   */
  public async getConfig(): Promise<PublicConfig> {
    if (!this.isCacheValid()) {
      return this.fetchConfig();
    }
    return this.config as PublicConfig;
  }

  /**
   * 获取网站名称
   */
  public async getSiteName(): Promise<string> {
    const config = await this.getConfig();
    return config.siteInfo.siteName;
  }

  /**
   * 获取网站描述
   */
  public async getSiteDescription(): Promise<string> {
    const config = await this.getConfig();
    return config.siteInfo.siteDescription;
  }

  /**
   * 获取网站关键词
   */
  public async getSiteKeywords(): Promise<string> {
    const config = await this.getConfig();
    return config.siteInfo.siteKeywords;
  }



  /**
   * 获取版权信息
   */
  public async getCopyright(): Promise<string> {
    const config = await this.getConfig();
    return config.siteInfo.copyright;
  }

  /**
   * 获取默认语言
   */
  public async getDefaultLanguage(): Promise<'zh' | 'en'> {
    const config = await this.getConfig();
    return config.siteInfo.defaultLanguage;
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    console.log('清除配置缓存');
    this.config = null;
    this.lastFetchTime = 0;
  }

  /**
   * 强制刷新配置
   * 用于配置更新后强制刷新
   */
  public async refreshConfig(): Promise<PublicConfig> {
    console.log('强制刷新配置');
    this.clearCache();
    const config = await this.fetchConfig();
    console.log('刷新后的配置:', config);
    return config;
  }

  /**
   * 获取水印域名
   * 优先使用系统配置的站点域名，备选使用当前访问域名
   */
  public async getWatermarkDomain(): Promise<string> {
    try {
      const config = await this.getConfig();
      // 优先使用系统配置的站点域名
      if (config.watermark && config.watermark.siteDomain) {
        return config.watermark.siteDomain;
      }
    } catch (error) {
      console.warn('获取系统配置域名失败:', error);
    }

    // 备选方案：使用当前访问域名
    const hostname = window.location.hostname;
    return hostname;
  }
}

// 导出单例实例
export default ConfigService.getInstance();
