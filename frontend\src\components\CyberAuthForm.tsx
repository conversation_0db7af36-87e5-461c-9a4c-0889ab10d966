import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Mail, Lock, Eye, EyeOff, Ticket } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import GeometricLogo from './GeometricLogo';
import configService, { SiteInfo } from '../services/configService';

interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required?: boolean;
}

interface CyberAuthFormProps {
  title: string;
  fields: FormField[];
  submitText: string;
  onSubmit: (formData: Record<string, string>) => void;
  isLoading: boolean;
  error: string | null;
  footerText?: React.ReactNode;
  forgotPasswordLink?: React.ReactNode;
  showLogo?: boolean;
  onChange?: (formData: Record<string, string>) => void;
}

const CyberAuthForm: React.FC<CyberAuthFormProps> = ({
  title,
  fields,
  submitText,
  onSubmit,
  isLoading,
  error,
  footerText,
  forgotPasswordLink,
  showLogo = true,
  onChange
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Record<string, string>>(
    fields.reduce((acc, field) => ({ ...acc, [field.name]: '' }), {})
  );
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        const config = await configService.getConfig();
        setSiteInfo(config.siteInfo);
      } catch (error) {
        console.error('加载网站配置失败:', error);
      }
    };

    loadSiteInfo();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newFormData = { ...formData, [name]: value };
    setFormData(newFormData);

    // 调用onChange回调
    if (onChange) {
      onChange(newFormData);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPassword(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  // 获取字段图标
  const getFieldIcon = (fieldName: string, fieldType: string) => {
    if (fieldName === 'email') return Mail;
    if (fieldType === 'password') return Lock;
    if (fieldName === 'inviteCode') return Ticket;
    return Mail; // 默认图标
  };

  // 获取字段标签（保持原始翻译）
  const getFieldLabel = (_fieldName: string, originalLabel: string) => {
    return originalLabel;
  };

  // 获取字段占位符（保持原始翻译）
  const getFieldPlaceholder = (_fieldName: string, originalPlaceholder: string) => {
    return originalPlaceholder;
  };

  return (
    <Card className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border rounded-2xl animate-fade-in relative overflow-hidden">
      {/* Card glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
      <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

      <CardHeader className="space-y-8 text-center py-10 relative z-10">
        {/* Logo - 点击返回首页 */}
        {showLogo && (
          <div className="flex justify-center">
            <GeometricLogo
              to="/"
              siteName={siteInfo?.siteName || t('app.title')}
              size="xl"
              showText={false}
              className="scale-125"
            />
          </div>
        )}

        {/* 标题 */}
        <div className="space-y-1">
          <CardTitle className="text-3xl font-bold text-cyber-text tracking-wider font-sans">{title}</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="space-y-5 px-8 pb-10 relative z-10">
        {error && (
          <div className={`mb-4 p-3 rounded-xl text-center font-mono text-sm ${
            error.includes('会话') || error.includes('登出') || error.includes('session') || error.includes('expired')
              ? 'bg-yellow-500/10 border border-yellow-500/30 text-yellow-400'
              : 'bg-red-500/10 border border-red-500/30 text-red-400'
          }`}>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {fields.map((field) => {
            const FieldIcon = getFieldIcon(field.name, field.type);
            const isPasswordField = field.type === 'password';
            const fieldType = isPasswordField && showPassword[field.name] ? 'text' : field.type;

            return (
              <div key={field.name}>
                <div className="space-y-2">
                  <Label
                    htmlFor={field.name}
                    className="text-sm font-bold text-cyber-text flex items-center gap-2 font-mono tracking-wider"
                  >
                    <FieldIcon className="h-4 w-4 text-cyber-muted" />
                    {getFieldLabel(field.name, field.label)}
                  </Label>
                  <div className="relative">
                    <Input
                      id={field.name}
                      name={field.name}
                      type={fieldType}
                      value={formData[field.name]}
                      onChange={handleChange}
                      placeholder={getFieldPlaceholder(field.name, field.placeholder)}
                      required={field.required !== false}
                      className="w-full h-12 px-4 rounded-xl bg-cyber-border/5 border border-cyber-cyan/30 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/80 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 autofill:bg-cyber-border/30 autofill:text-cyber-text autofill:shadow-[inset_0_0_0px_1000px_rgba(22,33,62,0.3)]"
                    />
                    {isPasswordField && (
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility(field.name)}
                        className="absolute right-4 top-1/2 -translate-y-1/2 text-cyber-muted hover:text-cyber-text transition-colors duration-200 outline-none focus:outline-none"
                      >
                        {showPassword[field.name] ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    )}
                  </div>
                </div>

                {/* 在密码字段后显示忘记密码链接 */}
                {isPasswordField && forgotPasswordLink && (
                  <div className="mt-4 mb-0 text-right text-sm text-cyber-muted font-mono">
                    {forgotPasswordLink}
                  </div>
                )}
              </div>
            );
          })}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full h-14 bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-bold text-base rounded-xl shadow-lg hover:shadow-[0_0_30px_rgba(0,245,255,0.4)] transition-all duration-300 active:scale-[0.98] font-mono tracking-wider outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          >
            {isLoading ? t('common.processing') : submitText}
          </Button>
        </form>

        {/* Footer Text */}
        {footerText && (
          <div className="text-center text-sm text-cyber-muted pt-4 border-t border-cyber-border/30 font-mono">
            {footerText}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CyberAuthForm;
