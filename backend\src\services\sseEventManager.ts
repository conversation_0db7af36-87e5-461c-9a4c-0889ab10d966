import { EventEmitter } from 'events';
import logger from '../utils/logger';

/**
 * SSE事件类型定义
 */
export interface SSEEventData {
  type: 'kline-update' | 'prediction-update' | 'prediction-line-update' | 'database-update';
  data: any;
  metadata?: any;
}

/**
 * SSE事件管理器
 * 统一管理所有SSE相关的事件发布和订阅
 * 解耦数据生成服务和SSE推送服务
 */
class SSEEventManager extends EventEmitter {
  private static instance: SSEEventManager;

  private constructor() {
    super();
    this.setupEventListeners();
    logger.info('SSE事件管理器已启动');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SSEEventManager {
    if (!SSEEventManager.instance) {
      SSEEventManager.instance = new SSEEventManager();
    }
    return SSEEventManager.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听所有SSE事件并记录日志
    this.on('sse-event', (eventData: SSEEventData) => {
      logger.debug('SSE事件发布', {
        type: eventData.type,
        dataSize: Array.isArray(eventData.data) ? eventData.data.length : 1,
        metadata: eventData.metadata
      });
    });

    // 错误处理
    this.on('error', (error) => {
      logger.error('SSE事件管理器错误', error);
    });
  }

  /**
   * 发布K线数据更新事件
   * @param klineData K线数据
   * @param metadata 元数据
   */
  public publishKlineUpdate(klineData: any[], metadata?: any): void {
    const eventData: SSEEventData = {
      type: 'kline-update',
      data: klineData,
      metadata
    };
    
    this.emit('sse-event', eventData);
    this.emit('kline-update', eventData);
  }

  // 预测数据发布方法已移除，改为仅通过数据库更新通知

  /**
   * 发布数据库更新事件
   * @param updateInfo 更新信息
   * @param metadata 元数据
   */
  public publishDatabaseUpdate(updateInfo?: any, metadata?: any): void {
    const eventData: SSEEventData = {
      type: 'database-update',
      data: updateInfo || { message: '数据库数据已更新' },
      metadata
    };
    
    this.emit('sse-event', eventData);
    this.emit('database-update', eventData);
  }

  /**
   * 订阅K线数据更新事件
   * @param callback 回调函数
   */
  public onKlineUpdate(callback: (eventData: SSEEventData) => void): void {
    this.on('kline-update', callback);
  }

  // 预测数据订阅方法已移除，改为仅通过数据库更新通知

  /**
   * 订阅数据库更新事件
   * @param callback 回调函数
   */
  public onDatabaseUpdate(callback: (eventData: SSEEventData) => void): void {
    this.on('database-update', callback);
  }

  /**
   * 获取事件统计信息
   */
  public getStats(): {
    listenerCount: {
      klineUpdate: number;
      predictionUpdate: number;
      predictionLineUpdate: number;
      databaseUpdate: number;
    };
    maxListeners: number;
  } {
    return {
      listenerCount: {
        klineUpdate: this.listenerCount('kline-update'),
        predictionUpdate: this.listenerCount('prediction-update'),
        predictionLineUpdate: this.listenerCount('prediction-line-update'),
        databaseUpdate: this.listenerCount('database-update')
      },
      maxListeners: this.getMaxListeners()
    };
  }

  /**
   * 销毁事件管理器
   */
  public destroy(): void {
    this.removeAllListeners();
    logger.info('SSE事件管理器已销毁');
  }
}

// 导出单例实例
export default SSEEventManager.getInstance();
