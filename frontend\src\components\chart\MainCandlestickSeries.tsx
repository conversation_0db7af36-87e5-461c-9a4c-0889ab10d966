/**
 * 主K线图系列组件
 * 
 * 该组件负责管理主K线数据的加载、显示和更新，
 * 从Chart.tsx中提取，作为独立的可复用组件
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { IChartApi, ISeriesApi, Time, CandlestickData, Range } from 'lightweight-charts';
import { KLineData } from '../../types/chartTypes';
import { timeToNumber } from '../../utils/chartUtils';

interface MainCandlestickSeriesProps {
  chartApi: IChartApi | null;
  isChartReady: boolean;
  candleData: KLineData[];
  isLoading?: boolean;
  onLoadMoreHistory?: (oldestTime: number) => void;
  onSeriesReady?: (series: ISeriesApi<"Candlestick">) => void;
  candlestickSeries?: ISeriesApi<"Candlestick"> | null; // 从图表管理器获取的K线系列
}

/**
 * 主K线图系列组件
 * 负责管理主K线图系列的数据更新和历史加载
 */
const MainCandlestickSeries: React.FC<MainCandlestickSeriesProps> = ({
  chartApi,
  isChartReady,
  candleData,
  isLoading = false,
  onLoadMoreHistory,
  onSeriesReady,
  candlestickSeries
}) => {
  // 引用存储
  const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  const oldestLoadedTimeRef = useRef<number | null>(null);
  const isLoadingMoreRef = useRef<boolean>(false);
  const isInitialLoadDoneRef = useRef<boolean>(false);
  const initialRenderTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFirstLoadRef = useRef<boolean>(true);
  const latestCandleRef = useRef<KLineData | null>(null);
  
  // 状态管理
  const [isSeriesReady, setIsSeriesReady] = useState<boolean>(false);

  // 初始化K线系列引用
  useEffect(() => {
    // 检查是否提供了K线系列
    if (!candlestickSeries) {
      console.warn('未提供K线系列实例');
      return;
    }
      
      // 保存引用
    mainSeriesRef.current = candlestickSeries;
    setIsSeriesReady(true);
      
      // 如果有回调函数，将系列实例传递给父组件
      if (onSeriesReady) {
      onSeriesReady(candlestickSeries);
      }
      
    console.log('使用图表管理器提供的K线系列');
    
    // 组件卸载时清理定时器
    return () => {
      if (initialRenderTimeoutRef.current) {
        clearTimeout(initialRenderTimeoutRef.current);
      }
    };
  }, [candlestickSeries, onSeriesReady]);

  // 更新主K线数据
  useEffect(() => {
    if (!mainSeriesRef.current || !isSeriesReady || candleData.length === 0) {
      return;
    }
    
    console.log(`更新主K线数据, 数据条数: ${candleData.length}`);
    
    try {
      // 更新数据 - 创建数据副本，防止修改原始数据
      mainSeriesRef.current.setData(candleData.map(item => ({...item})) as CandlestickData[]);
      
      // 首次加载时延迟设置初始加载完成标志
      if (isFirstLoadRef.current) {
        console.log('首次加载K线数据，初始化视图');
        
        // 标记为非首次加载
        isFirstLoadRef.current = false;
        
        // 延迟后才允许触发历史加载
        if (initialRenderTimeoutRef.current) {
          clearTimeout(initialRenderTimeoutRef.current);
        }
        initialRenderTimeoutRef.current = setTimeout(() => {
          isInitialLoadDoneRef.current = true;
          console.log('初始加载完成，允许历史数据加载');
        }, 1000);
      }
      
      // 记录最新K线数据
      if (candleData.length > 0) {
        latestCandleRef.current = candleData[candleData.length - 1];
      }
      
      // 记录已加载K线数据中最早的时间戳
      if (candleData.length > 0) {
        const timeValues = candleData.map(item => 
          typeof item.time === 'string' ? parseInt(item.time) : Number(item.time)
        );
        oldestLoadedTimeRef.current = Math.min(...timeValues) * 1000; // 转换为毫秒
        console.log('当前加载的最早K线时间:', new Date(oldestLoadedTimeRef.current).toISOString());
      }
    } catch (error) {
      console.error('更新主K线数据失败:', error);
    }
  }, [candleData, isSeriesReady]);

  // 创建时间范围变化处理函数
  const handleTimeRangeChange = useCallback((timeRange: Range<Time> | null) => {
    if (!timeRange || isLoadingMoreRef.current || !isInitialLoadDoneRef.current || !chartApi || !onLoadMoreHistory) return;
    
    // 检查是否滚动到了左边缘
    const visibleRangeStart = timeToNumber(timeRange.from);
    
    // 获取图表当前时间刻度的宽度
    const barSpacing = chartApi.timeScale().options().barSpacing || 6;
    const visibleTimeSpan = timeToNumber(timeRange.to) - visibleRangeStart;
    
    // 如果视图最左侧时间接近已加载数据的最早时间的5%，触发加载更多
    const leftEdgeThreshold = visibleTimeSpan * 0.05;
    const isNearLeftEdge = oldestLoadedTimeRef.current && 
        (visibleRangeStart * 1000 - leftEdgeThreshold <= oldestLoadedTimeRef.current);
        
    if (isNearLeftEdge && !isLoadingMoreRef.current && oldestLoadedTimeRef.current && isInitialLoadDoneRef.current) {
      isLoadingMoreRef.current = true;
      console.log('已滚动到左边界，加载更早的K线数据...');
      
      // 调用回调函数加载更多数据
      onLoadMoreHistory(oldestLoadedTimeRef.current / 1000);
      
      // 防止频繁触发，设置短暂的冷却时间
      setTimeout(() => {
        isLoadingMoreRef.current = false;
      }, 2000);
    }
  }, [chartApi, onLoadMoreHistory]);

  // 监听时间轴变化，实现滚动加载历史K线数据
  useEffect(() => {
    if (!chartApi || !onLoadMoreHistory || !isChartReady) return;
    
    // 添加监听
    chartApi.timeScale().subscribeVisibleTimeRangeChange(handleTimeRangeChange);
    
    // 清理函数
    return () => {
      chartApi.timeScale().unsubscribeVisibleTimeRangeChange(handleTimeRangeChange);
    };
  }, [chartApi, isChartReady, handleTimeRangeChange, onLoadMoreHistory]);

  // 获取最新K线数据的方法 - 可以被外部组件使用
  const getLatestCandleData = useCallback(() => {
    return latestCandleRef.current;
  }, []);

  // 组件不渲染任何UI元素
  return null;
};

export default MainCandlestickSeries; 