import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import useAdminStore from '../../store/useAdminStore';
import AdminLayout from '../../components/admin/AdminLayout';

interface UserStats {
  todayRegistered: number;
  total: number;
  online: number;
  trial: number;
  subscriber: number;
  normal: number;
}

const AdminDashboard: React.FC = () => {
  const { fetchUsers } = useAdminStore();
  const [userStats, setUserStats] = useState<UserStats>({
    todayRegistered: 0,
    total: 0,
    online: 0,
    trial: 0,
    subscriber: 0,
    normal: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // 获取用户统计数据
    const fetchUserStats = async () => {
      try {
        setIsLoading(true);
        // 获取所有用户，不限分页
        const { users } = await fetchUsers(1, 1000, '');

        // 计算各角色的用户数量
        const today = new Date();
        today.setHours(0, 0, 0, 0); // 今天的开始时间

        const stats: UserStats = {
          todayRegistered: 0,
          total: users.length,
          online: 0,
          trial: 0,
          subscriber: 0,
          normal: 0
        };

        users.forEach((user: any) => {
          // 计算今日注册用户
          const userCreatedAt = new Date(user.createdAt);
          if (userCreatedAt >= today) {
            stats.todayRegistered++;
          }

          // 计算各角色用户数量
          if (user.role === 'trial') stats.trial++;
          if (user.role === 'normal') stats.normal++;
          if (user.role === 'subscriber') stats.subscriber++;
          if (user.isOnline) stats.online++;
        });

        setUserStats(stats);
        setIsLoading(false);
      } catch (err) {
        const message = err instanceof Error ? err.message : '获取数据失败';
        setError(message);
        setIsLoading(false);
      }
    };

    fetchUserStats();
  }, [fetchUsers]);

  const renderStatsCard = (title: string, value: number, bgColor: string, textColor: string = 'text-white') => (
    <div className={`${bgColor} rounded-lg shadow-sm p-6 ${textColor}`}>
      <h3 className="text-lg font-semibold">{title}</h3>
      <p className="text-3xl font-bold mt-2">{value}</p>
    </div>
  );

  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">管理员仪表盘</h1>

        {isLoading ? (
          <div className="flex justify-center my-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 rounded">
            <p>{error}</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {renderStatsCard('今日注册', userStats.todayRegistered, 'bg-green-600')}
              {renderStatsCard('用户总数', userStats.total, 'bg-blue-600')}
              {renderStatsCard('在线用户', userStats.online, 'bg-indigo-600')}
              {renderStatsCard('试用用户', userStats.trial, 'bg-yellow-500')}
              {renderStatsCard('订阅用户', userStats.subscriber, 'bg-purple-600')}
              {renderStatsCard('普通用户', userStats.normal, 'bg-gray-600')}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">快速导航</h2>
                <div className="grid grid-cols-2 gap-4">
                  <Link
                    to="/admin/users"
                    className="flex items-center justify-center p-3 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg transition-colors"
                  >
                    <span className="text-blue-700 font-medium">用户管理</span>
                  </Link>

                  <Link
                    to="/admin/payments"
                    className="flex items-center justify-center p-3 bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg transition-colors"
                  >
                    <span className="text-purple-700 font-medium">支付管理</span>
                  </Link>
                  <Link
                    to="/admin/feedback"
                    className="flex items-center justify-center p-3 bg-orange-50 hover:bg-orange-100 border border-orange-200 rounded-lg transition-colors"
                  >
                    <span className="text-orange-700 font-medium">用户反馈</span>
                  </Link>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">系统状态</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">系统运行状态</span>
                    <span className="text-green-600 font-medium">正常</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">当前在线用户</span>
                    <span className="text-gray-900 font-medium">{userStats.online}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">服务器时间</span>
                    <span className="text-gray-900 font-medium">{new Date().toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;