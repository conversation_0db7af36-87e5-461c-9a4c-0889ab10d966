import mongoose, { Document, Schema, Model } from 'mongoose';

export interface VerificationCodeDocument extends Document {
  _id: mongoose.Types.ObjectId;
  email: string;
  code: string;
  purpose: 'email-verification' | 'password-reset';
  userId?: mongoose.Types.ObjectId;
  expiresAt: Date;
  attempts: number;
  maxAttempts: number;
  isUsed: boolean;
  createdAt: Date;
  updatedAt: Date;

  // 实例方法
  markAsUsed(): Promise<VerificationCodeDocument>;
  incrementAttempts(): Promise<VerificationCodeDocument>;
  isMaxAttemptsReached(): boolean;
  isExpired(): boolean;
}

export interface VerificationCodeModel extends Model<VerificationCodeDocument> {
  // 静态方法
  findValidCode(email: string, code: string, purpose: string): Promise<VerificationCodeDocument | null>;
  cleanupExpired(): Promise<any>;
  checkRateLimit(email: string, purpose: string, limitMinutes?: number): Promise<VerificationCodeDocument | null>;
}

const verificationCodeSchema = new Schema({
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
    index: true
  },
  code: {
    type: String,
    required: true,
    length: 6,
    match: /^\d{6}$/
  },
  purpose: {
    type: String,
    enum: ['email-verification', 'password-reset'],
    required: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    index: true
  },
  expiresAt: {
    type: Date,
    required: true,
    index: { expireAfterSeconds: 0 } // MongoDB TTL索引，自动删除过期文档
  },
  attempts: {
    type: Number,
    default: 0,
    min: 0
  },
  maxAttempts: {
    type: Number,
    default: 5,
    min: 1,
    max: 10
  },
  isUsed: {
    type: Boolean,
    default: false,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 复合索引：用于快速查找特定邮箱和用途的验证码
verificationCodeSchema.index({ email: 1, purpose: 1, isUsed: 1 });

// 复合索引：用于清理过期的验证码
verificationCodeSchema.index({ expiresAt: 1, isUsed: 1 });

// 更新时间中间件
verificationCodeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 静态方法：查找有效的验证码
verificationCodeSchema.statics.findValidCode = function(email: string, code: string, purpose: string) {
  return this.findOne({
    email: email.toLowerCase(),
    code,
    purpose,
    isUsed: false,
    expiresAt: { $gt: new Date() }
  });
};

// 静态方法：清理过期的验证码
verificationCodeSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { isUsed: true, updatedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // 删除24小时前已使用的验证码
    ]
  });
};

// 静态方法：检查发送频率限制
verificationCodeSchema.statics.checkRateLimit = function(email: string, purpose: string, limitMinutes: number = 1) {
  const limitTime = new Date(Date.now() - limitMinutes * 60 * 1000);
  return this.findOne({
    email: email.toLowerCase(),
    purpose,
    createdAt: { $gt: limitTime }
  });
};

// 实例方法：标记为已使用
verificationCodeSchema.methods.markAsUsed = function() {
  this.isUsed = true;
  this.updatedAt = new Date();
  return this.save();
};

// 实例方法：增加尝试次数
verificationCodeSchema.methods.incrementAttempts = function() {
  this.attempts += 1;
  this.updatedAt = new Date();
  return this.save();
};

// 实例方法：检查是否已达到最大尝试次数
verificationCodeSchema.methods.isMaxAttemptsReached = function() {
  return this.attempts >= this.maxAttempts;
};

// 实例方法：检查是否已过期
verificationCodeSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

const VerificationCode = mongoose.model<VerificationCodeDocument, VerificationCodeModel>('VerificationCode', verificationCodeSchema);

export default VerificationCode;
