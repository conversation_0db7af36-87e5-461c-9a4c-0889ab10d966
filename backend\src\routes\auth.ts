import express from 'express';
import authController from '../controllers/authController';
import authenticate from '../middlewares/authMiddleware';

const router = express.Router();

// 注册新用户
router.post('/register', authController.register);

// 用户登录
router.post('/login', authController.login);

// 验证邮箱
router.post('/verify-email', authController.verifyEmail);

// 忘记密码 - 发送重置密码邮件
router.post('/forgot-password', authController.forgotPassword);

// 验证重置密码token
router.post('/validate-reset-token', authController.validateResetToken);

// 重置密码
router.post('/reset-password', authController.resetPassword);

// 获取当前用户信息（需要认证）
router.get('/me', authenticate, authController.getMe);

// 修改密码（需要认证）
router.post('/change-password', authenticate, authController.changePassword);

// 刷新访问令牌
router.post('/refresh', authController.refresh);

// 发送验证码
router.post('/send-verification-code', authController.sendVerificationCode);

// 验证验证码
router.post('/verify-code', authController.verifyCode);

export default router;