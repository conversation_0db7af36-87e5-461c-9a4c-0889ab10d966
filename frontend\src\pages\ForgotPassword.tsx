import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CyberAuthForm from '../components/CyberAuthForm';
import useUserStore from '../store/useUserStore';

const ForgotPassword: React.FC = () => {
  const { t } = useTranslation();
  const { forgotPassword, isLoading, error, clearError, isInitializing } = useUserStore();
  const [emailSent, setEmailSent] = useState(false);

  // 表单错误状态（5秒自动消失）
  const [formError, setFormError] = useState<string | null>(null);
  const [errorTimer, setErrorTimer] = useState<NodeJS.Timeout | null>(null);
  
  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimer) {
        clearTimeout(errorTimer);
      }
    };
  }, [errorTimer]);

  // 设置表单错误，5秒后自动清除
  const setFormErrorWithTimeout = (message: string) => {
    // 清除之前的定时器
    if (errorTimer) {
      clearTimeout(errorTimer);
    }

    // 设置错误信息
    setFormError(message);

    // 5秒后清除错误信息
    const timer = setTimeout(() => {
      setFormError(null);
      setErrorTimer(null);
    }, 5000);

    setErrorTimer(timer);
  };

  // 手动清除表单错误
  const clearFormError = () => {
    if (errorTimer) {
      clearTimeout(errorTimer);
      setErrorTimer(null);
    }
    setFormError(null);
  };

  // 处理表单输入变化，清除错误提示
  const handleFormChange = (formData: Record<string, string>) => {
    // 当用户开始输入时，清除表单错误
    if (formError) {
      clearFormError();
    }
  };

  // 判断错误是否需要5秒自动消失（忘记密码页面的所有错误都应该自动消失）
  const shouldAutoHideError = (errorMessage: string) => {
    // 忘记密码页面的错误都是邮箱相关的，应该自动消失
    return true;
  };

  // 监听store中的error变化，判断是否需要5秒自动消失
  useEffect(() => {
    if (error && shouldAutoHideError(error)) {
      // 需要自动消失的错误，设置到formError中
      setFormErrorWithTimeout(error);
      clearError(); // 清除store中的error，避免重复显示
    }
  }, [error, clearError]);
  
  // 处理忘记密码表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // 清除之前的表单错误
      clearFormError();
      clearError();

      await forgotPassword(formData.email);
      setEmailSent(true);
    } catch (error) {
      // 错误已在store中处理，会通过useEffect自动设置为5秒消失
      console.error('发送重置密码邮件失败', error);
    }
  };
  
  // 忘记密码表单字段配置
  const fields = [
    {
      name: 'email',
      label: t('auth.email'),
      type: 'email',
      placeholder: t('auth.emailPlaceholder')
    }
  ];

  // 底部登录链接
  const footerText = (
    <>
      {t('auth.rememberPassword')}{' '}
      <Link to="/login" className="font-bold text-cyber-cyan hover:text-cyber-cyan/80 transition-colors duration-200 outline-none focus:outline-none">
        {t('auth.backToSignIn')}
      </Link>
    </>
  );
  
  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
    );
  }
  
  // 如果邮件已发送，显示提示信息
  if (emailSent) {
    return (
      <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
        <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

        <div className="relative z-10">
          <div className="text-cyber-green mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('auth.emailSent')}</h2>
          <p className="text-cyber-muted mb-4 font-mono">{t('auth.resetEmailSentDesc')}</p>
          <p className="text-cyber-muted mb-6 font-mono">{t('auth.resetEmailNote')}</p>
          <Link
            to="/login"
            className="inline-block text-cyber-cyan hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm border-cyber-cyan/30 border hover:border-cyber-cyan/60 py-2 px-6 rounded-xl"
          >
            {t('auth.backToSignIn')}
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full max-w-md">
      <CyberAuthForm
        title={t('auth.forgotPassword')}
        fields={fields}
        submitText={t('auth.sendResetLink')}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={formError || error}
        footerText={footerText}
        showLogo={false}
        onChange={handleFormChange}
      />
    </div>
  );
};

export default ForgotPassword; 