import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import inviteApi from '../api/invite';
import { Button } from './ui/button';
import { Loader2 } from 'lucide-react';
import { toast } from './ui/use-toast';
import { formatDateTime } from '../utils/dateFormatter';

// 邀请码类型定义
interface InviteCode {
  _id: string;
  code: string;
  createdBy: string;
  usedBy: {
    _id: string;
    email: string;
    username: string;
  } | null;
  usedAt: string | null;
  createdAt: string;
}

// 组件属性类型
interface InviteCodesListProps {
  className?: string;
}

const InviteCodesList: React.FC<InviteCodesListProps> = ({ className = '' }) => {
  const { t, i18n } = useTranslation();
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 格式化时间为本地时间
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return formatDateTime(dateString, i18n.language);
  };

  // 复制邀请码到剪贴板
  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code).then(
      () => {
        toast({
          title: t('common.copySuccess'),
          description: t('common.copySuccess'),
          variant: 'default'
        });
      },
      (err) => {
        console.error('复制失败:', err);
        toast({
          title: t('common.copyFailed'),
          description: t('common.copyFailed'),
          variant: 'destructive'
        });
      }
    );
  };

  // 获取邀请码列表
  useEffect(() => {
    const fetchInviteCodes = async () => {
      try {
        setLoading(true);
        const data = await inviteApi.getMyInviteCodes();
        setInviteCodes(data);
        setError(null);
      } catch (err) {
        setError(t('errors.dataLoadFailed'));
        console.error('获取邀请码失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchInviteCodes();
  }, []);

  // 显示加载中状态
  if (loading) {
    return (
      <div className={`flex justify-center items-center py-12 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-cyber-cyan" />
        <span className="ml-2 text-cyber-muted font-mono">{t('common.loading')}</span>
      </div>
    );
  }

  // 显示错误信息
  if (error) {
    return (
      <div className={`bg-cyber-pink/10 border border-cyber-pink/30 text-cyber-pink px-4 py-3 rounded-xl text-sm font-mono backdrop-blur-sm ${className}`}>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 邀请码管理卡片 */}
      <div className="border border-cyber-cyan/30 rounded-xl p-5 bg-cyber-dark/50 backdrop-blur-sm">

        {/* 如果没有邀请码 */}
        {inviteCodes.length === 0 ? (
          <div className="p-4 rounded-xl text-center border border-cyber-cyan/20 bg-cyber-dark/30">
            <p className="text-cyber-muted text-sm font-mono">{t('common.noInviteCodes')}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-cyber-cyan/10 border-b border-cyber-cyan/20">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">
                    {t('auth.inviteCode')}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">
                    {t('common.usageStatus')}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">
                    {t('common.usageTime')}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">
                    {t('common.operation')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-cyber-cyan/20">
                {inviteCodes.map((inviteCode) => (
                  <tr key={inviteCode._id} className="hover:bg-cyber-cyan/5 transition-colors">
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <div className="font-mono">{inviteCode.code}</div>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {inviteCode.usedBy ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-pink/20 text-cyber-pink border border-cyber-pink/30 font-mono">
                          {t('common.used')}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-cyan/20 text-cyber-cyan border border-cyber-cyan/30 font-mono">
                          {t('common.available')}
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-cyber-muted font-mono">
                      {formatDate(inviteCode.usedAt)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {!inviteCode.usedBy ? (
                        <Button
                          onClick={() => copyToClipboard(inviteCode.code)}
                          variant="ghost"
                          size="sm"
                          className="text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
                        >
                          {t('common.copy')}
                        </Button>
                      ) : (
                        <span className="text-cyber-muted text-sm font-mono">{t('common.used')}</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default InviteCodesList; 