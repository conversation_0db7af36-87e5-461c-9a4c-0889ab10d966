import React, { useState, useEffect } from 'react';
import configService, { SiteInfo } from '../../services/configService';
import useUserStore from '../../store/useUserStore';
import GeometricLogo from '../GeometricLogo';
import DocsLanguageSwitcher from './DocsLanguageSwitcher';

const DocsNavbar: React.FC = () => {
  const { isAuthenticated } = useUserStore();
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        // 强制刷新配置以获取最新的logo
        const config = await configService.refreshConfig();
        console.log('文档页面加载的配置:', config.siteInfo);
        setSiteInfo(config.siteInfo);
      } catch (error) {
        console.error('加载网站配置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSiteInfo();
  }, []);

  // 根据用户登录状态决定logo点击跳转的目标
  const getLogoLink = () => {
    return isAuthenticated ? '/dashboard' : '/';
  };

  if (isLoading) {
    return (
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div>
            <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="w-full bg-white border-b border-gray-200 relative z-10">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side: Logo */}
          <div className="flex items-center">
            <GeometricLogo
              to={getLogoLink()}
              siteName={siteInfo?.siteName || 'BTC 预测'}
              size="xl"
              className="[&_span]:!text-gray-800"
            />
          </div>

          {/* Right side: Language Switcher */}
          <div className="flex items-center space-x-4">
            <DocsLanguageSwitcher />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default DocsNavbar;
