import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';

interface Feedback {
  _id: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
}

interface FeedbackDetailProps {
  feedback: Feedback;
  onClose: () => void;
}

const FeedbackDetail: React.FC<FeedbackDetailProps> = ({ feedback, onClose }) => {
  const { t, i18n } = useTranslation();

  // 格式化日期
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '暂无';

    const date = new Date(dateString);
    return date.toLocaleString(i18n.language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 渲染状态标签
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-purple/20 text-cyber-purple border border-cyber-purple/30 font-mono">{t('feedback.pending')}</span>;
      case 'processing':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-cyan/20 text-cyber-cyan border border-cyber-cyan/30 font-mono">{t('feedback.processing')}</span>;
      case 'replied':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-green/20 text-cyber-green border border-cyber-green/30 font-mono">{t('feedback.replied')}</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-muted/20 text-cyber-muted border border-cyber-muted/30 font-mono">{t('feedback.unknownStatus')}</span>;
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-cyber-card/90 backdrop-blur-xl border border-cyber-cyan/30 rounded-2xl shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-cyber-cyan font-mono">{feedback.title}</DialogTitle>
        </DialogHeader>

        <div className="mt-4 space-y-4">
          <div className="space-y-2">
            <div className="text-sm text-cyber-muted font-mono">{t('feedback.submitTime')}</div>
            <div className="text-sm text-cyber-text font-mono">{formatDate(feedback.createdAt)}</div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-cyber-muted font-mono">{t('feedback.status')}</div>
            <div>{renderStatusBadge(feedback.status)}</div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-cyber-muted font-mono">{t('feedback.problemDescription')}</div>
            <div className="p-3 border border-cyber-cyan/20 bg-cyber-dark/30 rounded-xl text-sm text-cyber-text font-mono backdrop-blur-sm">
              {feedback.content}
            </div>
          </div>

          {feedback.status === 'replied' && feedback.adminReply && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-cyber-green font-mono">{t('feedback.adminReply')} ({formatDate(feedback.replyAt)})</div>
              <div className="p-3 border border-cyber-green/20 bg-cyber-green/5 rounded-xl text-sm text-cyber-text font-mono backdrop-blur-sm">
                {feedback.adminReply}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            onClick={onClose}
            className="bg-cyber-cyan/10 hover:bg-cyber-cyan/20 border border-cyber-cyan/30 hover:border-cyber-cyan/50 text-cyber-cyan hover:text-white font-mono transition-all duration-200"
          >
            {t('common.close')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FeedbackDetail;
