import fs from 'fs';
import path from 'path';
import systemSettingsService from './systemSettingsService';
import logger from '../utils/logger';

class LogCleanupService {
  private logsDir = path.join(process.cwd(), 'logs');

  /**
   * 自动清理过期日志（定时任务调用）
   */
  async autoCleanup(): Promise<void> {
    try {
      const settings = await systemSettingsService.getSystemSettings();
      
      if (!settings.logSettings.enableAutoCleanup) {
        logger.info('自动日志清理已禁用');
        return;
      }

      const retentionDays = settings.logSettings.retentionDays;
      const result = await this.cleanupOldLogs(retentionDays);
      
      logger.info('自动日志清理完成', {
        retentionDays,
        deletedCount: result.deletedCount,
        freedSpace: this.formatSize(result.freedSpace)
      });
    } catch (error) {
      logger.error('自动日志清理失败', error);
    }
  }

  /**
   * 手动清理日志（管理员后台调用）
   */
  async manualCleanup(days: number): Promise<{deletedCount: number, freedSpace: number}> {
    logger.info(`开始手动清理${days}天前的日志`);
    const result = await this.cleanupOldLogs(days);
    logger.info('手动日志清理完成', {
      days,
      deletedCount: result.deletedCount,
      freedSpace: this.formatSize(result.freedSpace)
    });
    return result;
  }

  /**
   * 清理指定天数之前的日志
   */
  private async cleanupOldLogs(days: number): Promise<{deletedCount: number, freedSpace: number}> {
    if (!fs.existsSync(this.logsDir)) {
      return { deletedCount: 0, freedSpace: 0 };
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const files = fs.readdirSync(this.logsDir);
    let deletedCount = 0;
    let freedSpace = 0;

    for (const file of files) {
      if (!file.endsWith('.log')) continue;

      const filePath = path.join(this.logsDir, file);
      
      try {
        const stats = fs.statSync(filePath);
        
        // 检查文件修改时间是否超过保留期
        if (stats.mtime < cutoffDate) {
          freedSpace += stats.size;
          fs.unlinkSync(filePath);
          deletedCount++;
          logger.info(`删除过期日志文件: ${file}`);
        }
      } catch (error) {
        logger.error(`删除日志文件失败: ${file}`, error);
      }
    }

    return { deletedCount, freedSpace };
  }

  /**
   * 获取日志文件统计信息
   */
  async getLogStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile: string | null;
    newestFile: string | null;
  }> {
    if (!fs.existsSync(this.logsDir)) {
      return { totalFiles: 0, totalSize: 0, oldestFile: null, newestFile: null };
    }

    const files = fs.readdirSync(this.logsDir)
      .filter(file => file.endsWith('.log'))
      .map(file => {
        const filePath = path.join(this.logsDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          mtime: stats.mtime
        };
      })
      .sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    return {
      totalFiles: files.length,
      totalSize,
      oldestFile: files.length > 0 ? files[0].name : null,
      newestFile: files.length > 0 ? files[files.length - 1].name : null
    };
  }

  /**
   * 获取日志文件列表
   */
  async getLogFiles(): Promise<Array<{
    name: string;
    size: number;
    date: string;
    mtime: Date;
  }>> {
    if (!fs.existsSync(this.logsDir)) {
      return [];
    }

    const files = fs.readdirSync(this.logsDir)
      .filter(file => file.endsWith('.log') || /\.log\.\d+$/.test(file))
      .map(file => {
        const filePath = path.join(this.logsDir, file);
        const stats = fs.statSync(filePath);
        const dateMatch = file.match(/(\d{4}-\d{2}-\d{2})/);
        return {
          name: file,
          size: stats.size,
          date: dateMatch ? dateMatch[1] : '',
          mtime: stats.mtime
        };
      })
      .sort((a, b) => b.mtime.getTime() - a.mtime.getTime()); // 按修改时间倒序

    return files;
  }

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取日志文件路径（用于下载）
   */
  getLogFilePath(filename: string): string {
    return path.join(this.logsDir, filename);
  }
}

export default new LogCleanupService();
