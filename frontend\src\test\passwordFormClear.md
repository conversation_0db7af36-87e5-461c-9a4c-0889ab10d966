# 密码表单清空功能测试

## 问题描述
修改密码页面在侧边栏切换菜单后，密码字段仍然显示之前输入的内容，而问题反馈页面的内容会被清空。

## 问题原因
1. **修改密码表单**：直接写在 UserCenter 组件内部，使用组件级别的 useState
2. **问题反馈表单**：通过独立组件渲染，切换时组件会卸载和重新挂载

## 解决方案
添加 useEffect 监听 activeTab 变化，当离开密码页面时自动清空密码表单。

## 测试步骤

### 修复前的行为：
1. 进入用户中心 → 修改密码页面
2. 输入当前密码、新密码、确认密码
3. 切换到其他菜单（如账户信息）
4. 再切换回修改密码页面
5. **问题**：密码字段仍然显示之前输入的内容 ❌

### 修复后的预期行为：
1. 进入用户中心 → 修改密码页面
2. 输入当前密码、新密码、确认密码
3. 切换到其他菜单（如账户信息）
4. 再切换回修改密码页面
5. **修复**：密码字段已被清空 ✅

### 对比测试：
1. 进入用户中心 → 问题反馈页面
2. 输入反馈标题和内容
3. 切换到其他菜单
4. 再切换回问题反馈页面
5. **预期**：表单内容被清空（保持原有行为）✅

## 安全性考虑
- 密码字段在切换菜单时自动清空，避免敏感信息在内存中停留过久
- 提高了用户体验的一致性
- 符合安全最佳实践

## 代码变更
```typescript
// 添加清空密码表单的函数
const clearPasswordForm = () => {
  setOldPassword('');
  setNewPassword('');
  setConfirmPassword('');
  setPasswordError(null);
  setPasswordSuccess(null);
};

// 监听activeTab变化
useEffect(() => {
  if (activeTab !== 'password') {
    clearPasswordForm();
  }
}, [activeTab]);
```

## 验证要点
1. ✅ 密码表单在切换菜单时被清空
2. ✅ 错误和成功消息也被清空
3. ✅ 不影响其他表单的正常功能
4. ✅ 提高了安全性和用户体验一致性
