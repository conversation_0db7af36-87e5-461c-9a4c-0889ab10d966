import axios from 'axios';
import systemSettingsService from './systemSettingsService';

/**
 * 翻译服务类
 * 支持 DeepL 和 Google Translate API
 */
class TranslationService {
  private deeplApiKey: string;
  private googleApiKey: string;
  private preferredService: 'deepl' | 'google';
  private enableTranslation: boolean;

  constructor() {
    // 初始化时使用环境变量作为默认值
    this.deeplApiKey = process.env.DEEPL_API_KEY || '';
    this.googleApiKey = process.env.GOOGLE_TRANSLATE_API_KEY || '';
    this.preferredService = process.env.PREFERRED_TRANSLATION_SERVICE as 'deepl' | 'google' || 'deepl';
    this.enableTranslation = false;
  }

  /**
   * 从数据库加载翻译配置
   */
  private async loadConfig() {
    try {
      const settings = await systemSettingsService.getSystemSettings();
      if (settings.translationSettings) {
        // 优先使用数据库配置，如果为空则使用环境变量
        this.deeplApiKey = settings.translationSettings.deeplApiKey || process.env.DEEPL_API_KEY || '';
        this.googleApiKey = settings.translationSettings.googleApiKey || process.env.GOOGLE_TRANSLATE_API_KEY || '';
        this.preferredService = settings.translationSettings.preferredService || 'deepl';
        this.enableTranslation = settings.translationSettings.enableTranslation;
      }
    } catch (error) {
      console.error('加载翻译配置失败，使用环境变量配置:', error);
      // 如果加载失败，继续使用环境变量配置
    }
  }

  /**
   * 翻译文本
   * @param text 要翻译的文本
   * @param targetLang 目标语言 ('zh' | 'en')
   * @param sourceLang 源语言 ('zh' | 'en')
   * @returns 翻译后的文本
   */
  async translateText(text: string, targetLang: 'zh' | 'en', sourceLang: 'zh' | 'en'): Promise<string> {
    // 加载最新配置
    await this.loadConfig();

    if (!text || text.trim() === '') {
      return '';
    }

    if (sourceLang === targetLang) {
      return text;
    }

    // 检查翻译功能是否启用
    if (!this.enableTranslation) {
      throw new Error('翻译功能未启用');
    }

    try {
      if (this.preferredService === 'deepl' && this.deeplApiKey) {
        return await this.translateWithDeepL(text, targetLang, sourceLang);
      } else if (this.googleApiKey) {
        return await this.translateWithGoogle(text, targetLang, sourceLang);
      } else {
        throw new Error('没有配置可用的翻译服务API密钥');
      }
    } catch (error) {
      console.error('翻译失败:', error);
      // 如果首选服务失败，尝试备用服务
      try {
        if (this.preferredService === 'deepl' && this.googleApiKey) {
          return await this.translateWithGoogle(text, targetLang, sourceLang);
        } else if (this.preferredService === 'google' && this.deeplApiKey) {
          return await this.translateWithDeepL(text, targetLang, sourceLang);
        }
      } catch (fallbackError) {
        console.error('备用翻译服务也失败:', fallbackError);
      }
      throw new Error('翻译服务暂时不可用');
    }
  }

  /**
   * 使用 DeepL API 翻译
   */
  private async translateWithDeepL(text: string, targetLang: 'zh' | 'en', sourceLang: 'zh' | 'en'): Promise<string> {
    const deeplTargetLang = targetLang === 'zh' ? 'ZH' : 'EN';
    const deeplSourceLang = sourceLang === 'zh' ? 'ZH' : 'EN';

    const response = await axios.post(
      'https://api-free.deepl.com/v2/translate',
      new URLSearchParams({
        auth_key: this.deeplApiKey,
        text: text,
        source_lang: deeplSourceLang,
        target_lang: deeplTargetLang,
        preserve_formatting: '1'
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 30000
      }
    );

    if (response.data.translations && response.data.translations.length > 0) {
      return response.data.translations[0].text;
    } else {
      throw new Error('DeepL API 返回了无效的响应');
    }
  }

  /**
   * 使用 Google Translate API 翻译
   */
  private async translateWithGoogle(text: string, targetLang: 'zh' | 'en', sourceLang: 'zh' | 'en'): Promise<string> {
    const googleTargetLang = targetLang === 'zh' ? 'zh-CN' : 'en';
    const googleSourceLang = sourceLang === 'zh' ? 'zh-CN' : 'en';

    const response = await axios.post(
      `https://translation.googleapis.com/language/translate/v2?key=${this.googleApiKey}`,
      {
        q: text,
        source: googleSourceLang,
        target: googleTargetLang,
        format: 'text'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    if (response.data.data && response.data.data.translations && response.data.data.translations.length > 0) {
      return response.data.data.translations[0].translatedText;
    } else {
      throw new Error('Google Translate API 返回了无效的响应');
    }
  }

  /**
   * 翻译 Markdown 内容
   * 保持 Markdown 格式不变，只翻译文本内容
   */
  async translateMarkdown(markdown: string, targetLang: 'zh' | 'en', sourceLang: 'zh' | 'en'): Promise<string> {
    if (!markdown || markdown.trim() === '') {
      return '';
    }

    if (sourceLang === targetLang) {
      return markdown;
    }

    try {
      // 简单的 Markdown 处理：分段翻译，保持格式
      const lines = markdown.split('\n');
      const translatedLines: string[] = [];

      for (const line of lines) {
        const trimmedLine = line.trim();
        
        // 跳过空行、代码块、链接等不需要翻译的内容
        if (
          trimmedLine === '' ||
          trimmedLine.startsWith('```') ||
          trimmedLine.startsWith('![') ||
          trimmedLine.startsWith('[') && trimmedLine.includes('](') ||
          trimmedLine.startsWith('http') ||
          /^#+\s*$/.test(trimmedLine)
        ) {
          translatedLines.push(line);
          continue;
        }

        // 提取需要翻译的文本（去除 Markdown 标记）
        let textToTranslate = trimmedLine;
        let prefix = '';
        let suffix = '';

        // 处理标题
        const headerMatch = trimmedLine.match(/^(#+\s*)(.*)/);
        if (headerMatch) {
          prefix = headerMatch[1];
          textToTranslate = headerMatch[2];
        }

        // 处理列表
        const listMatch = trimmedLine.match(/^(\s*[-*+]\s*)(.*)/);
        if (listMatch) {
          prefix = listMatch[1];
          textToTranslate = listMatch[2];
        }

        // 处理引用
        const quoteMatch = trimmedLine.match(/^(>\s*)(.*)/);
        if (quoteMatch) {
          prefix = quoteMatch[1];
          textToTranslate = quoteMatch[2];
        }

        // 翻译文本内容
        if (textToTranslate.trim()) {
          const translatedText = await this.translateText(textToTranslate, targetLang, sourceLang);
          translatedLines.push(prefix + translatedText + suffix);
        } else {
          translatedLines.push(line);
        }

        // 添加延迟以避免API限制
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return translatedLines.join('\n');
    } catch (error) {
      console.error('Markdown 翻译失败:', error);
      throw error;
    }
  }

  /**
   * 检查翻译服务是否可用
   */
  async isAvailable(): Promise<boolean> {
    await this.loadConfig();
    return this.enableTranslation && !!(this.deeplApiKey || this.googleApiKey);
  }

  /**
   * 获取当前使用的翻译服务
   */
  getCurrentService(): string {
    if (this.preferredService === 'deepl' && this.deeplApiKey) {
      return 'DeepL';
    } else if (this.googleApiKey) {
      return 'Google Translate';
    }
    return 'None';
  }
}

export default new TranslationService();
