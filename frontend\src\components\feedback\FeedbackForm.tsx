import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../../components/ui/button';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { submitFeedback } from '../../api/feedback';
import { useToast } from '../ui/use-toast';

interface FeedbackFormProps {
  onSuccess?: () => void;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // 表单验证
  const isFormValid = () => {
    if (!title.trim()) {
      toast({
        title: t('feedback.titleRequired'),
        variant: 'destructive'
      });
      return false;
    }

    if (!content.trim()) {
      toast({
        title: t('feedback.contentRequired'),
        variant: 'destructive'
      });
      return false;
    }

    if (content.length > 1000) {
      toast({
        title: t('feedback.contentTooLong'),
        variant: 'destructive'
      });
      return false;
    }

    return true;
  };

  // 提交反馈
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isFormValid()) return;

    try {
      setIsSubmitting(true);
      await submitFeedback({ title, content });

      toast({
        title: t('feedback.submitSuccess'),
        description: t('feedback.submitSuccessDesc'),
        variant: 'default'
      });

      // 重置表单
      setTitle('');
      setContent('');

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('提交失败:', error);
      toast({
        title: t('feedback.submitFailed'),
        description: error instanceof Error ? error.message : t('feedback.submitFailedDesc'),
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="rounded-xl p-5 bg-cyber-dark/30 backdrop-blur-sm">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="space-y-2 text-left">
            <Label htmlFor="title" className="text-cyber-muted font-mono text-sm">{t('feedback.title')}</Label>
            <Input
              id="title"
              placeholder={t('feedback.titlePlaceholder')}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={100}
              required
              className="w-full h-12 px-4 rounded-xl bg-cyber-border/5 border border-cyber-cyan/20 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/60 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
          <div className="space-y-2 text-left">
            <Label htmlFor="content" className="text-cyber-muted font-mono text-sm">{t('feedback.description')}</Label>
            <Textarea
              id="content"
              placeholder={t('feedback.descriptionPlaceholder')}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={5}
              required
              className="w-full px-4 py-3 rounded-xl bg-cyber-border/5 border border-cyber-cyan/20 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/60 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 resize-none"
            />
            <div className="text-xs text-cyber-muted text-right font-mono">
              {content.length}/1000
            </div>
          </div>
        </div>
        <div className="flex justify-end mt-6">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-mono transition-all duration-200"
          >
            {isSubmitting ? t('feedback.submitting') : t('feedback.submitFeedback')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default FeedbackForm;
