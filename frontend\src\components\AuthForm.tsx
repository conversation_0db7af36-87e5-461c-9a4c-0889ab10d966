import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required?: boolean;
}

interface AuthFormProps {
  title: string;
  fields: FormField[];
  submitText: string;
  onSubmit: (formData: Record<string, string>) => void;
  isLoading: boolean;
  error: string | null;
  footerText?: React.ReactNode;
}

const AuthForm: React.FC<AuthFormProps> = ({
  title,
  fields,
  submitText,
  onSubmit,
  isLoading,
  error,
  footerText
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Record<string, string>>(
    fields.reduce((acc, field) => ({ ...acc, [field.name]: '' }), {})
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="max-w-md mx-auto bg-background-card p-8 rounded-lg shadow-card">
      <h2 className="text-2xl font-bold mb-6 text-center text-content-primary">
        {title}
      </h2>
      
      {error && (
        <div className="mb-4 p-3 bg-error/10 border border-error text-error rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {fields.map((field) => (
          <div key={field.name} className="mb-4">
            <label 
              htmlFor={field.name}
              className="block text-sm font-medium text-content-secondary mb-1"
            >
              {field.label}
            </label>
            <input
              type={field.type}
              id={field.name}
              name={field.name}
              value={formData[field.name]}
              onChange={handleChange}
              placeholder={field.placeholder}
              required={field.required !== false}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-background-input text-content-primary"
            />
          </div>
        ))}
        
        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-2 px-4 rounded-md text-primary-foreground font-medium ${
            isLoading
              ? 'bg-primary/70 cursor-not-allowed'
              : 'bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary'
          } transition-colors duration-200`}
        >
          {isLoading ? t('common.processing') : submitText}
        </button>
      </form>
      
      {footerText && (
        <div className="mt-4 text-center text-sm text-content-muted">
          {footerText}
        </div>
      )}
    </div>
  );
};

export default AuthForm; 