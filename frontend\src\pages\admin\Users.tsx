import React, { useEffect, useState } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import UserTable from '../../components/admin/UserTable';
import useAdminStore from '../../store/useAdminStore';
import CreateUserModal from '../../components/admin/CreateUserModal';
import { Button, Input, Select } from '../../components/admin/ui';

const AdminUsers: React.FC = () => {
  const { fetchUsers } = useAdminStore();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [filters, setFilters] = useState({
    role: '',
    isOnline: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });

  // 创建用户模态框
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // 获取用户列表
  const fetchUserList = async () => {
    try {
      setIsLoading(true);
      setError('');

      // 准备筛选条件
      const filterParams: any = {};
      if (filters.role) filterParams.role = filters.role;
      if (filters.isOnline) filterParams.isOnline = filters.isOnline === 'true';

      const response = await fetchUsers(pagination.page, pagination.limit, search, filterParams);
      setUsers(response.users);
      setPagination(response.pagination);
      setIsLoading(false);
    } catch (err) {
      const message = err instanceof Error ? err.message : '获取用户列表失败';
      setError(message);
      setIsLoading(false);
    }
  };

  // 初次加载和筛选条件变化时获取数据
  useEffect(() => {
    fetchUserList();
  }, [pagination.page, filters]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
    fetchUserList();
  };

  // 处理筛选
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            variant="primary"
            className="bg-green-600 hover:bg-green-700"
          >
            创建用户
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 rounded">
            <p>{error}</p>
          </div>
        )}

        <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6 mb-6">
          <div className="md:flex md:justify-between mb-4">
            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="mb-4 md:mb-0">
              <div className="flex">
                <Input
                  type="text"
                  placeholder="搜索邮箱..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="rounded-r-none"
                />
                <Button
                  type="submit"
                  variant="primary"
                  className="rounded-l-none"
                >
                  搜索
                </Button>
              </div>
            </form>

            {/* 筛选器 */}
            <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
              <Select
                name="role"
                value={filters.role}
                onChange={handleFilterChange}
                options={[
                  { value: '', label: '所有角色' },
                  { value: 'trial', label: '试用用户' },
                  { value: 'normal', label: '普通用户' },
                  { value: 'subscriber', label: '订阅用户' },
                  { value: 'admin', label: '管理员' },
                  { value: 'banned', label: '已封禁' }
                ]}
              />

              <Select
                name="isOnline"
                value={filters.isOnline}
                onChange={handleFilterChange}
                options={[
                  { value: '', label: '所有状态' },
                  { value: 'true', label: '在线' },
                  { value: 'false', label: '离线' }
                ]}
              />
            </div>
          </div>

          {/* 用户表格 */}
          <UserTable users={users} isLoading={isLoading} onRefresh={fetchUserList} />

          {/* 分页控制 */}
          {!isLoading && pagination.pages > 1 && (
            <div className="flex justify-center mt-6">
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-border bg-white dark:bg-background-input dark:border-border text-sm font-medium text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border border-border dark:border-border text-sm font-medium ${
                      page === pagination.page
                        ? 'z-10 bg-blue-50 border-primary text-blue-600 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-white dark:bg-background-input text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-border bg-white dark:bg-background-input dark:border-border text-sm font-medium text-content-muted dark:text-content-secondary hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>

      {/* 创建用户模态框 */}
      <CreateUserModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={fetchUserList}
      />
    </AdminLayout>
  );
};

export default AdminUsers;