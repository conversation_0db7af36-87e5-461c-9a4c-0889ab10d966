import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useUserStore from '../store/useUserStore';
import InviteCodesList from '../components/InviteCodesList';
import { User, Lock, Gift, Bell, CreditCard, MessageSquare, Eye, EyeOff } from 'lucide-react';
import { getSubscriptionInfo } from '../api/subscription';

import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { cn } from '../lib/utils';
import SubscriptionInfo from '../components/SubscriptionInfo';
import UserFeedback from '../components/feedback/UserFeedback';
import NotificationList from '../components/NotificationList';
import notificationService from '../services/notificationService';
import { formatDate } from '../utils/dateFormatter';

// 用户中心页面
const UserCenter: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user, error, changePassword, fetchUserInfo, logout } = useUserStore();
  const [activeTab, setActiveTab] = useState('account');
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordChangeCountdown, setPasswordChangeCountdown] = useState(5);
  const [unreadCount, setUnreadCount] = useState(0);

  // 密码显示状态管理
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // 清空密码表单的函数
  const clearPasswordForm = () => {
    setOldPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setPasswordError(null);
    setPasswordSuccess(null);
    // 重置密码显示状态
    setShowOldPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  // 翻译奖励原因
  const translateRewardReason = (reason: string) => {
    return t(`user.rewardReasons.${reason}`, { defaultValue: reason });
  };

  // 加载未读通知数量
  const loadUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('加载未读通知数量失败:', error);
      setUnreadCount(0);
    }
  };

  // 检查并更新用户状态
  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user) return;

      try {
        // 获取最新的订阅信息来检查状态
        const response = await getSubscriptionInfo();
        const currentStatus = response.subscription.status;

        let expectedRole: string;
        if (currentStatus === 'active') {
          expectedRole = 'subscriber';
        } else if (currentStatus === 'trial') {
          expectedRole = 'trial';
        } else {
          expectedRole = 'normal';
        }

        // 如果用户角色与实际状态不符，重新获取用户信息
        if (user.role !== expectedRole) {
          console.log(`[UserCenter] 检测到用户状态变化: ${user.role} -> ${expectedRole}，更新用户信息`);
          await fetchUserInfo();
        }
      } catch (error) {
        console.error('[UserCenter] 检查用户状态失败:', error);
      }
    };

    checkUserStatus();
  }, [user, fetchUserInfo]);

  // 组件挂载时加载未读通知数量
  useEffect(() => {
    loadUnreadCount();
    // 每30秒刷新一次未读数量
    const interval = setInterval(loadUnreadCount, 30000);
    return () => clearInterval(interval);
  }, []);

  // 监听activeTab变化，当离开密码页面时清空密码表单
  useEffect(() => {
    // 当从密码页面切换到其他页面时，清空密码表单
    // 这样可以确保安全性，避免密码在内存中停留太久
    if (activeTab !== 'password') {
      clearPasswordForm();
    }
  }, [activeTab]);



  // 处理密码更改
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);

    if (newPassword !== confirmPassword) {
      setPasswordError(t('user.passwordMismatch'));
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError(t('user.passwordTooShort'));
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await changePassword(oldPassword, newPassword);

      // 如果需要重新认证，显示成功消息并跳转到登录页
      if (response && response.requireReauth) {
        setPasswordSuccess(t('user.passwordChangeSuccess'));
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');

        // 启动5秒倒计时
        let timeLeft = 5;
        setPasswordChangeCountdown(timeLeft);

        const timer = setInterval(() => {
          timeLeft -= 1;
          setPasswordChangeCountdown(timeLeft);

          if (timeLeft <= 0) {
            clearInterval(timer);
            // 倒计时结束，立即清除认证状态
            // logout() 会触发 unauthorized 事件，全局监听器会处理跳转
            logout();
          }
        }, 1000);
      } else {
        setPasswordSuccess(t('user.passwordUpdated'));
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (err) {
      let message = t('user.passwordUpdateFailed');

      if (err instanceof Error) {
        // 根据后端错误消息映射到对应的翻译键
        if (err.message.includes('当前密码不正确') || err.message.includes('Current password is incorrect')) {
          message = t('errors.currentPasswordIncorrect');
        } else if (err.message.includes('密码长度') || err.message.includes('password must be')) {
          message = t('user.passwordTooShort');
        } else {
          // 其他错误使用原始消息或默认消息
          message = err.message;
        }
      }

      setPasswordError(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 用户角色显示
  const renderRoleBadge = () => {
    if (!user) return null;

    let badgeClass = 'inline-flex items-center rounded-full px-3 py-1 text-xs font-medium font-mono border backdrop-blur-sm';
    let roleName = '';

    switch (user.role) {
      case 'subscriber':
        badgeClass += ' bg-cyber-green/20 text-cyber-green border-cyber-green/30';
        roleName = t('user.subscriberUser');
        break;
      case 'normal':
        badgeClass += ' bg-cyber-cyan/20 text-cyber-cyan border-cyber-cyan/30';
        roleName = t('user.normalUser');
        break;
      case 'trial':
        badgeClass += ' bg-cyber-purple/20 text-cyber-purple border-cyber-purple/30';
        roleName = t('user.trialUser');
        break;
      default:
        badgeClass += ' bg-cyber-muted/20 text-cyber-muted border-cyber-muted/30';
        roleName = t('user.unknownRole');
    }

    return <span className={badgeClass}>{roleName}</span>;
  };

  // 定义菜单项
  const menuItems = [
    { id: 'account', label: t('user.accountInfo'), icon: <User className="w-4 h-4 mr-2" />, badge: null },
    { id: 'password', label: t('user.changePassword'), icon: <Lock className="w-4 h-4 mr-2" />, badge: null },
    { id: 'invites', label: t('user.inviteManagement'), icon: <Gift className="w-4 h-4 mr-2" />, badge: null },
    { id: 'subscription', label: t('user.mySubscription'), icon: <CreditCard className="w-4 h-4 mr-2" />, badge: null },
    { id: 'feedback', label: t('user.feedback'), icon: <MessageSquare className="w-4 h-4 mr-2" />, badge: null },
    {
      id: 'notifications',
      label: t('user.notifications'),
      icon: <Bell className="w-4 h-4 mr-2" />,
      badge: unreadCount > 0 ? unreadCount : null
    },
  ];

  return (
    <div className="w-full min-h-[calc(100vh-4rem)] bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card relative">
      <div className="container max-w-6xl mx-auto px-4 py-8 relative z-10">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-[240px_1fr]">
          {/* 左侧菜单栏 */}
          <div className="md:col-span-1">
            <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

              <CardContent className="p-0 relative z-10">
                <div className="flex flex-col items-center p-6 border-b border-cyber-border/30">
                  <div className="h-16 w-16 rounded-full bg-cyber-cyan/10 border border-cyber-cyan/30 flex items-center justify-center text-xl font-semibold text-cyber-cyan mb-3 font-mono">
                  {user?.email.charAt(0).toUpperCase() || 'U'}
                </div>
                  <p className="text-sm text-center font-medium truncate max-w-full text-cyber-text font-mono">{user?.email}</p>
                <div className="mt-2">{renderRoleBadge()}</div>
              </div>

                <nav className="p-2">
                  {menuItems.map(item => (
                    <Button
                      key={item.id}
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-full justify-start mb-1 h-10 relative font-mono text-sm transition-all duration-300",
                        activeTab === item.id
                          ? "bg-cyber-cyan/15 text-cyber-cyan border border-cyber-cyan/30 shadow-[0_0_15px_rgba(0,245,255,0.2)]"
                          : "text-cyber-muted hover:bg-cyber-border/10 hover:text-cyber-text border border-transparent hover:border-cyber-border/30"
                      )}
                      onClick={() => {
                        setActiveTab(item.id);
                        // 如果点击的是通知菜单，刷新未读数量
                        if (item.id === 'notifications') {
                          loadUnreadCount();
                        }
                        // 切换tab时重置滚动位置到顶部，避免内容高度变化导致的抖动
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                      }}
                    >
                      {item.icon}
                      <span className="flex-1 text-left">{item.label}</span>
                      {/* 显示未读通知数量 */}
                      {item.badge && (
                        <span className="ml-auto bg-cyber-pink text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 font-mono">
                          {item.badge > 99 ? '99+' : item.badge}
                        </span>
                      )}
                    </Button>
                  ))}
              </nav>
              </CardContent>
            </Card>
            </div>

          {/* 右侧内容区 */}
          <div className="md:col-span-1 min-h-[calc(100vh-12rem)]">
              {/* 账户信息 */}
              {activeTab === 'account' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.accountInfo')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div className="space-y-1">
                      <Label className="text-cyber-muted font-mono text-sm">{t('user.email')}</Label>
                      <p className="font-medium text-cyber-text font-mono">{user?.email || 'N/A'}</p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-cyber-muted font-mono text-sm">{t('user.accountType')}</Label>
                      <p className="font-medium text-cyber-text font-mono">
                          {user?.role === 'subscriber' ? t('user.subscriberUser') :
                           user?.role === 'normal' ? t('user.normalUser') : t('user.trialUser')}
                        </p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-cyber-muted font-mono text-sm">{t('user.createdAt')}</Label>
                      <p className="font-medium text-cyber-text font-mono">{formatDate(user?.createdAt || '', i18n.language)}</p>
                      </div>
                    <div className="space-y-1">
                      <Label className="text-cyber-muted font-mono text-sm">{t('user.emailVerificationStatus')}</Label>
                      <p className="font-medium font-mono">
                          {user?.isVerified ?
                            <span className="text-cyber-green">{t('user.verified')}</span> :
                            <span className="text-cyber-pink">{t('user.unverified')}</span>}
                        </p>
                      </div>
                    </div>

                    {user?.role === 'trial' && (
                    <div className="mt-6 p-4 bg-cyber-purple/10 rounded-xl border border-cyber-purple/30 backdrop-blur-sm">
                        <h4 className="text-cyber-purple font-semibold mb-2 font-mono">{t('user.trialInfo')}</h4>
                      <p className="mb-2 text-sm text-cyber-muted font-mono">{t('user.trialDesc')}</p>
                      <p className="flex justify-between text-sm font-mono">
                          <span className="text-cyber-muted">{t('user.trialEndsAt')}</span>
                        <span className="font-semibold text-cyber-purple">{formatDate(user?.trialEndsAt || '', i18n.language)}</span>
                        </p>
                      </div>
                    )}

                    {user?.role === 'subscriber' && (
                    <div className="mt-6 p-4 bg-cyber-green/10 rounded-xl border border-cyber-green/30 backdrop-blur-sm">
                        <h4 className="text-cyber-green font-semibold mb-2 font-mono">{t('user.subscriptionInfo')}</h4>
                      <p className="text-sm text-cyber-muted font-mono">{t('user.subscriptionDesc')}</p>
                      </div>
                    )}

                    {user?.rewardHistory && user.rewardHistory.length > 0 && (
                      <div className="mt-6 p-4 bg-cyber-cyan/10 rounded-xl border border-cyber-cyan/30 backdrop-blur-sm">
                        <h4 className="text-cyber-cyan font-semibold mb-2 font-mono">{t('user.rewardHistory')}</h4>
                        <div className="space-y-3">
                          {user.rewardHistory.map((reward: {
                            type: string;
                            days: number;
                            grantedAt: string;
                            reason: string;
                          }, index: number) => (
                            <div key={index} className="flex justify-between text-sm border-b border-cyber-border/30 pb-2 last:border-0 last:pb-0">
                              <div>
                                <p className="font-medium text-cyber-text font-mono">{translateRewardReason(reward.reason)}</p>
                                <p className="text-cyber-muted text-xs font-mono">{formatDate(reward.grantedAt, i18n.language)}</p>
                              </div>
                              <span className="font-semibold text-cyber-green font-mono">+{reward.days}{t('user.days')}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>
              )}

              {/* 修改密码 */}
              {activeTab === 'password' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.changePassword')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <form onSubmit={handlePasswordChange}>
                    {passwordError && (
                      <div className="mb-6 p-3 bg-cyber-pink/10 border border-cyber-pink/30 text-cyber-pink rounded-xl text-sm font-mono backdrop-blur-sm">
                        {passwordError}
                      </div>
                    )}

                    {passwordSuccess && (
                      <div className="mb-6 p-3 bg-cyber-green/10 border border-cyber-green/30 text-cyber-green rounded-xl text-sm font-mono backdrop-blur-sm">
                        <div>{passwordSuccess}</div>
                        {passwordSuccess === t('user.passwordChangeSuccess') && (
                          <div className="mt-2 text-xs text-cyber-muted">
                            {t('auth.autoRedirectIn5s').replace('5', passwordChangeCountdown.toString())}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="space-y-4">
                      <div className="space-y-2 text-left">
                        <Label htmlFor="old-password" className="text-cyber-muted font-mono text-sm">{t('auth.currentPassword')}</Label>
                        <div className="relative">
                          <Input
                            id="old-password"
                            type={showOldPassword ? "text" : "password"}
                            value={oldPassword}
                            onChange={(e) => setOldPassword(e.target.value)}
                            placeholder={t('auth.currentPasswordPlaceholder')}
                            className="w-full h-12 px-4 pr-12 rounded-xl bg-cyber-border/5 border border-cyber-cyan/30 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/80 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowOldPassword(!showOldPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-cyber-cyan transition-colors duration-200"
                          >
                            {showOldPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2 text-left">
                        <Label htmlFor="new-password" className="text-cyber-muted font-mono text-sm">{t('auth.newPassword')}</Label>
                        <div className="relative">
                          <Input
                            id="new-password"
                            type={showNewPassword ? "text" : "password"}
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            placeholder={t('auth.newPasswordPlaceholder')}
                            className="w-full h-12 px-4 pr-12 rounded-xl bg-cyber-border/5 border border-cyber-cyan/30 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/80 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                            minLength={6}
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-cyber-cyan transition-colors duration-200"
                          >
                            {showNewPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2 text-left">
                        <Label htmlFor="confirm-password" className="text-cyber-muted font-mono text-sm">{t('auth.confirmPassword')}</Label>
                        <div className="relative">
                          <Input
                            id="confirm-password"
                            type={showConfirmPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            placeholder={t('auth.confirmPasswordPlaceholder')}
                            className="w-full h-12 px-4 pr-12 rounded-xl bg-cyber-border/5 border border-cyber-cyan/30 text-cyber-text placeholder:text-cyber-muted focus:border-cyber-cyan/80 transition-all duration-300 text-base font-mono outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                            minLength={6}
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-cyber-cyan transition-colors duration-200"
                          >
                            {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full mt-6 h-12 bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-bold text-base rounded-xl shadow-lg hover:shadow-[0_0_30px_rgba(0,245,255,0.4)] transition-all duration-300 active:scale-[0.98] font-mono tracking-wider"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? t('user.updating') : t('user.updatePassword')}
                    </Button>
                  </form>
                </CardContent>
              </Card>
              )}

              {/* 邀请码管理 */}
              {activeTab === 'invites' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.inviteManagement')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="mb-6 p-4 bg-cyber-cyan/10 rounded-xl border border-cyber-cyan/30 text-sm backdrop-blur-sm">
                      <h4 className="text-cyber-cyan font-semibold mb-2 font-mono">{t('user.howToInvite')}</h4>
                    <ul className="list-disc list-inside text-cyber-muted space-y-1.5 font-mono">
                        <li>{t('user.inviteStep1')}</li>
                        <li>{t('user.inviteStep2')}</li>
                        <li>{t('user.inviteStep3')}</li>
                        <li>{t('user.inviteReward')}</li>
                      </ul>
                    </div>

                  <div className="mt-4">
                      <InviteCodesList />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 我的订阅 */}
            {activeTab === 'subscription' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.mySubscription')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <SubscriptionInfo />
                </CardContent>
              </Card>
            )}

            {/* 问题反馈 */}
            {activeTab === 'feedback' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.feedback')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <UserFeedback />
                </CardContent>
              </Card>
            )}

            {/* 系统通知 */}
            {activeTab === 'notifications' && (
              <Card className="bg-cyber-card/40 backdrop-blur-xl border border-cyber-border/50 rounded-2xl shadow-2xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

                <CardHeader className="relative z-10">
                  <CardTitle className="text-cyber-text font-sans text-xl">{t('user.notifications')}</CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <NotificationList />
                </CardContent>
              </Card>
              )}
            </div>
          </div>
        </div>
    </div>
  );
};

export default UserCenter;