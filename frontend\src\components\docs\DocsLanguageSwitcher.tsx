import React, { useState, useRef, useEffect } from 'react'
import { ChevronDown, Globe } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface DocsLanguageSwitcherProps {
  className?: string
  variant?: 'default' | 'compact'
}

const DocsLanguageSwitcher: React.FC<DocsLanguageSwitcherProps> = ({
  className = '',
  variant = 'default'
}) => {
  const { i18n } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const languages = [
    { code: 'zh', name: '中文', nativeName: '中文' },
    { code: 'en', name: 'English', nativeName: 'English' }
  ]

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode)
    setIsOpen(false)
  }

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 按ESC键关闭下拉菜单
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [])

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className={`
          flex items-center gap-2 px-3 py-2 h-10 rounded-md min-w-[100px] justify-between
          text-gray-700 hover:text-gray-900
          border border-gray-200 hover:border-gray-300
          bg-white hover:bg-gray-50
          transition-all duration-300 text-sm
          outline-none focus:outline-none focus-visible:outline-none focus:ring-0 focus:ring-offset-0 focus:border-gray-300
          ${variant === 'compact' ? 'px-2 py-1 h-8 min-w-[80px]' : ''}
        `}
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <div className="flex items-center gap-2">
          <Globe className="h-4 w-4 text-gray-500" />
          {variant === 'default' && (
            <span className="font-medium text-gray-700">
              {currentLanguage.nativeName}
            </span>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 flex-shrink-0 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          className="
            absolute right-0 top-full mt-1 w-32
            bg-white border border-gray-200 rounded-md shadow-lg z-50
            py-1
          "
          role="listbox"
        >
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className="
                w-full text-left px-3 py-2 text-sm
                text-gray-700 hover:text-gray-900 hover:bg-gray-100
                transition-colors duration-200
                outline-none focus:outline-none focus:bg-gray-100 focus:text-gray-900
              "
              role="option"
              aria-selected={language.code === currentLanguage.code}
            >
              {language.nativeName}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

export default DocsLanguageSwitcher
