import React, { useState } from 'react';
import useAdminStore from '../../store/useAdminStore';
import { useToast } from '../ui/use-toast';
import { <PERSON><PERSON>, <PERSON>dal<PERSON>eader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, Button, Input, Select, Label } from './ui';

interface CreateUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateUserModal: React.FC<CreateUserModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { createUser } = useAdminStore();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'normal',
    isVerified: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      setError('邮箱和密码是必填项');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      await createUser(formData);
      toast({
        title: '用户创建成功',
        variant: 'default'
      });
      setIsLoading(false);
      onSuccess();
      onClose();
      // 重置表单
      setFormData({
        email: '',
        password: '',
        role: 'normal',
        isVerified: true
      });
    } catch (err) {
      const message = err instanceof Error ? err.message : '创建用户失败';
      setError(message);
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const roleOptions = [
    { value: 'trial', label: '试用用户' },
    { value: 'normal', label: '普通用户' },
    { value: 'subscriber', label: '订阅用户' },
    { value: 'admin', label: '管理员' }
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="创建新用户" size="md">
      <ModalBody>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-4 rounded">
            <p>{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              label="邮箱"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />

            <Input
              label="密码"
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
            />

            <Select
              label="角色"
              name="role"
              value={formData.role}
              onChange={handleChange}
              options={roleOptions}
            />

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isVerified"
                name="isVerified"
                checked={formData.isVerified}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <Label htmlFor="isVerified" className="ml-2">
                已验证邮箱
              </Label>
            </div>
          </div>
        </form>
      </ModalBody>

      <ModalFooter>
        <Button
          variant="outline"
          onClick={onClose}
        >
          取消
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? '创建中...' : '创建用户'}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default CreateUserModal; 