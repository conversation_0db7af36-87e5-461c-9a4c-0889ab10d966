import mongoose, { Document, Schema } from 'mongoose';

/**
 * 30分钟K线数据接口
 * 定义30分钟K线数据文档的结构和类型
 */
export interface IThirtyMinKline extends Document {
  symbol: string;      // 交易对符号（例如：BTCUSDT）
  openTime: number;    // 开盘时间戳（毫秒）
  closeTime: number;   // 收盘时间戳（毫秒）
  open: string;        // 开盘价
  high: string;        // 最高价
  low: string;         // 最低价
  close: string;       // 收盘价
  volume: string;      // 交易量
  quoteVolume: string; // 交易额
  trades: number;      // 交易笔数
  createdAt: Date;     // 记录创建时间
  updatedAt: Date;     // 记录更新时间
}

/**
 * 30分钟K线数据模式
 * 定义MongoDB中30分钟K线数据集合的结构
 */
const ThirtyMinKlineSchema: Schema = new Schema({
  symbol: { type: String, required: true, index: true },      // 交易对符号
  openTime: { type: Number, required: true, index: true },    // 开盘时间戳
  closeTime: { type: Number, required: true },                // 收盘时间戳
  open: { type: String, required: true },                     // 开盘价
  high: { type: String, required: true },                     // 最高价
  low: { type: String, required: true },                      // 最低价
  close: { type: String, required: true },                    // 收盘价
  volume: { type: String, required: true },                   // 交易量
  quoteVolume: { type: String, required: true },              // 交易额
  trades: { type: Number, required: true },                   // 交易笔数
}, {
  timestamps: true,    // 自动添加createdAt和updatedAt字段
  versionKey: false    // 不使用版本字段（__v）
});

// 创建复合索引，确保每个交易对在每个时间点只有一条记录
ThirtyMinKlineSchema.index({ symbol: 1, openTime: 1 }, { unique: true });

// 导出30分钟K线数据模型
export default mongoose.model<IThirtyMinKline>('ThirtyMinKline', ThirtyMinKlineSchema); 