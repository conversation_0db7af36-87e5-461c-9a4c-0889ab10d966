import { Router } from 'express';
import notificationController from '../controllers/notificationController';
import authenticate from '../middlewares/authMiddleware';

const router = Router();

// 所有通知路由都需要认证
router.use(authenticate);

/**
 * 获取用户通知列表
 * GET /api/notifications
 * Query参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 20)
 * - unreadOnly: 是否只获取未读通知 (默认: false)
 */
router.get('/', notificationController.getUserNotifications);

/**
 * 获取用户未读通知数量
 * GET /api/notifications/unread-count
 */
router.get('/unread-count', notificationController.getUnreadCount);

/**
 * 标记通知为已读
 * PATCH /api/notifications/:id/read
 */
router.patch('/:id/read', notificationController.markAsRead);

/**
 * 标记所有通知为已读
 * PATCH /api/notifications/mark-all-read
 */
router.patch('/mark-all-read', notificationController.markAllAsRead);

/**
 * 删除通知
 * DELETE /api/notifications/:id
 */
router.delete('/:id', notificationController.deleteNotification);

export default router;
