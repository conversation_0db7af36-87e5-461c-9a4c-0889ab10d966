import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import useAdminStore from '../store/useAdminStore';

const AdminRoute: React.FC = () => {
  const { isAuthenticated, token, isInitializing, user, initialize } = useAdminStore();
  const location = useLocation();
  const [initAttempted, setInitAttempted] = useState(false);

  useEffect(() => {
    // 避免重复初始化
    if (!initAttempted) {
      // 检查是否有正在进行的重定向
      const isRedirecting = sessionStorage.getItem('admin_redirecting') === 'true';
      
      if (!isRedirecting) {
        console.log('AdminRoute: 初始化管理员认证');
        initialize();
      } else {
        console.log('AdminRoute: 检测到重定向状态，跳过初始化');
        sessionStorage.removeItem('admin_redirecting');
      }
      
      setInitAttempted(true);
    }
  }, [initialize, initAttempted]);

  // 如果正在初始化，显示加载状态
  if (isInitializing && !initAttempted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 如果用户没有认证或不是管理员，重定向到登录页
  if (!isAuthenticated || !token || user?.role !== 'admin') {
    return <Navigate to="/admin/login" state={{ from: location.pathname }} replace />;
  }

  // 已认证且是管理员，渲染子路由
  return <Outlet />;
};

export default AdminRoute; 