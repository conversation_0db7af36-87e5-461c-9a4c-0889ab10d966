const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// 修改为你的MongoDB连接字符串
const MONGO_URI = 'mongodb://localhost:27017/btc-prediction';

// 引入User模型
const User = require('../dist/models/User').default;

async function createAdmin() {
  const email = '<EMAIL>';
  const password = '123456';
  const hash = await bcrypt.hash(password, 10);

  await mongoose.connect(MONGO_URI);

  const exists = await User.findOne({ email });
  if (exists) {
    console.log('管理员已存在');
    process.exit(0);
  }

  await User.create({
    email,
    username: 'admin_23456789',
    password: password,
    role: 'admin',
    trialEndsAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    isVerified: true,
    registeredIp: '127.0.0.1',
    createdAt: new Date(),
    lastLoginAt: null,
    isOnline: false
  });

  console.log('管理员创建成功');
  process.exit(0);
}

createAdmin().catch(err => {
  console.error('创建管理员失败:', err);
  process.exit(1);
}); 