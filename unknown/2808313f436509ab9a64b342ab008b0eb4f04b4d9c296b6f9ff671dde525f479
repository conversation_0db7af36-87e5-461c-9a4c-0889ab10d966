import { Request, Response } from 'express';
import InviteCode from '../models/InviteCode';
import mongoose from 'mongoose';

/**
 * 获取当前用户的邀请码列表
 */
export const getMyInviteCodes = async (req: Request, res: Response) => {
  try {
    // 获取当前登录用户的ID
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      });
    }

    // 查询用户创建的所有邀请码
    const inviteCodes = await InviteCode.find({ createdBy: userId })
      .populate('usedBy', 'email username') // 获取使用者的邮箱和用户名
      .sort({ createdAt: -1 }); // 按创建时间倒序排列

    return res.status(200).json({
      success: true,
      data: inviteCodes
    });
  } catch (error) {
    console.error('获取邀请码列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取邀请码详细信息
 */
export const getInviteCodeDetails = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    
    // 查询邀请码信息
    const inviteCode = await InviteCode.findOne({ code })
      .populate('createdBy', 'email username')
      .populate('usedBy', 'email username');
    
    if (!inviteCode) {
      return res.status(404).json({
        success: false,
        message: '邀请码不存在'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: inviteCode
    });
  } catch (error) {
    console.error('获取邀请码详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
}; 