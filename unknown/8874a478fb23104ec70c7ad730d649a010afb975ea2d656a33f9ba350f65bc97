import mongoose, { Document, Schema } from 'mongoose';

/**
 * 预测折线数据点接口
 */
interface IPredictionPoint {
  time: number;   // 预测时间点（毫秒时间戳）
  value: number;  // 预测价格值
}

/**
 * 节气预测折线接口
 * 定义预测折线文档的结构和类型
 */
export interface ISolarTermPredictionLine extends Document {
  symbol: string;           // 交易对符号（例如：BTCUSDT）
  predictionTime: number;   // 预测生成的时间戳（毫秒）
  targetStartTime: number;  // 预测目标时间段的开始时间戳（毫秒）
  targetEndTime: number;    // 预测目标时间段的结束时间戳（毫秒）
  solarTermDate: string;    // 节气日期（YYYY-MM-DD格式）
  predictionCycle: string;  // 预测周期类型（'0_day'|'3_day'|'6_day'|...）
  predictionData: IPredictionPoint[]; // 折线预测数据点列表
  isActive: boolean;        // 是否为最新的预测
  createdAt: Date;          // 记录创建时间
}

// 删除之前定义的模型（如果存在）以确保索引被正确重置
try {
  if (mongoose.models.SolarTermPredictionLine) {
    delete mongoose.models.SolarTermPredictionLine;
  }
} catch (error) {
  console.error('删除旧模型时出错:', error);
}

/**
 * 预测折线数据模式
 * 定义MongoDB中预测折线数据集合的结构
 */
const SolarTermPredictionLineSchema: Schema = new Schema({
  symbol: { type: String, required: true, index: true },           // 交易对符号
  predictionTime: { type: Number, required: true },                // 预测生成时间
  targetStartTime: { type: Number, required: true, index: true },  // 目标开始时间
  targetEndTime: { type: Number, required: true },                 // 目标结束时间
  solarTermDate: { type: String, required: true },                 // 节气日期
  predictionCycle: { type: String, required: true },               // 预测周期类型
  predictionData: [{ 
    time: { type: Number, required: true },                        // 预测时间点
    value: { type: Number, required: true }                        // 预测价格值
  }],
  isActive: { type: Boolean, default: true },                      // 是否活跃
  createdAt: { type: Date, default: Date.now }                     // 创建时间
}, {
  timestamps: true,    // 自动添加createdAt和updatedAt字段
  versionKey: false    // 不使用版本字段（__v）
});

// 清除之前的索引并重新定义新索引
SolarTermPredictionLineSchema.index({ symbol: 1, targetStartTime: 1 }, { unique: true });

// 导出预测折线数据模型
export default mongoose.models.SolarTermPredictionLine || 
  mongoose.model<ISolarTermPredictionLine>('SolarTermPredictionLine', SolarTermPredictionLineSchema); 