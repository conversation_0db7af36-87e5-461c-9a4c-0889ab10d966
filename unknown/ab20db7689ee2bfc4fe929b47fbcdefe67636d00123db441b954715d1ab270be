import { Router } from 'express';
import subscriptionController from '../controllers/subscriptionController';
import authenticate from '../middlewares/authMiddleware';

const router = Router();

// 需要认证的路由
router.post('/create', authenticate, subscriptionController.createSubscription);
router.get('/info', authenticate, subscriptionController.getUserSubscription);
router.get('/payments', authenticate, subscriptionController.getPaymentHistory);

// 支付回调 (不需要认证，但需要验证来自NOWPayments)
router.post('/webhook', subscriptionController.handlePaymentWebhook);

// 添加重定向路由 - 处理直接访问后端的支付成功/取消URL
router.get('/success', subscriptionController.handlePaymentRedirect);
router.get('/cancel', subscriptionController.handlePaymentRedirect);

export default router; 