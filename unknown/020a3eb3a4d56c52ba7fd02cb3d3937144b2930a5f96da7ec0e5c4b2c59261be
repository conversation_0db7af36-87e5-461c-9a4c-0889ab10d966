# BTC 预测网站

这是一个BTC 30分钟K线预测网站，能够在特定时间点根据1分钟K线形态预测未来30分钟的价格走势。

## 项目结构

项目采用前后端分离的架构：

```
BT/
├── frontend/            # React前端
│   ├── src/
│   │   ├── components/  # 可复用组件
│   │   ├── pages/       # 页面组件
│   │   ├── services/    # API服务
│   │   ├── hooks/       # 自定义Hooks
│   │   ├── utils/       # 工具函数
│   │   ├── i18n/        # 国际化配置
│   │   └── types/       # TypeScript类型定义
│   └── ...
├── backend/             # Node.js后端
│   ├── src/
│   │   ├── controllers/ # 控制器
│   │   ├── models/      # 数据模型
│   │   ├── routes/      # 路由
│   │   ├── services/    # 业务逻辑服务
│   │   ├── config/      # 配置
│   │   └── utils/       # 工具函数
│   └── ...
└── ...
```

## 核心功能

- 实时BTC 30分钟K线图表显示
- 在图表下方20%区域显示预测K线
- 在特定时间点自动生成预测
  - 奇数小时：HH:02, HH:28, HH:58
  - 偶数小时：HH:28

## 预测逻辑

系统在指定时间点通过获取特定1分钟K线的形态，按比例复制生成未来30分钟周期的预测K线：

- 奇数小时HH:02分：使用HH:00的K线预测HH:00-HH:30
- 奇数小时HH:28分：使用HH:01的K线预测HH:30-HH+1:00
- 奇数小时HH:58分：使用HH:02的K线预测HH+1:00-HH+1:30
- 偶数小时HH:28分：使用HH-1:03的K线预测HH:30-HH+1:00

## 技术栈

### 前端
- React + TypeScript
- Tailwind CSS
- TradingView Lightweight Charts
- i18next（国际化）
- Axios（API请求）

### 后端
- Node.js (Express)
- TypeScript
- MongoDB
- Mongoose
- node-cron（定时任务）

## 环境要求

- Node.js 16+
- MongoDB 4.4+

## 安装与运行

### 后端

```bash
cd backend
npm install
# 配置.env文件
npm run build
npm start
# 开发模式
# npm run dev
```
npm run build

### 前端

```bash
cd frontend
npm install
# 配置环境变量
npm start
```

## 可扩展功能

项目设计为模块化，可以无痛扩展以下功能：

- 用户认证模块（JWT + 权限控制）
- 订阅计划模块（Stripe / 支付宝）
- 通知系统（如订阅到期提醒）
- 历史预测查询 + 准确率分析
- 多语言支持

## 接口文档

### 后端API

- GET /api/klines - 获取K线数据
- POST /api/klines/refresh - 刷新K线数据
- GET /api/predictions - 获取预测数据
- POST /api/predictions/trigger - 手动触发预测

## 新增功能：自动检查补齐缺失的预测K线

系统现在支持自动检查并补齐缺失的预测K线数据：

- 每天凌晨2点系统会自动检查最近3天的预测K线数据完整性
- 后端服务器启动时也会自动检查并补齐最近3天的缺失数据
- 如果发现缺失的预测K线数据，会按照预测规则自动生成并补齐
- 补齐的数据将标记为非活跃状态，不会影响当前活跃的预测
- 前端会显示完整的预测K线，不再出现遗漏缺失的情况

这个功能完全自动化，不需要手动触发。系统保持原有的预测规则和生成逻辑，只是增加了自动检查和补齐机制。

## 节气预测折线功能

基于节气周期构建每日的预测折线，并在实时行情折线图图表底部展示这些预测的折线。

### 核心逻辑

预测折线基于以下规则生成：

1. 每月第一个节气日的预测：
   - 数据来源：节气日前一日23:00开始的48条1分钟行情数据
   - 生成时间：节气日0点
   - 预测时间段：节气日前一日23:00 ~ 节气日23:00（24小时）

2. 每月第一个节气日后的预测：
   - 数据来源：从每月第一个节气日前一日的23:48开始的144条1分钟行情数据
   - 生成时间：从节气日当日23:00起，每隔三天
   - 预测时间段：该日23:00 ~ 第四日23:00（72小时）

3. 预测持续到下一个月第一个节气日前一日23:00为止

### 前端展示方案

- 预测折线显示在图表内底部40%区域
- 仅在折线图模式下显示预测折线，K线图模式下隐藏
- 与主图表实时行情折线完全解耦，不影响已有功能

### 技术实现

前端组件：
- PredictionLineChartLayer：独立的预测折线图层组件
- 使用lightweight-charts创建单独的LineSeries
- 通过API定时更新预测折线数据

后端服务：
- SolarTermPredictionService：节气预测折线生成和查询服务
- 基于1分钟K线数据生成30分钟周期的预测折线
- 定时任务自动根据节气周期生成预测

[MIT](LICENSE) 