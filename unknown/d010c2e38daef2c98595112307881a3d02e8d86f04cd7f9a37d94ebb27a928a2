import express from 'express';
import feedbackController from '../controllers/feedbackController';
import authenticate from '../middlewares/authMiddleware';

const router = express.Router();

// 用户反馈相关路由 - 需要用户认证
router.use(authenticate);

// 提交反馈
router.post('/', feedbackController.submitFeedback);

// 获取当前用户的反馈列表
router.get('/', feedbackController.getUserFeedbacks);

// 获取反馈详情
router.get('/:id', feedbackController.getFeedbackDetail);

export default router;
