import mongoose, { Document, Schema } from 'mongoose';

export interface PaymentDocument extends Document {
  userId: mongoose.Types.ObjectId;
  userEmail: string;                  // 用户邮箱
  amount: number;                    // 支付金额
  currency: string;                  // 币种 (USD, BTC, ETH等)
  plan: 'monthly' | 'quarterly' | 'yearly'; // 订阅计划
  paymentId: string;                 // NOWPayments 支付ID
  invoiceId: string;                 // NOWPayments 发票ID
  orderId: string;                   // NOWPayments 订单ID
  paymentAddress?: string;           // 支付地址
  paymentStatus: 'pending' | 'confirming' | 'confirmed' | 'sending' | 'partially_paid' | 'finished' | 'failed' | 'refunded' | 'expired';
  txId?: string;                     // 交易哈希
  failureCount?: number;             // 支付失败计数
  lockedUntil?: Date;                // 支付锁定时间
  createdAt: Date;                   // 创建时间
  updatedAt: Date;                   // 更新时间
}

const paymentSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userEmail: {
    type: String,
    required: true,
    index: true  // 添加索引以优化查询
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true
  },
  plan: {
    type: String,
    enum: ['monthly', 'quarterly', 'yearly'],
    required: true
  },
  paymentId: {
    type: String,
    required: true,
    unique: true
  },
  invoiceId: {
    type: String,
    required: true
  },
  orderId: {
    type: String,
    required: true,
    unique: true
  },
  paymentAddress: {
    type: String
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'confirming', 'confirmed', 'sending', 'partially_paid', 'finished', 'failed', 'refunded', 'expired'],
    default: 'pending'
  },
  txId: {
    type: String
  },
  failureCount: {
    type: Number,
    default: 0
  },
  lockedUntil: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

const Payment = mongoose.model<PaymentDocument>('Payment', paymentSchema);

export default Payment;