import mongoose, { Document, Schema } from 'mongoose';

/**
 * 通知类型枚举
 */
export type NotificationType =
  | 'trial_granted'        // 注册成功获得试用
  | 'trial_expiring'       // 试用期即将到期
  | 'subscription_success' // 订阅成功
  | 'subscription_expiring'// 订阅即将到期
  | 'invite_reward';       // 邀请奖励

/**
 * 通知优先级枚举
 */
export type NotificationPriority = 'low' | 'medium' | 'high';

/**
 * 通知文档接口
 */
export interface NotificationDocument extends Document {
  userId: mongoose.Types.ObjectId;     // 用户ID
  type: NotificationType;              // 通知类型
  title: string;                       // 通知标题
  content: string;                     // 通知内容
  isRead: boolean;                     // 是否已读
  priority: NotificationPriority;      // 优先级
  metadata?: any;                      // 额外数据（如试用天数、订阅计划等）
  createdAt: Date;                     // 创建时间
  readAt?: Date;                       // 阅读时间
  updatedAt: Date;                     // 更新时间
}

/**
 * 通知数据模式
 */
const notificationSchema = new Schema({
  // 用户ID，关联到User模型
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true  // 添加索引以优化查询
  },

  // 通知类型
  type: {
    type: String,
    enum: ['trial_granted', 'trial_expiring', 'subscription_success', 'subscription_expiring', 'invite_reward'],
    required: true,
    index: true  // 添加索引以便按类型查询
  },

  // 通知标题
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },

  // 通知内容
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },

  // 是否已读
  isRead: {
    type: Boolean,
    default: false,
    index: true  // 添加索引以便查询未读通知
  },

  // 优先级
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },

  // 额外元数据
  metadata: {
    type: Schema.Types.Mixed,
    default: null
  },

  // 阅读时间
  readAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,  // 自动添加createdAt和updatedAt字段
  versionKey: false  // 不使用版本字段（__v）
});

// 复合索引：用户ID + 创建时间（降序），用于高效分页查询
notificationSchema.index({ userId: 1, createdAt: -1 });

// 复合索引：用户ID + 是否已读，用于查询未读通知
notificationSchema.index({ userId: 1, isRead: 1 });

// 复合索引：用户ID + 类型，用于按类型查询通知
notificationSchema.index({ userId: 1, type: 1 });

// 添加前置钩子，在标记为已读时设置readAt时间
notificationSchema.pre('save', function(next) {
  if (this.isModified('isRead') && this.isRead && !this.readAt) {
    this.readAt = new Date();
  }
  next();
});

// 静态方法：创建通知
notificationSchema.statics.createNotification = async function(
  userId: mongoose.Types.ObjectId,
  type: NotificationType,
  title: string,
  content: string,
  priority: NotificationPriority = 'medium',
  metadata?: any
) {
  return this.create({
    userId,
    type,
    title,
    content,
    priority,
    metadata,
    isRead: false
  });
};

// 静态方法：获取用户未读通知数量
notificationSchema.statics.getUnreadCount = async function(userId: mongoose.Types.ObjectId) {
  return this.countDocuments({ userId, isRead: false });
};

// 静态方法：标记用户所有通知为已读
notificationSchema.statics.markAllAsRead = async function(userId: mongoose.Types.ObjectId) {
  const now = new Date();
  return this.updateMany(
    { userId, isRead: false },
    { isRead: true, readAt: now }
  );
};

// 创建并导出模型
const Notification = mongoose.model<NotificationDocument>('Notification', notificationSchema);

export default Notification;