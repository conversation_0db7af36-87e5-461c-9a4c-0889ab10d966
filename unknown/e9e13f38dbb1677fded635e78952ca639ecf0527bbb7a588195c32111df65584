<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <!-- 网格背景 -->
  <g stroke="rgba(255,255,255,0.05)" stroke-width="1">
    <g id="vertical-lines">
      <line x1="0" y1="0" x2="0" y2="400" />
      <line x1="100" y1="0" x2="100" y2="400" />
      <line x1="200" y1="0" x2="200" y2="400" />
      <line x1="300" y1="0" x2="300" y2="400" />
      <line x1="400" y1="0" x2="400" y2="400" />
      <line x1="500" y1="0" x2="500" y2="400" />
      <line x1="600" y1="0" x2="600" y2="400" />
      <line x1="700" y1="0" x2="700" y2="400" />
      <line x1="800" y1="0" x2="800" y2="400" />
    </g>
    <g id="horizontal-lines">
      <line x1="0" y1="0" x2="800" y2="0" />
      <line x1="0" y1="100" x2="800" y2="100" />
      <line x1="0" y1="200" x2="800" y2="200" />
      <line x1="0" y1="300" x2="800" y2="300" />
      <line x1="0" y1="400" x2="800" y2="400" />
    </g>
  </g>

  <!-- 价格图表曲线 -->
  <path d="M0,300 C50,280 70,260 100,240 S150,220 200,200 S250,150 300,180 S350,220 400,160 S450,100 500,120 S550,140 600,110 S650,130 700,150 S750,180 800,160" 
        fill="none" 
        stroke="#3B82F6" 
        stroke-width="3"
        opacity="0.8" />
        
  <!-- 面积填充 -->
  <path d="M0,300 C50,280 70,260 100,240 S150,220 200,200 S250,150 300,180 S350,220 400,160 S450,100 500,120 S550,140 600,110 S650,130 700,150 S750,180 800,160 L800,400 L0,400 Z" 
        fill="url(#gradient)" 
        opacity="0.2" />
  
  <!-- 预测线段 (虚线) -->
  <path d="M600,110 C650,90 700,70 800,50" 
        fill="none" 
        stroke="#8B5CF6" 
        stroke-width="3"
        stroke-dasharray="6,6"
        opacity="0.8" />

  <!-- 亮点标记 -->
  <circle cx="400" cy="160" r="5" fill="#3B82F6" />
  <circle cx="600" cy="110" r="5" fill="#8B5CF6" />
  
  <!-- 水平基准线 -->
  <line x1="0" y1="200" x2="800" y2="200" stroke="#FFFFFF" stroke-width="1" stroke-dasharray="4,4" opacity="0.3" />
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0" />
    </linearGradient>
  </defs>
</svg> 