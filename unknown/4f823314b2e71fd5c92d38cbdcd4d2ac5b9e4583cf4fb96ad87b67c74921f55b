import React, { useEffect } from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import useUserStore from '../store/useUserStore';

const PrivateRoute: React.FC = () => {
  const { isAuthenticated, token, fetchUserInfo, isInitializing } = useUserStore();
  const location = useLocation();

  useEffect(() => {
    // 如果有 token 但用户信息不存在，尝试获取用户信息
    if (token && !isAuthenticated) {
      fetchUserInfo();
    }
  }, [token, isAuthenticated, fetchUserInfo]);

  // 如果正在初始化（检查用户状态），显示加载状态
  if (isInitializing) {
    // 可以返回一个简单的加载指示器或空白内容，避免闪烁
    return <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-background">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>;
  }

  // 如果用户没有认证，重定向到登录页
  if (!isAuthenticated) {
    // 保存用户尝试访问的路径，以便登录后重定向回来
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  // 已认证，渲染子路由
  return <Outlet />;
};

export default PrivateRoute; 