/**
 * 图表相关类型定义
 * 
 * 本文件定义了与图表相关的所有类型，从Chart.tsx组件中提取，
 * 用于提高代码复用性和维护性。
 */

import { Time, CandlestickData } from 'lightweight-charts';

/**
 * K线数据接口定义
 */
export interface KLineData {
  time: Time; // 时间戳
  open: number; // 开盘价
  high: number; // 最高价
  low: number; // 最低价
  close: number; // 收盘价
  volume?: number; // 交易量（可选）
}

/**
 * 预测数据接口定义
 */
export interface PredictionData {
  time: Time; // 时间戳
  open: number; // 开盘价
  high: number; // 最高价
  low: number; // 最低价
  close: number; // 收盘价
  isActive: boolean; // 是否为活跃预测（最新预测）
}

/**
 * 处理后的预测数据接口
 */
export interface ProcessedPrediction extends CandlestickData {
  isActive: boolean;
  originalDirection: 'up' | 'down';
  _opacity?: number;
}

/**
 * 预测折线数据点
 */
export interface PredictionLineData {
  time: number; // 时间戳（毫秒）
  value: number; // 预测值
}

/**
 * 格式化后的预测折线数据点
 */
export interface FormattedPredictionPoint {
  time: Time; // lightweight-charts格式的时间
  value: number; // 预测值
} 