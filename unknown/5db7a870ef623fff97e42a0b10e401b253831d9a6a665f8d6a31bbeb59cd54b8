import { Request, Response } from 'express';
import Feedback from '../models/Feedback';
import mongoose from 'mongoose';

/**
 * 用户提交反馈
 * POST /api/feedback
 */
export const submitFeedback = async (req: Request, res: Response) => {
  try {
    const { title, content } = req.body;
    const userId = req.user?._id;
    const userEmail = req.user?.email;

    // 验证必要字段
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '请提供反馈标题和内容'
      });
    }

    // 创建新的反馈记录
    const feedback = new Feedback({
      userId,
      userEmail,
      title,
      content,
      status: 'pending',
      createdAt: new Date()
    });

    await feedback.save();

    res.status(201).json({
      success: true,
      message: '反馈提交成功',
      feedback: {
        _id: feedback._id,
        title: feedback.title,
        content: feedback.content,
        status: feedback.status,
        createdAt: feedback.createdAt
      }
    });
  } catch (error) {
    console.error('提交反馈错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取当前用户的反馈列表
 * GET /api/feedback
 */
export const getUserFeedbacks = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    // 分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // 查询用户的反馈记录
    const feedbacks = await Feedback.find({ userId })
      .sort({ createdAt: -1 }) // 按创建时间倒序排列
      .skip(skip)
      .limit(limit);

    // 获取总记录数
    const total = await Feedback.countDocuments({ userId });

    res.json({
      success: true,
      data: feedbacks,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取用户反馈列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取反馈详情
 * GET /api/feedback/:id
 */
export const getFeedbackDetail = async (req: Request, res: Response) => {
  try {
    const feedbackId = req.params.id;
    const userId = req.user?._id;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    // 查询反馈记录
    const feedback = await Feedback.findById(feedbackId);

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: '反馈不存在'
      });
    }

    // 验证是否为当前用户的反馈（非管理员只能查看自己的反馈）
    if (req.user?.role !== 'admin' && feedback.userId.toString() !== userId?.toString()) {
      return res.status(403).json({
        success: false,
        message: '无权查看此反馈'
      });
    }

    res.json({
      success: true,
      data: feedback
    });
  } catch (error) {
    console.error('获取反馈详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 管理员获取反馈详情
 * GET /api/admin/feedback/:id
 */
export const getAdminFeedbackDetail = async (req: Request, res: Response) => {
  try {
    const feedbackId = req.params.id;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    // 查询反馈记录（管理员可以查看所有反馈）
    const feedback = await Feedback.findById(feedbackId);

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: '反馈不存在'
      });
    }

    res.json({
      success: true,
      data: feedback
    });
  } catch (error) {
    console.error('获取反馈详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 管理员获取所有反馈列表
 * GET /api/admin/feedback
 */
export const getAllFeedbacks = async (req: Request, res: Response) => {
  try {
    // 分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // 筛选参数
    const status = req.query.status as string;
    const search = req.query.search as string;

    // 构建查询条件
    const filter: any = {};
    if (status) {
      filter.status = status;
    }
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } }
      ];
    }

    // 查询反馈记录
    const feedbacks = await Feedback.find(filter)
      .sort({ createdAt: -1 }) // 按创建时间倒序排列
      .skip(skip)
      .limit(limit);

    // 获取总记录数
    const total = await Feedback.countDocuments(filter);

    res.json({
      success: true,
      data: feedbacks,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取所有反馈列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 管理员回复反馈
 * POST /api/admin/feedback/:id/reply
 */
export const replyFeedback = async (req: Request, res: Response) => {
  try {
    const feedbackId = req.params.id;
    const { reply, status } = req.body;

    // 验证必要字段
    if (!reply) {
      return res.status(400).json({
        success: false,
        message: '请提供回复内容'
      });
    }

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    // 查询反馈记录
    const feedback = await Feedback.findById(feedbackId);

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: '反馈不存在'
      });
    }

    // 更新反馈记录
    feedback.adminReply = reply;
    feedback.replyAt = new Date();

    // 如果提供了状态，则更新状态
    if (status && ['pending', 'processing', 'replied'].includes(status)) {
      feedback.status = status as 'pending' | 'processing' | 'replied';
    } else {
      // 默认设置为已回复状态
      feedback.status = 'replied';
    }

    await feedback.save();

    res.json({
      success: true,
      message: '回复成功',
      data: feedback
    });
  } catch (error) {
    console.error('回复反馈错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 管理员更新反馈状态
 * PATCH /api/admin/feedback/:id/status
 */
export const updateFeedbackStatus = async (req: Request, res: Response) => {
  try {
    const feedbackId = req.params.id;
    const { status } = req.body;

    // 验证必要字段
    if (!status || !['pending', 'processing', 'replied'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的状态值'
      });
    }

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    // 更新反馈状态
    const feedback = await Feedback.findByIdAndUpdate(
      feedbackId,
      { status },
      { new: true }
    );

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: '反馈不存在'
      });
    }

    res.json({
      success: true,
      message: '状态更新成功',
      data: feedback
    });
  } catch (error) {
    console.error('更新反馈状态错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  submitFeedback,
  getUserFeedbacks,
  getFeedbackDetail,
  getAdminFeedbackDetail,
  getAllFeedbacks,
  replyFeedback,
  updateFeedbackStatus
};
