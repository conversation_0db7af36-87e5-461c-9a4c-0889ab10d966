import React, { useState } from 'react';
import useAdminStore from '../../store/useAdminStore';
import ConfirmDeleteModal from './ConfirmDeleteModal';
import SubscriptionEditModal from './SubscriptionEditModal';
import { useToast } from '../ui/use-toast';

interface User {
  _id: string;
  email: string;
  role: string;
  isVerified: boolean;
  isOnline: boolean;
  createdAt: string;
  lastLoginAt?: string;
  originalRole?: string;
  subscription?: {
    plan?: string;
    status?: string;
    endDate?: string;
  };
  status?: string;
}

interface UserTableProps {
  users: User[];
  isLoading: boolean;
  onRefresh: () => void;
}

const UserTable: React.FC<UserTableProps> = ({ users, isLoading, onRefresh }) => {
  const { updateUserRole, deleteUser, banUser, unbanUser } = useAdminStore();
  const { toast } = useToast();
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [error, setError] = useState('');
  
  // 确认删除用户模态框
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  
  // 确认封禁/解封模态框
  const [banModalOpen, setBanModalOpen] = useState(false);
  const [userToBan, setUserToBan] = useState<User | null>(null);
  const [isBanning, setIsBanning] = useState(false); // true = 封禁, false = 解封
  
  // 订阅编辑模态框
  const [subscriptionModalOpen, setSubscriptionModalOpen] = useState(false);
  const [userToEditSubscription, setUserToEditSubscription] = useState<User | null>(null);

  const handleRoleChange = async (userId: string) => {
    try {
      setError('');
      await updateUserRole(userId, selectedRole);
      setEditingUserId(null);
      toast({
        title: '用户角色更新成功',
        variant: 'default'
      });
      onRefresh(); // 刷新用户列表
    } catch (err) {
      const message = err instanceof Error ? err.message : '更新角色失败';
      setError(message);
    }
  };

  const startEditing = (userId: string, currentRole: string) => {
    setEditingUserId(userId);
    setSelectedRole(currentRole);
  };

  const cancelEditing = () => {
    setEditingUserId(null);
    setError('');
  };
  
  // 删除用户
  const confirmDelete = (user: User) => {
    setUserToDelete(user);
    setDeleteModalOpen(true);
  };
  
  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    
    try {
      await deleteUser(userToDelete._id);
      toast({
        title: '用户删除成功',
        variant: 'default'
      });
      onRefresh(); // 刷新用户列表
    } catch (err) {
      const message = err instanceof Error ? err.message : '删除用户失败';
      throw new Error(message);
    }
  };
  
  // 封禁用户
  const confirmBanUser = (user: User) => {
    setUserToBan(user);
    setIsBanning(true);
    setBanModalOpen(true);
  };
  
  // 解封用户
  const confirmUnbanUser = (user: User) => {
    setUserToBan(user);
    setIsBanning(false);
    setBanModalOpen(true);
  };
  
  const handleBanOrUnbanUser = async () => {
    if (!userToBan) return;
    
    try {
      if (isBanning) {
        await banUser(userToBan._id);
        toast({
          title: '用户已封禁',
          variant: 'default'
        });
      } else {
        await unbanUser(userToBan._id);
        toast({
          title: '用户已解封',
          variant: 'default'
        });
      }
      onRefresh(); // 刷新用户列表
    } catch (err) {
      const message = err instanceof Error ? err.message : isBanning ? '封禁用户失败' : '解封用户失败';
      throw new Error(message);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '未记录';
    return new Date(dateString).toLocaleString();
  };
  
  // 判断用户是否被封禁
  const isBanned = (user: User) => {
    return user.role === 'banned';
  };

  // 打开订阅编辑模态框
  const openSubscriptionModal = (user: User) => {
    setUserToEditSubscription(user);
    setSubscriptionModalOpen(true);
  };

  return (
    <div className="overflow-x-auto bg-white dark:bg-background-card shadow-card rounded-lg">
      {error && (
        <div className="bg-error/10 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          <p>{error}</p>
        </div>
      )}

      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-background-input">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              邮箱
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              角色
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              注册时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              最近登录
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              订阅状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-content-muted dark:text-content-secondary uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-background-card divide-y divide-gray-200 dark:divide-gray-700">
          {isLoading ? (
            <tr>
              <td colSpan={7} className="px-6 py-4 text-center">
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                </div>
              </td>
            </tr>
          ) : users.length === 0 ? (
            <tr>
              <td colSpan={7} className="px-6 py-4 text-center text-content-muted dark:text-content-secondary">
                没有找到用户
              </td>
            </tr>
          ) : (
            users.map((user) => (
              <tr key={user._id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-gray-900 dark:text-content-primary">
                      {user.email}
                      {!user.isVerified && (
                        <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-warning/10 text-yellow-800">
                          未验证
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {editingUserId === user._id ? (
                    <select
                      value={selectedRole}
                      onChange={(e) => setSelectedRole(e.target.value)}
                      className="block w-full px-3 py-2 text-gray-700 bg-white dark:bg-background-input dark:text-content-primary border border-border dark:border-border rounded-md focus:outline-none focus:ring-blue-500 focus:border-primary"
                    >
                      <option value="trial">试用用户</option>
                      <option value="normal">普通用户</option>
                      <option value="subscriber">订阅用户</option>
                      <option value="admin">管理员</option>
                    </select>
                  ) : (
                    <div>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.role === 'admin'
                            ? 'bg-error/10 text-red-800'
                            : user.role === 'subscriber'
                            ? 'bg-purple-100 text-purple-800'
                            : user.role === 'normal'
                            ? 'bg-success/10 text-green-800'
                            : user.role === 'banned'
                            ? 'bg-gray-800 text-white'
                            : 'bg-warning/10 text-yellow-800'
                        }`}
                      >
                        {user.role === 'admin'
                          ? '管理员'
                          : user.role === 'subscriber'
                          ? '订阅用户'
                          : user.role === 'normal'
                          ? '普通用户'
                          : user.role === 'banned'
                          ? '已封禁'
                          : '试用用户'}
                      </span>
                      {isBanned(user) && (
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                          (原角色: {user.originalRole})
                        </span>
                      )}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-content-muted dark:text-content-secondary">
                  {formatDate(user.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-content-muted dark:text-content-secondary">
                  {formatDate(user.lastLoginAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.isOnline ? 'bg-success/10 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {user.isOnline ? '在线' : '离线'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-content-muted dark:text-content-secondary">
                  <div className="flex justify-between items-center">
                    <div>
                      {user.subscription ? (
                        <div>
                          <div>
                            <span className="text-xs font-medium text-gray-700 dark:text-content-secondary">计划:</span>{' '}
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              {user.subscription.plan === 'monthly' ? '月度' : 
                               user.subscription.plan === 'quarterly' ? '季度' : 
                               user.subscription.plan === 'yearly' ? '年度' : '未知'}
                            </span>
                          </div>
                          {user.subscription.status && (
                            <div className="mt-1">
                              <span className="text-xs font-medium text-gray-700 dark:text-content-secondary">状态:</span>{' '}
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                user.subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                                user.subscription.status === 'expired' ? 'bg-red-100 text-red-800' : 
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {user.subscription.status === 'active' ? '有效' : 
                                 user.subscription.status === 'expired' ? '已过期' :
                                 user.subscription.status === 'cancelled' ? '已取消' : user.subscription.status}
                              </span>
                            </div>
                          )}
                          {user.subscription.endDate && (
                            <div className="mt-1 text-xs">
                              <span className="font-medium text-gray-700 dark:text-content-secondary">截止:</span>{' '}
                              {formatDate(user.subscription.endDate)}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500 dark:text-gray-400">未订阅</span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => openSubscriptionModal(user)}
                      className="ml-2 text-blue-600 hover:text-blue-900 dark:text-primary dark:hover:text-blue-300"
                    >
                      {user.subscription ? '编辑' : '添加'}
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {editingUserId === user._id ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleRoleChange(user._id)}
                        className="text-blue-600 hover:text-blue-900 dark:text-primary dark:hover:text-blue-300"
                      >
                        保存
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="text-content-muted hover:text-gray-900 dark:text-content-secondary dark:hover:text-content-secondary"
                      >
                        取消
                      </button>
                    </div>
                  ) : (
                    <div className="flex space-x-2">
                      {!isBanned(user) && (
                        <button
                          onClick={() => startEditing(user._id, user.role)}
                          className="text-blue-600 hover:text-blue-900 dark:text-primary dark:hover:text-blue-300"
                        >
                          编辑角色
                        </button>
                      )}
                      
                      {isBanned(user) ? (
                        <button
                          onClick={() => confirmUnbanUser(user)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        >
                          解除封禁
                        </button>
                      ) : (
                        <button
                          onClick={() => confirmBanUser(user)}
                          className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                        >
                          封禁
                        </button>
                      )}
                      
                      <button
                        onClick={() => confirmDelete(user)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        删除
                      </button>
                    </div>
                  )}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
      
      {/* 确认删除模态框 */}
      <ConfirmDeleteModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteUser}
        title="确认删除用户"
        message={`您确定要删除用户 ${userToDelete?.email} 吗？此操作不可逆。`}
      />
      
      {/* 确认封禁/解封模态框 */}
      <ConfirmDeleteModal
        isOpen={banModalOpen}
        onClose={() => setBanModalOpen(false)}
        onConfirm={handleBanOrUnbanUser}
        title={isBanning ? "确认封禁用户" : "确认解除封禁"}
        message={isBanning 
          ? `您确定要封禁用户 ${userToBan?.email} 吗？封禁后该用户将无法登录系统。` 
          : `您确定要解除对 ${userToBan?.email} 的封禁吗？解除后将恢复用户原有权限。`
        }
      />

      {/* 订阅编辑模态框 */}
      <SubscriptionEditModal
        isOpen={subscriptionModalOpen}
        onClose={() => setSubscriptionModalOpen(false)}
        onSuccess={onRefresh}
        user={userToEditSubscription}
      />
    </div>
  );
};

export default UserTable; 