import mongoose, { Document, Schema } from 'mongoose';

/**
 * 用户反馈文档接口
 * 定义用户反馈数据的结构和类型
 */
export interface FeedbackDocument extends Document {
  userId: mongoose.Types.ObjectId;  // 提交反馈的用户ID
  userEmail: string;                // 用户邮箱（冗余存储，便于查询）
  title: string;                    // 反馈标题
  content: string;                  // 反馈内容
  status: 'pending' | 'processing' | 'replied';  // 处理状态：未处理/处理中/已回复
  adminReply?: string;              // 管理员回复内容
  replyAt?: Date;                   // 回复时间
  createdAt: Date;                  // 创建时间
  updatedAt: Date;                  // 更新时间
}

/**
 * 用户反馈模式
 * 定义MongoDB中用户反馈集合的结构
 */
const feedbackSchema = new Schema({
  // 用户ID，关联到User模型
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 用户邮箱（冗余存储，便于查询）
  userEmail: {
    type: String,
    required: true,
    index: true  // 添加索引以优化查询
  },
  
  // 反馈标题
  title: {
    type: String,
    required: true,
    trim: true
  },
  
  // 反馈内容
  content: {
    type: String,
    required: true
  },
  
  // 处理状态
  status: {
    type: String,
    enum: ['pending', 'processing', 'replied'],
    default: 'pending'
  },
  
  // 管理员回复内容（可选）
  adminReply: {
    type: String,
    default: null
  },
  
  // 回复时间（可选）
  replyAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true  // 自动添加createdAt和updatedAt字段
});

// 创建并导出模型
const Feedback = mongoose.model<FeedbackDocument>('Feedback', feedbackSchema);

export default Feedback;
