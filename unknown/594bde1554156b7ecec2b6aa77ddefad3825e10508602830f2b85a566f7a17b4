import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';

const Settings: React.FC = () => {
  const navigate = useNavigate();

  // 组件挂载时重定向到新的设置页面
  useEffect(() => {
    navigate('/admin/settings/basic');
  }, [navigate]);

  // 渲染加载状态，等待重定向
  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Settings;
