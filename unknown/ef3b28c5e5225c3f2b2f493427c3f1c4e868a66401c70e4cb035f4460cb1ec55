import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../../components/admin/AdminLayout';

const SettingsIndex: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // 默认重定向到基础设置页面
    navigate('/admin/settings/basic');
  }, [navigate]);

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default SettingsIndex;
