import winston from 'winston';
import 'winston-daily-rotate-file';
import path from 'path';

// 安全的JSON序列化函数，处理循环引用
const safeStringify = (obj: any): string => {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular]';
      }
      seen.add(value);
    }
    return value;
  });
};
import fs from 'fs';

// 确保日志目录存在
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 创建日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 如果有额外的元数据，添加到日志中
    if (Object.keys(meta).length > 0) {
      try {
        log += ` ${safeStringify(meta)}`;
      } catch (error: any) {
        log += ` [Error serializing metadata: ${error?.message || 'Unknown error'}]`;
      }
    }
    
    // 如果有错误堆栈，添加到日志中
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

// 创建控制台格式（简化版）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message }) => {
    return `${timestamp} ${level}: ${message}`;
  })
);

// 创建winston logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // 控制台输出（保持现有行为）
    new winston.transports.Console({
      format: consoleFormat
    }),
    
    // 按日期轮转的文件输出
    new winston.transports.DailyRotateFile({
      filename: path.join(logsDir, 'app-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '5m',      // 单文件最大5MB
      maxFiles: '15d',    // 保留15天
      format: logFormat
    })
  ]
});

// 导出logger实例
export default logger;
