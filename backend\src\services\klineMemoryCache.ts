import logger from '../utils/logger';

/**
 * K线数据缓存项接口
 */
interface KlineCacheItem {
  data: any[];
  timestamp: number;
  ttl: number;
}

/**
 * K线内存缓存服务
 * 专门用于缓存实时K线数据，避免重复调用币安API
 */
class KlineMemoryCache {
  private cache = new Map<string, KlineCacheItem>();
  private readonly DEFAULT_TTL = 15 * 1000; // 15秒TTL
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 每分钟清理一次过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60 * 1000);
    
    logger.info('K线内存缓存服务已启动');
  }

  /**
   * 设置缓存数据
   * @param key 缓存键
   * @param data 数据
   * @param ttl 生存时间（毫秒）
   */
  set(key: string, data: any[], ttl: number = this.DEFAULT_TTL): void {
    const item: KlineCacheItem = {
      data: JSON.parse(JSON.stringify(data)), // 深拷贝避免引用问题
      timestamp: Date.now(),
      ttl
    };
    
    this.cache.set(key, item);
    logger.debug(`K线缓存已设置: ${key}, 数据量: ${data.length}, TTL: ${ttl}ms`);
  }

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @returns 缓存的数据或null
   */
  get(key: string): any[] | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      logger.debug(`K线缓存已过期并删除: ${key}`);
      return null;
    }
    
    logger.debug(`K线缓存命中: ${key}, 数据量: ${item.data.length}`);
    return JSON.parse(JSON.stringify(item.data)); // 返回深拷贝
  }

  /**
   * 检查缓存是否存在且未过期
   * @param key 缓存键
   * @returns 是否有效
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 删除指定缓存
   * @param key 缓存键
   */
  delete(key: string): void {
    this.cache.delete(key);
    logger.debug(`K线缓存已删除: ${key}`);
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug(`K线缓存清理完成，删除了 ${cleanedCount} 个过期项`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    totalItems: number;
    memoryUsage: string;
    cacheKeys: string[];
  } {
    const totalItems = this.cache.size;
    const memoryUsage = `${Math.round(JSON.stringify([...this.cache.values()]).length / 1024)} KB`;
    const cacheKeys = Array.from(this.cache.keys());
    
    return {
      totalItems,
      memoryUsage,
      cacheKeys
    };
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    logger.info('K线缓存已清空');
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
    logger.info('K线内存缓存服务已销毁');
  }
}

// 导出单例实例
export default new KlineMemoryCache();
