import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Home, AlertTriangle } from 'lucide-react';
import { Button } from '../components/ui/button';

const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(10);

  // 10秒倒计时自动跳转到首页
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          navigate('/', { replace: true });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate]);

  const handleGoHome = () => {
    navigate('/', { replace: true });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card flex items-center justify-center px-4">
      <div className="w-full max-w-lg mx-auto text-center">
        {/* 主要错误卡片 - 固定宽度 */}
        <div className="bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 relative overflow-hidden">
          {/* 背景装饰效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
          <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

          <div className="relative z-10">
            {/* 404 大标题 */}
            <div className="mb-8">
              <div className="flex items-center justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-yellow-400" />
              </div>
              <h1 className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-pink font-mono tracking-wider mb-4">
                404
              </h1>
              <div className="h-1 w-24 bg-gradient-to-r from-cyber-cyan to-cyber-purple mx-auto rounded-full"></div>
            </div>

            {/* 错误信息 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-cyber-text mb-4 font-sans">
                {t('notFound.title', '页面未找到')}
              </h2>
              <p className="text-cyber-muted mb-4 font-mono text-lg">
                {t('notFound.description', '抱歉，您访问的页面不存在或已被移动。')}
              </p>
            </div>

            {/* 自动跳转倒计时 */}
            <div className="mb-8 p-4 bg-cyber-cyan/10 rounded-xl border border-cyber-cyan/30">
              <p className="text-cyber-cyan font-mono text-sm">
                {t('notFound.autoRedirect', '将在')}
                <span className="font-bold text-lg mx-2 text-cyber-text">{countdown}</span>
                {t('notFound.secondsRedirectHome', '秒后自动跳转到首页')}
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center">
              <Button
                onClick={handleGoHome}
                className="flex items-center space-x-2 bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-bold px-6 py-3 rounded-xl shadow-lg hover:shadow-[0_0_30px_rgba(0,245,255,0.4)] transition-all duration-300 active:scale-[0.98] font-mono"
              >
                <Home className="h-5 w-5" />
                <span>{t('notFound.goHome', '返回首页')}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
