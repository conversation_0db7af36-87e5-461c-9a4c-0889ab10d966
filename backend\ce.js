const axios = require('axios');
const https = require('https');

// 测试不同的API端点和配置
const endpoints = [
  // 测试网API
  'https://testnet.binance.vision/api/v3/time',
  // 主网API
  'https://api.binance.com/api/v3/time',
  'https://api1.binance.com/api/v3/time',
  'https://api2.binance.com/api/v3/time',
  'https://api3.binance.com/api/v3/time',
  'https://api-gcp.binance.com/api/v3/time'
];

// 创建配置了代理的axios实例 - 使用HTTP代理
const axiosWithHttpProxy = axios.create({
  proxy: {
    host: '127.0.0.1',
    port: 10809,
    protocol: 'http'
  },
  timeout: 30000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
});

// 创建配置了代理的axios实例 - 使用HTTPS代理
const axiosWithHttpsProxy = axios.create({
  proxy: {
    host: '127.0.0.1',
    port: 10808,  // v2rayN默认SOCKS代理端口
    protocol: 'https'
  },
  timeout: 30000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  },
  // 忽略SSL证书验证
  httpsAgent: new https.Agent({ 
    rejectUnauthorized: false
  })
});

// 尝试所有端点和代理组合
async function testAllOptions() {
  console.log('正在测试多种组合方式连接币安API...');
  
  // 测试HTTP代理
  console.log('\n[测试HTTP代理连接]');
  for (const endpoint of endpoints) {
    try {
      console.log(`测试端点: ${endpoint}`);
      const res = await axiosWithHttpProxy.get(endpoint);
      console.log(`✅ 成功! ${endpoint} 正常返回:`, res.data);
      console.log('使用HTTP代理连接成功! 端点:', endpoint);
      return true;
    } catch (err) {
      console.error(`❌ ${endpoint} 请求失败:`, err.message);
      if (err.code) {
        console.error('错误代码:', err.code);
      }
      if (err.response) {
        console.error('响应状态:', err.response.status);
      }
    }
  }
  
  // 测试HTTPS代理
  console.log('\n[测试HTTPS代理连接]');
  for (const endpoint of endpoints) {
    try {
      console.log(`测试端点: ${endpoint}`);
      const res = await axiosWithHttpsProxy.get(endpoint);
      console.log(`✅ 成功! ${endpoint} 正常返回:`, res.data);
      console.log('使用HTTPS代理连接成功! 端点:', endpoint);
      return true;
    } catch (err) {
      console.error(`❌ ${endpoint} 请求失败:`, err.message);
      if (err.code) {
        console.error('错误代码:', err.code);
      }
      if (err.response) {
        console.error('响应状态:', err.response.status);
      }
    }
  }
  
  // 测试使用socks协议
  console.log('\n[测试SOCKS代理连接]');
  try {
    // 安装socks-proxy-agent以支持SOCKS代理
    console.log('尝试安装socks-proxy-agent...');
    require('child_process').execSync('npm install socks-proxy-agent --no-save', { stdio: 'inherit' });
    
    const { SocksProxyAgent } = require('socks-proxy-agent');
    const socksAgent = new SocksProxyAgent('socks5://127.0.0.1:10808');
    
    const axiosWithSocksProxy = axios.create({
      httpAgent: socksAgent,
      httpsAgent: socksAgent,
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json'
      }
    });
    
    for (const endpoint of endpoints) {
      try {
        console.log(`测试端点: ${endpoint}`);
        const res = await axiosWithSocksProxy.get(endpoint);
        console.log(`✅ 成功! ${endpoint} 正常返回:`, res.data);
        console.log('使用SOCKS代理连接成功! 端点:', endpoint);
        return true;
      } catch (err) {
        console.error(`❌ ${endpoint} 请求失败:`, err.message);
      }
    }
  } catch (err) {
    console.error('设置SOCKS代理失败:', err.message);
  }
  
  console.log('\n所有连接尝试均失败，建议:');
  console.log('1. 确认v2rayN代理正在运行，并且已设置为全局代理模式');
  console.log('2. 检查代理端口配置（HTTP代理默认为10809，SOCKS代理默认为10808）');
  console.log('3. 考虑使用其他第三方数据源替代币安API');
  console.log('4. 考虑在非中国大陆地区部署服务器来获取币安数据');
}

testAllOptions();
