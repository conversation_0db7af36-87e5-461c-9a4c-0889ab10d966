import React, { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Logo } from '../components/Logo';
import configService from '../services/configService';

const SubscriptionLayout: React.FC = () => {
  const { t } = useTranslation();
  const [copyright, setCopyright] = useState('');

  useEffect(() => {
    const loadCopyright = async () => {
      try {
        const copyrightText = await configService.getCopyright();
        setCopyright(copyrightText);
      } catch (error) {
        console.error('获取版权信息失败:', error);
        // 使用默认版权信息
        setCopyright(`© ${new Date().getFullYear()} ${t('app.title')}. All rights reserved.`);
      }
    };
    loadCopyright();
  }, [t]);

  return (
    <div className="min-h-screen bg-background text-content-primary flex flex-col">
      {/* 顶部导航 */}
      <header className="border-b border-border py-4">
        <div className="container mx-auto px-4 flex justify-center">
          <Logo />
        </div>
      </header>

      {/* 主内容区 */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <Outlet />
      </main>

      {/* 页脚 */}
      <footer className="border-t border-border py-6">
        <div className="container mx-auto px-4 text-center text-content-secondary text-sm">
          {copyright}
        </div>
      </footer>
    </div>
  );
};

export default SubscriptionLayout; 