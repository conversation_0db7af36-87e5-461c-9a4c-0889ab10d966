import { Request, Response } from 'express';
import User, { UserDocument } from '../models/User';
import mongoose from 'mongoose';
import systemSettingsService from '../services/systemSettingsService';
import configService from '../services/configService';
import bcrypt from 'bcrypt';
import { generateAdminToken } from '../middlewares/adminAuthMiddleware';



/**
 * 管理员登录
 * POST /api/admin/login
 */
export const adminLogin = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // 验证请求体
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱和密码'
      });
    }

    // 查找用户
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查账号是否被锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      // 计算剩余锁定时间（分钟）
      const remainingMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / (60 * 1000));

      return res.status(403).json({
        success: false,
        message: `账号已被锁定，请在 ${remainingMinutes} 分钟后重试`
      });
    }

    // 验证密码
    const isPasswordValid = await user.comparePassword(password);

    // 从配置服务获取登录失败限制次数和锁定时间
    const loginFailLimit = await configService.get('securitySettings.loginFailLimit', 5);
    const loginLockTime = await configService.get('securitySettings.loginLockTime', 2);

    if (!isPasswordValid) {
      // 增加登录失败计数
      user.loginFailCount = (user.loginFailCount || 0) + 1;

      // 如果达到失败限制次数，锁定账号
      if (user.loginFailCount >= loginFailLimit) {
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + loginLockTime); // 锁定指定小时
        user.lockedUntil = lockUntil;

        await user.save();

        return res.status(403).json({
          success: false,
          message: `由于多次登录失败，账号已被锁定 ${loginLockTime} 小时`
        });
      }

      await user.save();

      return res.status(401).json({
        success: false,
        message: `密码错误，还有 ${loginFailLimit - user.loginFailCount} 次尝试机会`
      });
    }

    // 检查是否为管理员
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '没有管理员权限'
      });
    }

    // 登录成功，重置登录失败计数和锁定时间
    user.loginFailCount = 0;
    user.lockedUntil = undefined;

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    user.isOnline = true;
    await user.save();

    // 使用统一的管理员token生成
    const accessToken = generateAdminToken(user._id.toString());

    // 返回标准格式
    res.json({
      success: true,
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      },
      accessToken // 使用accessToken而不是token
    });
  } catch (error) {
    console.error('管理员登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取用户列表（分页）
 * GET /api/admin/users
 */
export const getUsers = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string || '';
    const isOnline = req.query.isOnline === 'true';
    const role = req.query.role as string;
    const isVerified = req.query.isVerified === 'true';
    const subscriptionStatus = req.query.subscriptionStatus as string;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const filter: any = {};
    if (search) {
      filter.email = { $regex: search, $options: 'i' };
    }
    if (req.query.isOnline !== undefined) {
      filter.isOnline = isOnline;
    }
    if (role) {
      filter.role = role;
    }
    if (req.query.isVerified !== undefined) {
      filter.isVerified = isVerified;
    }
    if (subscriptionStatus) {
      if (subscriptionStatus === 'active') {
        filter['subscription.status'] = 'active';
      } else if (subscriptionStatus === 'expired') {
        filter['subscription.status'] = 'expired';
      } else if (subscriptionStatus === 'cancelled') {
        filter['subscription.status'] = 'cancelled';
      } else if (subscriptionStatus === 'none') {
        // 没有订阅
        filter.subscription = { $exists: false };
      }
    }

    // 获取用户总数
    const total = await User.countDocuments(filter);

    // 获取用户列表
    const users = await User.find(filter)
      .select('_id email username role isVerified isOnline createdAt lastLoginAt subscription trialEndsAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // 转换用户数据，添加订阅状态和试用期信息
    const enhancedUsers = users.map(user => {
      const now = new Date();
      const isInTrial = user.role === 'trial' && new Date(user.trialEndsAt) > now;
      const isSubscribed = user.subscription && user.subscription.status === 'active' && new Date(user.subscription.endDate) > now;

      return {
        _id: user._id,
        email: user.email,
        username: user.username,
        role: user.role,
        isVerified: user.isVerified,
        isOnline: user.isOnline,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        subscription: user.subscription ? {
          plan: user.subscription.plan,
          status: user.subscription.status,
          endDate: user.subscription.endDate
        } : null,
        trialEndsAt: user.trialEndsAt,
        status: isSubscribed ? 'subscribed' : (isInTrial ? 'trial' : 'inactive')
      };
    });

    res.json({
      success: true,
      users: enhancedUsers,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新用户角色
 * PATCH /api/admin/users/:id
 */
export const updateUserRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // 验证请求体
    if (!role || !['trial', 'normal', 'subscriber', 'admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的用户角色'
      });
    }

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 查找并更新用户
    const user = await User.findByIdAndUpdate(
      id,
      { role },
      { new: true }
    ).select('_id email role isVerified createdAt');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('更新用户角色错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 封禁用户账号
 * POST /api/admin/users/:id/ban
 */
export const banUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 查找并更新用户，将角色设置为"banned"
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 如果是尝试封禁管理员，拒绝操作
    if (user.role === 'admin' && req.user?._id.toString() !== id) {
      return res.status(403).json({
        success: false,
        message: '不能封禁管理员账号'
      });
    }

    // 保存用户原来的角色，以便解封时恢复
    const originalRole = user.role;

    // 检查originalRole是否是有效值（不能是'banned'）
    if (originalRole === 'banned') {
      return res.status(400).json({
        success: false,
        message: '用户已经处于封禁状态'
      });
    }

    user.role = 'banned';
    user.isOnline = false;
    // 在用户对象中添加原始角色信息
    user.originalRole = originalRole as 'trial' | 'normal' | 'subscriber' | 'admin';

    await user.save();

    res.json({
      success: true,
      message: '用户账号已封禁',
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('封禁用户账号错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 解封用户账号
 * POST /api/admin/users/:id/unban
 */
export const unbanUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 查找并更新用户
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (user.role !== 'banned') {
      return res.status(400).json({
        success: false,
        message: '该用户未被封禁'
      });
    }

    // 恢复用户原来的角色，如果找不到原始角色，则设为normal
    const originalRole = user.originalRole || 'normal';
    user.role = originalRole;

    // 清空原始角色字段
    user.set('originalRole', undefined);

    await user.save();

    res.json({
      success: true,
      message: '用户账号已解封',
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('解封用户账号错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 创建新用户
 * POST /api/admin/users
 */
export const createUser = async (req: Request, res: Response) => {
  try {
    const { email, password, role = 'normal', isVerified = true } = req.body;

    // 验证请求体
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱和密码'
      });
    }

    if (!['trial', 'normal', 'subscriber', 'admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户角色'
      });
    }

    // 检查邮箱是否已存在
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: '此邮箱已被注册'
      });
    }

    // 获取默认试用期天数
    const trialDays = await systemSettingsService.getDefaultTrialDays();
    const trialEndsAt = new Date();
    trialEndsAt.setDate(trialEndsAt.getDate() + trialDays);

    // 创建新用户
    const user = new User({
      email,
      password, // 密码会在保存前自动加密
      role,
      isVerified,
      trialEndsAt,
      registeredIp: req.ip || '由管理员创建',
      createdAt: new Date()
    });

    await user.save();

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        trialEndsAt: user.trialEndsAt
      }
    });
  } catch (error) {
    console.error('创建用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 删除用户
 * DELETE /api/admin/users/:id
 */
export const deleteUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 不能删除自己
    if (req.user?._id.toString() === id) {
      return res.status(400).json({
        success: false,
        message: '不能删除当前登录的管理员账号'
      });
    }

    // 查找用户
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 额外安全检查：不能删除管理员账号
    if (user.role === 'admin' && req.user?._id.toString() !== id) {
      return res.status(403).json({
        success: false,
        message: '不能删除其他管理员账号'
      });
    }

    // 删除用户
    await User.deleteOne({ _id: id });

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 更新用户订阅
 * PATCH /api/admin/users/:id/subscription
 */
export const updateUserSubscription = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { plan, status, endDate } = req.body;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 验证请求体
    if (!plan || !status || !endDate) {
      return res.status(400).json({
        success: false,
        message: '请提供订阅计划、状态和结束日期'
      });
    }

    // 验证计划和状态的有效值
    if (!['monthly', 'quarterly', 'yearly'].includes(plan)) {
      return res.status(400).json({
        success: false,
        message: '无效的订阅计划'
      });
    }

    if (!['active', 'expired', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的订阅状态'
      });
    }

    // 查找用户
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户订阅信息
    user.subscription = {
      plan,
      status,
      startDate: user.subscription?.startDate || new Date(),
      endDate: new Date(endDate)
    };

    // 根据订阅状态更新用户角色
    if (user.role !== 'admin' && user.role !== 'banned') {
      if (status === 'active') {
        // 活跃订阅，设置为订阅用户
        user.role = 'subscriber';
      } else if (status === 'expired' || status === 'cancelled') {
        // 订阅过期或取消，降级为普通用户
        user.role = 'normal';
      }
    }

    await user.save();

    res.json({
      success: true,
      message: '用户订阅已更新',
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        subscription: user.subscription
      }
    });
  } catch (error) {
    console.error('更新用户订阅错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  adminLogin,
  getUsers,
  updateUserRole,
  banUser,
  unbanUser,
  createUser,
  deleteUser,
  updateUserSubscription
};