import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import { getAllFeedbacks, updateFeedbackStatus } from '../../api/adminFeedback';
import { Button, Select } from '../../components/admin/ui';
import { useToast } from '../../components/ui/use-toast';

interface Feedback {
  _id: string;
  userId: string;
  userEmail: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
  updatedAt: string;
}

const AdminFeedback: React.FC = () => {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [search, setSearch] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  // 获取反馈列表
  const fetchFeedbacks = async () => {
    try {
      setIsLoading(true);
      // 如果状态筛选是"all"，则传递空字符串给API
      const status = statusFilter === 'all' ? '' : statusFilter;
      const response = await getAllFeedbacks(page, 10, status, search);
      setFeedbacks(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      toast({
        title: '获取反馈列表失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载和筛选条件变化时重新获取数据
  useEffect(() => {
    fetchFeedbacks();
  }, [page, statusFilter]);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // 重置页码
    fetchFeedbacks();
  };

  // 处理状态筛选变化
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setPage(1); // 重置页码
  };

  // 查看反馈详情
  const handleViewDetail = (id: string) => {
    navigate(`/admin/feedback/${id}`);
  };

  // 更新反馈状态
  const handleUpdateStatus = async (id: string, status: 'pending' | 'processing' | 'replied') => {
    try {
      await updateFeedbackStatus(id, status);
      // 更新本地状态
      setFeedbacks(prev =>
        prev.map(feedback =>
          feedback._id === id ? { ...feedback, status } : feedback
        )
      );
      toast({
        title: '状态更新成功',
        variant: 'default'
      });
    } catch (error) {
      console.error('更新反馈状态失败:', error);
      toast({
        title: '更新状态失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive'
      });
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 渲染状态标签
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-md bg-yellow-100 text-yellow-800">未处理</span>;
      case 'processing':
        return <span className="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800">处理中</span>;
      case 'replied':
        return <span className="px-2 py-1 text-xs rounded-md bg-green-100 text-green-800">已回复</span>;
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">用户反馈管理</h1>
          <p className="text-gray-600 mt-1">查看和处理用户提交的反馈</p>
        </div>

        <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6 mb-6">
          <div className="md:flex md:justify-between mb-4">
            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="mb-4 md:mb-0">
              <div className="flex">
                <input
                  type="text"
                  placeholder="搜索邮箱或标题..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  搜索
                </button>
              </div>
            </form>

            {/* 状态筛选 */}
            <div className="w-full md:w-48">
              <Select
                value={statusFilter}
                onChange={(e) => handleStatusChange(e.target.value)}
                options={[
                  { value: 'all', label: '全部状态' },
                  { value: 'pending', label: '未处理' },
                  { value: 'processing', label: '处理中' },
                  { value: 'replied', label: '已回复' }
                ]}
                placeholder="状态筛选"
              />
            </div>
          </div>

          {/* 反馈列表 */}
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : feedbacks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无反馈记录
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {feedbacks.map((feedback) => (
                    <tr key={feedback._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{feedback.userEmail}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{feedback.title}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatDate(feedback.createdAt)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {renderStatusBadge(feedback.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetail(feedback._id)}
                          >
                            查看
                          </Button>
                          {feedback.status !== 'processing' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateStatus(feedback._id, 'processing')}
                            >
                              标记处理中
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页控制 */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <Button
                variant="outline"
                onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                disabled={page === 1 || isLoading}
              >
                上一页
              </Button>
              <span className="text-sm text-gray-500">
                第 {page} 页 / 共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={page === totalPages || isLoading}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminFeedback;
