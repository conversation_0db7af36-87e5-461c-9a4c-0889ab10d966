import React, { useState, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import useAdminStore from '../../../store/useAdminStore';
import AdminLayout from '../../../components/admin/AdminLayout';
import { Button, Input, Card } from '../../../components/admin/ui';

const UserSettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    userSettings: {
      enableRegistration: true,
      inviteCodeRequired: false,
      defaultRole: 'trial' as 'trial' | 'normal',
      passwordMinLength: 6,
      passwordRequireSpecialChar: false,
      allowedEmailDomains: [] as string[],
      trialDays: 7,
      inviteRewardDays: 30
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();
      setFormData({
        userSettings: response.settings.userSettings
      });
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取系统设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';
    const isNumber = type === 'number';

    // 处理嵌套属性 (如 userSettings.enableRegistration)
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section as keyof typeof prev],
          [field]: isCheckbox
            ? (e.target as HTMLInputElement).checked
            : isNumber
              ? Number(value)
              : value
        }
      }));
    } else {
      // 处理普通属性
      setFormData(prev => ({
        ...prev,
        [name]: isCheckbox
          ? (e.target as HTMLInputElement).checked
          : isNumber
            ? Number(value)
            : value
      }));
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        userSettings: formData.userSettings
      });
      toast({
        title: '用户设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新用户设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">用户设置</h1>
          <p className="text-gray-600 mt-1">管理用户注册和权限相关配置</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 左侧表单 */}
                <div className="space-y-4">
                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <label htmlFor="enableRegistration" className="text-sm font-medium text-gray-700 dark:text-content-secondary">
                        开启注册
                      </label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          id="enableRegistration"
                          name="userSettings.enableRegistration"
                          checked={formData.userSettings.enableRegistration}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      关闭后，新用户将无法注册账号
                    </p>
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <label htmlFor="inviteCodeRequired" className="text-sm font-medium text-gray-700 dark:text-content-secondary">
                        需要邀请码
                      </label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          id="inviteCodeRequired"
                          name="userSettings.inviteCodeRequired"
                          checked={formData.userSettings.inviteCodeRequired}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      开启后，新用户注册需要输入有效的邀请码
                    </p>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="defaultRole" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                      默认用户角色
                    </label>
                    <select
                      id="defaultRole"
                      name="userSettings.defaultRole"
                      value={formData.userSettings.defaultRole}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-background-input dark:text-content-primary"
                    >
                      <option value="trial">试用用户</option>
                      <option value="normal">普通用户</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      新注册用户的默认角色
                    </p>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="passwordMinLength" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                      密码最小长度
                    </label>
                    <input
                      type="number"
                      id="passwordMinLength"
                      name="userSettings.passwordMinLength"
                      value={formData.userSettings.passwordMinLength}
                      onChange={handleChange}
                      min={6}
                      max={32}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-background-input dark:text-content-primary"
                    />
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <label htmlFor="passwordRequireSpecialChar" className="text-sm font-medium text-gray-700 dark:text-content-secondary">
                        密码需要特殊字符
                      </label>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          id="passwordRequireSpecialChar"
                          name="userSettings.passwordRequireSpecialChar"
                          checked={formData.userSettings.passwordRequireSpecialChar}
                          onChange={handleChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      开启后，密码必须包含特殊字符
                    </p>
                  </div>
                </div>

                {/* 右侧表单 */}
                <div className="space-y-4">
                  <div className="mb-4">
                    <label htmlFor="trialDays" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                      试用天数
                    </label>
                    <input
                      type="number"
                      id="trialDays"
                      name="userSettings.trialDays"
                      value={formData.userSettings.trialDays}
                      onChange={handleChange}
                      min={1}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-background-input dark:text-content-primary"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      新用户的试用期天数
                    </p>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="inviteRewardDays" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                      邀请奖励天数
                    </label>
                    <input
                      type="number"
                      id="inviteRewardDays"
                      name="userSettings.inviteRewardDays"
                      value={formData.userSettings.inviteRewardDays}
                      onChange={handleChange}
                      min={1}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-background-input dark:text-content-primary"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      成功邀请新用户后获得的奖励天数
                    </p>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="allowedEmailDomains" className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-1">
                      允许的邮箱域名
                    </label>
                    <input
                      type="text"
                      id="allowedEmailDomains"
                      name="allowedEmailDomains"
                      value={formData.userSettings.allowedEmailDomains.join(',')}
                      onChange={(e) => {
                        const domains = e.target.value.split(',').map(d => d.trim()).filter(Boolean);
                        setFormData(prev => ({
                          ...prev,
                          userSettings: {
                            ...prev.userSettings,
                            allowedEmailDomains: domains
                          }
                        }));
                      }}
                      placeholder="example.com,another.com"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-background-input dark:text-content-primary"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-content-muted">
                      限制注册的邮箱域名，多个域名用逗号分隔。留空表示不限制
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  disabled={isSaving}
                  loading={isSaving}
                >
                  {isSaving ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default UserSettings;
