import SolarTermPredictionLine, { ISolarTermPredictionLine } from '../models/SolarTermPredictionLine';
import binanceService from './binanceService';
import SolarTermConfigService from './solarTermConfigService';
import {
  formatDate,
  isMonthFirstSolarTermDay,
  getMonthFirstSolarTerm,
  getNextMonthFirstSolarTerm
} from '../utils/solarTermUtils';
import logger from '../utils/logger';

/**
 * 预测折线虚拟价格转换器
 * 基于节气日特殊预测重新计算虚拟基准，其他预测使用当前转换比例
 */
class SolarTermVirtualPriceConverter {
  private static readonly DEFAULT_VIRTUAL_BASE = 10000.00; // 首次运行默认基准
  private static readonly FLUCTUATION_RANGE = 0.001;       // ±0.1%波动

  // 当前转换比例缓存：避免频繁数据库查询
  private static currentRatioCache = new Map<string, number>();

  /**
   * 转换预测折线数据为虚拟价格
   * @param realPredictionData 真实预测数据点数组
   * @param symbol 交易对符号
   * @param targetStartTime 预测目标开始时间
   * @param predictionType 预测类型（用于判断是否是节气日特殊预测）
   * @returns 虚拟价格数据点数组
   */
  static async convertPredictionLineData(
    realPredictionData: { time: number, value: number }[],
    symbol: string,
    targetStartTime: number,
    predictionType: string
  ): Promise<{ time: number, value: number }[]> {

    if (realPredictionData.length === 0) return [];

    try {
      let conversionRatio: number;

      // 1. 检查是否是节气日特殊预测
      if (predictionType === 'solar_term_day_start') {
        // 节气日：重新计算虚拟基准和转换比例
        conversionRatio = await this.calculateNewConversionRatio(symbol, targetStartTime, realPredictionData[0].value);

        // 存储新的转换比例到数据库
        await SolarTermConfigService.setCurrentRatio(symbol, conversionRatio);

        // 🔥 关键修复：同时更新内存缓存
        this.currentRatioCache.set(symbol, conversionRatio);

        logger.info(`[转换比例] 节气日更新`, { conversionRatio: conversionRatio.toFixed(6) });

      } else {
        // 常规预测：使用当前转换比例
        const cachedRatio = this.currentRatioCache.get(symbol);

        if (cachedRatio !== undefined) {
          conversionRatio = cachedRatio;
          logger.info(`[转换比例] 使用缓存比例`, { conversionRatio: conversionRatio.toFixed(6) });
        } else {
          const savedRatio = await SolarTermConfigService.getCurrentRatio(symbol);

          if (savedRatio === null) {
            // 首次运行（不是节气日预测）
            const virtualBase = this.DEFAULT_VIRTUAL_BASE;
            const realBase = realPredictionData[0].value;
            conversionRatio = virtualBase / realBase;

            // 存储首次转换比例
            await SolarTermConfigService.setCurrentRatio(symbol, conversionRatio);

            logger.info(`[转换比例] 首次运行`, {
              virtualBase: virtualBase.toFixed(2),
              realBase: realBase.toFixed(2),
              conversionRatio: conversionRatio.toFixed(6)
            });
          } else {
            // 使用已存储的转换比例
            conversionRatio = savedRatio;
            logger.info(`[转换比例] 从数据库恢复比例`, { conversionRatio: conversionRatio.toFixed(6) });
          }

          // 🔥 修复：缓存转换比例
          this.currentRatioCache.set(symbol, conversionRatio);
        }
      }

      // 2. 转换预测数据
      const virtualPredictionData = realPredictionData.map(point => ({
        time: point.time,
        value: Math.round(point.value * conversionRatio * 100) / 100 // 保留2位小数
      }));

      logger.info(`[虚拟价格] 转换完成`, {
        conversionRatio: conversionRatio.toFixed(6),
        pointCount: virtualPredictionData.length,
        realPriceRange: `${realPredictionData[0].value.toFixed(2)} ~ ${realPredictionData[realPredictionData.length-1].value.toFixed(2)}`,
        virtualPriceRange: `${virtualPredictionData[0].value.toFixed(2)} ~ ${virtualPredictionData[virtualPredictionData.length-1].value.toFixed(2)}`
      });

      return virtualPredictionData;

    } catch (error) {
      logger.error('[虚拟价格转换] 转换失败', error);
      throw error; // 停止当前预测生成
    }
  }

  /**
   * 计算新的转换比例（节气日使用）
   * @param symbol 交易对符号
   * @param targetStartTime 预测目标开始时间
   * @param currentRealBase 当前真实基准价格
   * @returns 新的转换比例
   */
  private static async calculateNewConversionRatio(
    symbol: string,
    targetStartTime: number,
    currentRealBase: number
  ): Promise<number> {

    // 查找前一个预测点的虚拟价格
    const lastVirtualPrice = await this.getLastPredictionVirtualPrice(symbol, targetStartTime);

    let virtualBase: number;

    if (lastVirtualPrice === null) {
      // 没有历史预测，使用默认基准
      virtualBase = this.DEFAULT_VIRTUAL_BASE;
      logger.info(`[虚拟基准] 无历史预测，使用默认基准`, { virtualBase: virtualBase.toFixed(2) });
    } else {
      // 基于前一个预测点的虚拟价格，加上小幅波动
      const fluctuation = (Math.random() - 0.5) * this.FLUCTUATION_RANGE;
      virtualBase = Math.round(lastVirtualPrice * (1 + fluctuation) * 100) / 100;

      logger.info(`[虚拟基准] 基于前一预测点`, {
        lastVirtualPrice: lastVirtualPrice.toFixed(2),
        fluctuation: (fluctuation * 100).toFixed(2) + '%',
        virtualBase: virtualBase.toFixed(2)
      });
    }

    // 计算新的转换比例
    const newRatio = virtualBase / currentRealBase;
    return newRatio;
  }

  /**
   * 获取前一个预测点的最后虚拟价格
   * @param symbol 交易对符号
   * @param beforeTime 时间点之前
   * @returns 最后虚拟价格，如果没有返回null
   */
  private static async getLastPredictionVirtualPrice(symbol: string, beforeTime: number): Promise<number | null> {
    try {
      const lastPrediction = await SolarTermPredictionLine.findOne({
        symbol,
        targetStartTime: { $lt: beforeTime }
      }).sort({ targetStartTime: -1 });

      if (lastPrediction && lastPrediction.predictionData.length > 0) {
        // 返回该预测的最后一个虚拟价格点
        const lastPoint = lastPrediction.predictionData[lastPrediction.predictionData.length - 1];
        return lastPoint.value;
      }

      return null;
    } catch (error) {
      logger.error('[虚拟基准] 查询前一预测点失败', error);
      return null;
    }
  }









  /**
   * 清理转换比例缓存
   * 清理内存中的转换比例缓存
   */
  static clearRatioCache(): void {
    this.currentRatioCache.clear();
    logger.info(`[缓存清理] 清理转换比例缓存`);
  }
}

/**
 * 节气预测折线服务类
 * 负责生成和管理基于节气周期的预测折线
 * 新版逻辑：根据每月第一个节气日期生成预测折线，每天23:00预测未来3天
 */
class SolarTermPredictionService {
  // 记录上一次预测使用的数据末尾时间，用于后续预测的连续性参考
  private lastPredictionDataEndTime: Map<string, number> = new Map();

  /**
   * 将UTC时间转换为北京时间（UTC+8）
   * @param date UTC时间
   * @returns 北京时间
   */
  private toBeijingTime(date: Date): Date {
    return new Date(date.getTime() + 8 * 60 * 60 * 1000);
  }

  /**
   * 将北京时间转换为UTC时间
   * @param beijingDate 北京时间
   * @returns UTC时间
   */
  private toUTCTime(beijingDate: Date): Date {
    return new Date(beijingDate.getTime() - 8 * 60 * 60 * 1000);
  }

  /**
   * 判断给定日期是否是预测时间点
   *
   * @param date 日期对象（默认UTC+8北京时间）
   * @returns 如果是预测时间点则返回预测信息，否则返回null
   */
  public isPredictionTimePoint(date: Date): {
    isSolarTermDay: boolean;
    cycle: string;
    predictionHours: number;
    isSolarTermDayStart?: boolean; // 是否为节气日当天的开始预测(00:00)
  } | null {
    // 直接使用传入的日期作为北京时间
    const beijingDate = date;
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1; // JS月份从0开始
    const day = beijingDate.getDate();
    const hour = beijingDate.getHours();
    const minute = beijingDate.getMinutes();

    // 只在整点时进行预测（容错判断：允许59分、0分、1分）
    if (minute !== 0 && minute !== 59 && minute !== 1) return null;

    // 判断是否是当月第一个节气日
    const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

    // 节气日当日的00:00预测
    if (isSolarTermDay && hour === 0) {
      return {
        isSolarTermDay: true,
        cycle: 'solar_term_day_start',
        predictionHours: 24, // 预测未来24小时
        isSolarTermDayStart: true // 标记为节气日开始
      };
    }

    // 获取当前月和前一个月的节气日信息
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    let prevMonthDate = new Date(beijingDate);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    // 确定当前时间所属的节气周期
    let activeSolarTermDate: Date;
    let daysSinceSolarTerm: number;
    let nextMonthSolarTerm: Date | null;

    // 创建当月节气日期对象
    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    // 创建前月节气日期对象
    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    const currentTime = beijingDate.getTime();

    // 判断当前时间属于哪个节气周期
    if (currentSolarTermDate && currentTime >= currentSolarTermDate.getTime()) {
      // 当前时间 >= 当月节气日，属于当月节气周期
      activeSolarTermDate = currentSolarTermDate;
      daysSinceSolarTerm = Math.floor((currentTime - currentSolarTermDate.getTime()) / (24 * 60 * 60 * 1000));
      nextMonthSolarTerm = getNextMonthFirstSolarTerm(date);
    } else if (prevSolarTermDate) {
      // 当前时间 < 当月节气日，属于前月节气周期
      activeSolarTermDate = prevSolarTermDate;
      daysSinceSolarTerm = Math.floor((currentTime - prevSolarTermDate.getTime()) / (24 * 60 * 60 * 1000));
      // 下个节气日就是当月节气日
      nextMonthSolarTerm = currentSolarTermDate;
    } else {
      // 无法确定节气周期
      return null;
    }

    if (!nextMonthSolarTerm) return null;

    // 判断是否是节气日当天的23:00
    if (isSolarTermDay && hour === 23) {
      return {
        isSolarTermDay: true,
        cycle: 'day_0', // 标记为节气日第0天
        predictionHours: 72  // 预测未来72小时（3天）
      };
    }

    // 节气日后每天的23:00都触发预测
    if (daysSinceSolarTerm > 0 && hour === 23) {
      // 计算到下个节气日前一天23:00的剩余小时数
      const nextMonthPrevDay = new Date(nextMonthSolarTerm.getTime() - 24 * 60 * 60 * 1000);
      nextMonthPrevDay.setHours(23, 0, 0, 0);

      const remainingHours = Math.max(0, (nextMonthPrevDay.getTime() - currentTime) / (60 * 60 * 1000));

      // 如果剩余时间小于24小时，则不生成预测
      if (remainingHours < 24) {
        return null;
      }

      // 确定预测时间长度
      let predictionHours = 72; // 标准是72小时（3天）

      // 如果剩余时间不足72小时但大于等于48小时，则预测48小时
      if (remainingHours < 72 && remainingHours >= 48) {
        predictionHours = 48;
      }
      // 如果剩余时间不足48小时但大于等于24小时，则预测24小时
      else if (remainingHours < 48 && remainingHours >= 24) {
        predictionHours = 24;
      }

      return {
        isSolarTermDay: false,
        cycle: `day_${daysSinceSolarTerm}`,
        predictionHours
      };
    }

    return null;
  }

  /**
   * 生成预测折线
   *
   * @param date 当前日期时间（UTC+8北京时间）
   * @param symbol 交易对符号
   * @returns 生成的预测数据，如果当前不是预测时间点则返回null
   */
  public async generatePrediction(date: Date = new Date(Date.now() + 8 * 60 * 60 * 1000), symbol: string = 'BTCUSDT'): Promise<ISolarTermPredictionLine | null> {
    // 使用传入的北京时间
    const beijingDate = date;
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1;

    // 1. 确定预测点所属的节气周期
    // 获取当前月第一个节气日
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    // 获取前一个月的节气
    let prevMonthDate = new Date(beijingDate);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    if (!currentSolarTerm && !prevSolarTerm) {
      logger.error('无法确定当前或前一个节气日');
      return null;
    }

    // 创建节气日日期对象
    let solarTermDayDate: Date;
    let solarTermYear: number;
    let solarTermMonth: number;
    let solarTermDay: number;

    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    // 2. 确定预测点类型
    let predictionPoint;
    if (currentSolarTermDate && beijingDate >= currentSolarTermDate && currentSolarTerm) {
      // 当前月节气周期
      predictionPoint = this.isPredictionTimePoint(date);
      solarTermDayDate = currentSolarTermDate;
      solarTermYear = year;
      solarTermMonth = month;
      solarTermDay = currentSolarTerm.day;
    } else if (prevSolarTermDate && prevSolarTerm) {
      // 前一个月节气周期
        predictionPoint = this.isPredictionTimePoint(date);
      solarTermDayDate = prevSolarTermDate;
      solarTermYear = prevYear;
      solarTermMonth = prevMonth;
      solarTermDay = prevSolarTerm.day;
    } else {
      logger.error('无法确定预测点所属节气周期');
      return null;
    }

    // 如果不是预测时间点，返回null
    if (!predictionPoint) {
      return null;
    }

    // 构建节气日日期字符串
    const solarTermDate = formatDate(solarTermYear, solarTermMonth, solarTermDay);

    // 3. 计算源数据时间和目标预测时间
    let sourceStartTimes: number[] = [];
    let sourceEndTimes: number[] = [];
    let sourceMinuteCounts: number[] = [];
    let targetStartTime: number;
    let targetEndTime: number;

    if (predictionPoint.isSolarTermDayStart) {
      // 节气日当日预测（00:00触发）
      // 数据区间：前一天23:00开始的48条1分钟K线（23:00~23:47）
      // 预测区间：前一天23:00到节气日23:00（24小时）

      // 设置目标时间范围
      const prevDay = new Date(beijingDate);
      prevDay.setDate(prevDay.getDate() - 1);
      prevDay.setHours(23, 0, 0, 0);
      targetStartTime = prevDay.getTime();

      const targetEnd = new Date(beijingDate);
      targetEnd.setHours(23, 0, 0, 0);
      targetEndTime = targetEnd.getTime();

      // 设置源数据时间范围（前一天23:00开始的48分钟数据）
      sourceStartTimes.push(targetStartTime);
      sourceMinuteCounts.push(48);
      sourceEndTimes.push(new Date(sourceStartTimes[0] + (sourceMinuteCounts[0] - 1) * 60 * 1000).getTime());

      logger.info(`节气日预测(00:00)`, {
        startTime: new Date(sourceStartTimes[0]).toISOString(),
        endTime: new Date(sourceEndTimes[0]).toISOString(),
        dataCount: sourceMinuteCounts[0]
      });
    } else if (predictionPoint.isSolarTermDay) {
      // 节气日当天23:00的第一次常规预测（第0天）
      // 设置目标时间范围（预测未来72小时/3天）
      targetStartTime = date.getTime();
      targetEndTime = new Date(targetStartTime + predictionPoint.predictionHours * 60 * 60 * 1000).getTime();

      // 根据预测天数准备多段源数据
      const daysToPredict = predictionPoint.predictionHours / 24;
      const solarTermPrevDay = new Date(solarTermDayDate);
      solarTermPrevDay.setDate(solarTermPrevDay.getDate() - 1);

      for (let day = 0; day < daysToPredict; day++) {
        // 为每一天预测准备一段48分钟数据
        const startTimeBase = new Date(solarTermPrevDay);
        startTimeBase.setHours(23, 48, 0, 0); // 从23:48开始
        const dayOffset = day * 48 * 60 * 1000; // 每天增加48分钟
        const startTime = startTimeBase.getTime() + dayOffset;

        sourceStartTimes.push(startTime);
        sourceMinuteCounts.push(48);
        sourceEndTimes.push(new Date(startTime + (48 - 1) * 60 * 1000).getTime());

        logger.info(`节气日常规预测(23:00)`, {
          day: day + 1,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(sourceEndTimes[day]).toISOString()
        });
      }
    } else {
      // 节气日之后的每天23:00预测
      // 设置目标时间范围（预测未来72小时/3天）
      targetStartTime = date.getTime();
      targetEndTime = new Date(targetStartTime + predictionPoint.predictionHours * 60 * 60 * 1000).getTime();

      // 从预测点的cycle提取天数
      const daysSinceSolarTerm = parseInt(predictionPoint.cycle.split('_')[1]);

      // 计算源数据基准：从节气日前一天23:48开始
      const solarTermPrevDay = new Date(solarTermDayDate);
      solarTermPrevDay.setDate(solarTermPrevDay.getDate() - 1);
      solarTermPrevDay.setHours(23, 48, 0, 0); // 从23:48开始

      // 根据预测天数准备多段源数据
      const daysToPredict = predictionPoint.predictionHours / 24;

      for (let day = 0; day < daysToPredict; day++) {
        // 为每一天预测准备一段48分钟数据
        // 基准时间 + 天数偏移 * 48分钟 + 当前预测天偏移 * 48分钟
        const startTime = solarTermPrevDay.getTime() +
                           daysSinceSolarTerm * 48 * 60 * 1000 +
                           day * 48 * 60 * 1000;

        sourceStartTimes.push(startTime);
        sourceMinuteCounts.push(48);
        sourceEndTimes.push(new Date(startTime + (48 - 1) * 60 * 1000).getTime());

        logger.info(`常规预测`, {
          daysSinceSolarTerm,
          day: day + 1,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(sourceEndTimes[day]).toISOString()
        });
      }
    }

    logger.info(`预测目标时间段`, {
      startTime: new Date(targetStartTime).toISOString(),
      endTime: new Date(targetEndTime).toISOString()
    });

    // 4. 获取源数据
    try {
      // 存储各段源数据
      const allKlineData: any[][] = [];

      // 获取每段1分钟K线数据
      for (let i = 0; i < sourceStartTimes.length; i++) {
        const klineData = await binanceService.fetchMinuteKline(
          symbol,
          sourceStartTimes[i],
          sourceEndTimes[i],
          sourceMinuteCounts[i]
        );

        if (!klineData || klineData.length < sourceMinuteCounts[i]) {
          console.error(`获取第${i+1}段K线数据失败或数据不足，预期${sourceMinuteCounts[i]}条，实际获取${klineData?.length || 0}条`);
        return null;
        }

        allKlineData.push(klineData);
      }

      // 记录最后一条数据的时间，用于连续性检查
      if (sourceEndTimes.length > 0) {
        const symbolKey = `${symbol}_${solarTermDate}`;
        this.lastPredictionDataEndTime.set(symbolKey, sourceEndTimes[sourceEndTimes.length - 1] + 60 * 1000);
      }

      // 5. 生成预测点
      const targetDurationHours = (targetEndTime - targetStartTime) / (60 * 60 * 1000);
      const predictionData = this.generate30MinPredictionPoints(
        allKlineData,
        targetStartTime,
        targetDurationHours
      );

      if (predictionData.length === 0) {
        console.error('[预测折线] 生成真实预测数据点失败');
        return null;
      }

      console.log(`[预测折线] 生成真实预测数据点: ${predictionData.length}个, 价格范围: ${predictionData[0].value.toFixed(2)} ~ ${predictionData[predictionData.length-1].value.toFixed(2)}`);

      // 6. 转换为虚拟价格数据
      let virtualPredictionData: { time: number, value: number }[];
      const predictionCycle = predictionPoint.isSolarTermDayStart ? 'solar_term_day_start' : predictionPoint.cycle;

      try {
        virtualPredictionData = await SolarTermVirtualPriceConverter
          .convertPredictionLineData(predictionData, symbol, targetStartTime, predictionCycle);
      } catch (error) {
        console.error('[预测折线] 虚拟价格转换失败，停止预测生成:', error);
        return null; // 停止当前预测生成
      }

      // 7. 创建并保存预测结果（使用虚拟数据）

      try {
        // 使用简化的重叠处理逻辑
        return await this.savePredictionWithDeduplication(
          symbol,
          targetStartTime,
          targetEndTime,
          virtualPredictionData,
          date.getTime(),
          solarTermDate,
          predictionCycle
        );
      } catch (error) {
        console.error('保存预测数据失败:', error);
        return null;
      }
    } catch (error) {
      console.error('生成预测失败:', error);
      return null;
    }
  }

  /**
   * 精确的重叠处理：只删除重叠的预测点，保留非重叠部分
   *
   * @param symbol 交易对符号
   * @param targetStartTime 预测开始时间
   * @param targetEndTime 预测结束时间
   * @param predictionData 预测数据点
   * @param predictionTime 预测生成时间
   * @param solarTermDate 节气日期
   * @param predictionCycle 预测周期
   * @returns 保存的预测记录
   */
  private async savePredictionWithDeduplication(
    symbol: string,
    targetStartTime: number,
    targetEndTime: number,
    predictionData: { time: number, value: number }[],
    predictionTime: number,
    solarTermDate: string,
    predictionCycle: string
  ): Promise<any> {
    try {
      // 1. 计算新预测的时间范围
      const newTimeRange = {
        start: Math.min(...predictionData.map(p => p.time)),
        end: Math.max(...predictionData.map(p => p.time))
      };

      console.log(`[预测折线] 新预测时间范围: ${new Date(newTimeRange.start).toISOString()} 到 ${new Date(newTimeRange.end).toISOString()}`);

      // 2. 查找时间范围重叠的旧记录
      // 使用更简单的重叠检查：两个时间段重叠当且仅当 max(start1,start2) < min(end1,end2)
      const overlappingRecords = await SolarTermPredictionLine.find({
        symbol,
        // 时间段重叠条件：旧记录结束时间 > 新记录开始时间 AND 旧记录开始时间 < 新记录结束时间
        targetEndTime: { $gt: newTimeRange.start },
        targetStartTime: { $lt: newTimeRange.end }
      });

      // 3. 处理重叠的预测点（精确处理，只删除重叠部分）
      if (overlappingRecords.length > 0) {
        console.log(`[预测折线] 发现 ${overlappingRecords.length} 个重叠记录，开始精确处理预测点`);

        // 创建新预测点的时间集合
        const newTimeSet = new Set(predictionData.map(p => p.time));

        for (const record of overlappingRecords) {
          let hasChanges = false;
          let removedCount = 0;

          // 过滤掉与新预测重叠的时间点
          const filteredPredictionData = record.predictionData.filter((point: any) => {
            const isOverlapping = newTimeSet.has(point.time);
            if (isOverlapping) {
              removedCount++;
              hasChanges = true;
            }
            return !isOverlapping;
          });

          if (hasChanges) {
            if (filteredPredictionData.length === 0) {
              // 如果所有预测点都被移除，删除整个记录
              await SolarTermPredictionLine.deleteOne({ _id: record._id });
              console.log(`  - 删除空记录: ${new Date(record.targetStartTime).toISOString()} 到 ${new Date(record.targetEndTime).toISOString()} (${record.predictionCycle})`);
            } else {
              // 更新记录，移除重叠的预测点
              record.predictionData = filteredPredictionData;

              // 重新计算targetStartTime和targetEndTime
              const times = filteredPredictionData.map((p: any) => p.time);
              const minTime = Math.min(...times);
              const maxTime = Math.max(...times);

              record.targetStartTime = minTime;
              record.targetEndTime = maxTime;

              await record.save();
              logger.info(`更新记录: 移除重叠点`, {
                removedCount,
                remainingPoints: filteredPredictionData.length,
                predictionCycle: record.predictionCycle,
                newTimeRange: `${new Date(minTime).toISOString()} 到 ${new Date(maxTime).toISOString()}`
              });
            }
          }
        }
      }

      // 4. 创建新记录
      const newRecord = new SolarTermPredictionLine({
        symbol,
        predictionTime,
        targetStartTime,
        targetEndTime,
        solarTermDate,
        predictionCycle,
        predictionData,
        isActive: true
      });

      await newRecord.save();
      logger.info(`[预测折线] 创建新记录`, {
        solarTermDate,
        predictionCycle,
        dataPoints: predictionData.length,
        timeRange: `${new Date(targetStartTime).toISOString()} 到 ${new Date(targetEndTime).toISOString()}`
      });

      return newRecord;

    } catch (error) {
      logger.error('[预测折线] 保存预测数据失败', error);
      throw error;
    }
  }

  /**
   * 生成30分钟预测点
   * 根据多段1分钟K线数据生成30分钟预测点
   * 每段K线数据对应预测一天
   *
   * @param klineDataSegments 多段1分钟K线数据数组
   * @param targetStartTime 目标起始时间
   * @param targetDurationHours 目标持续小时数
   * @returns 生成的预测点数组
   */
  private generate30MinPredictionPoints(
    klineDataSegments: any[][],
    targetStartTime: number,
    targetDurationHours: number
  ): { time: number, value: number }[] {
    // 预测点数组
    const predictionPoints: { time: number, value: number }[] = [];

    // 计算每个30分钟预测点
    const totalPoints = targetDurationHours * 2; // 每小时2个30分钟点
    const pointsPerDay = 48; // 一天24小时，每小时2个点，共48个点

    for (let i = 0; i < totalPoints; i++) {
      // 计算当前预测点的时间
      const pointTime = targetStartTime + i * 30 * 60 * 1000;

      // 确定这个预测点属于哪一天
      const dayIndex = Math.floor(i / pointsPerDay);

      // 如果数据段不够用，使用最后一段
      const segmentIndex = Math.min(dayIndex, klineDataSegments.length - 1);
      const klineData = klineDataSegments[segmentIndex];

      // 计算在当天内的点索引
      const pointIndexInDay = i % pointsPerDay;

      // 将当天的48个预测点映射到48条K线数据
      // 每个预测点直接对应一条K线
      const klineIndex = Math.floor(pointIndexInDay * klineData.length / pointsPerDay);

        // 使用收盘价作为预测值
      const predictedValue = parseFloat(klineData[klineIndex][4]);

      predictionPoints.push({
        time: pointTime,
        value: predictedValue
      });
    }

    return predictionPoints;
  }

  /**
   * 计算当前节气周期的时间范围
   * 从当前月的solar_term_day_start到下个月的solar_term_day_start
   * 使用与生成预测折线相同的时间逻辑
   *
   * @param date 当前日期时间（UTC+8北京时间）
   * @returns 节气周期的开始和结束时间戳
   */
  private getCurrentSolarTermCycleRange(date: Date): { startTime: number | null, endTime: number | null } {
    try {
      // 转换为北京时间进行计算
      const beijingTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
      const year = beijingTime.getFullYear();
      const month = beijingTime.getMonth() + 1;

      // 获取当前月和下个月的第一个节气信息
      const currentSolarTerm = getMonthFirstSolarTerm(year, month);

      let nextMonth = month + 1;
      let nextYear = year;
      if (nextMonth > 12) {
        nextMonth = 1;
        nextYear++;
      }
      const nextSolarTerm = getMonthFirstSolarTerm(nextYear, nextMonth);

      if (!currentSolarTerm || !nextSolarTerm) {
        console.error('无法获取节气信息');
        return { startTime: null, endTime: null };
      }

      // 创建当前月第一个节气日期（solar_term_day_start时间点：节气日00:00北京时间）
      const currentSolarTermDate = new Date(year, month - 1, currentSolarTerm.day);
      currentSolarTermDate.setHours(0, 0, 0, 0); // 北京时间00:00

      // 创建下个月第一个节气日期（下个solar_term_day_start时间点：节气日00:00北京时间）
      const nextSolarTermDate = new Date(nextYear, nextMonth - 1, nextSolarTerm.day);
      nextSolarTermDate.setHours(0, 0, 0, 0); // 北京时间00:00

      // 转换为UTC时间戳用于数据库查询
      const currentSolarTermUTC = currentSolarTermDate.getTime() - 8 * 60 * 60 * 1000;
      const nextSolarTermUTC = nextSolarTermDate.getTime() - 8 * 60 * 60 * 1000;

      // 判断当前时间属于哪个节气周期（使用北京时间比较）
      if (beijingTime >= currentSolarTermDate) {
        // 当前时间 >= 当月节气日00:00，属于当月节气周期
        return {
          startTime: currentSolarTermUTC,
          endTime: nextSolarTermUTC
        };
      } else {
        // 当前时间 < 当月节气日00:00，属于上个月节气周期
        let prevMonth = month - 1;
        let prevYear = year;
        if (prevMonth < 1) {
          prevMonth = 12;
          prevYear--;
        }

        const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);
        if (!prevSolarTerm) {
          console.error('无法获取上个月节气信息');
          return { startTime: null, endTime: null };
        }

        const prevSolarTermDate = new Date(prevYear, prevMonth - 1, prevSolarTerm.day);
        prevSolarTermDate.setHours(0, 0, 0, 0); // 北京时间00:00
        const prevSolarTermUTC = prevSolarTermDate.getTime() - 8 * 60 * 60 * 1000;

        return {
          startTime: prevSolarTermUTC,
          endTime: currentSolarTermUTC
        };
      }
    } catch (error) {
      console.error('计算节气周期范围失败:', error);
      return { startTime: null, endTime: null };
    }
  }

  /**
   * 获取指定数量的预测数据点
   * 直接按预测点数量查询，简单高效
   *
   * @param symbol 交易对符号
   * @param endTime 结束时间戳，获取此时间之前的数据
   * @param limit 获取的预测点数量，默认480个
   * @returns 预测数据列表
   */
  public async getPaginatedPredictions(symbol: string = 'BTCUSDT', endTime?: number, limit: number = 480): Promise<any[]> {
    try {
      // 使用MongoDB聚合查询，直接按预测点数量获取并去重
      const pipeline: any[] = [
        // 1. 匹配符号
        { $match: { symbol } },

        // 2. 展开预测数据数组
        { $unwind: '$predictionData' },

        // 3. 按时间戳去重，保留最新的预测值
        {
          $group: {
            _id: '$predictionData.time',  // 按时间戳分组去重
            time: { $first: '$predictionData.time' },
            value: { $last: '$predictionData.value' },  // 保留最后（最新）的预测值
            recordId: { $last: '$_id' },  // 记录来源记录ID（用于调试）
            recordCount: { $sum: 1 }  // 统计重复数量（用于调试）
          }
        },

        // 4. 如果指定了endTime，筛选该时间之前的预测点
        ...(endTime ? [{
          $match: {
            'time': { $lt: endTime }  // 注意：这里用的是去重后的time字段
          }
        }] : []),

        // 5. 按时间倒序排序（最新的在前）
        { $sort: { time: -1 } },

        // 6. 限制数量
        { $limit: limit },

        // 7. 投影需要的字段（移除调试字段）
        {
          $project: {
            time: 1,
            value: 1,
            // 可选：保留调试信息
            // recordId: 1,
            // recordCount: 1
          }
        },

        // 8. 最终按时间正序排序（用于图表显示）
        { $sort: { time: 1 } }
      ];

      const results = await SolarTermPredictionLine.aggregate(pipeline);

      return results;
    } catch (error) {
      console.error('[预测点查询] 获取预测数据失败:', error);
      throw error;
    }
  }



  /**
   * 检查预测数据完整性
   *
   * @param date 当前日期（默认北京时间）
   * @param symbol 交易对符号
   * @returns 检查结果
   */
  public async checkPredictionIntegrity(date: Date = new Date(Date.now() + 8 * 60 * 60 * 1000), symbol: string = 'BTCUSDT'): Promise<{
    success: boolean;
    message: string;
    missed: number;
    generated: number;
    skipped: number;
  }> {
    console.log(`开始检查预测数据完整性: ${date.toISOString()}`);

    // 获取最近的预测时间点
    const recentTimePoints = this.findRecentPredictionTimePoints(date, 10); // 检查最近10天的数据

    let missed = 0;
    let generated = 0;
    let skipped = 0;

    // 检查每个时间点是否存在预测数据
    for (const timePoint of recentTimePoints) {
      const exists = await this.checkPredictionExists(timePoint, symbol);

      if (!exists) {
        console.log(`缺失预测数据: ${timePoint.toISOString()}`);
        missed++;

        // 生成缺失的预测数据
        const prediction = await this.generatePrediction(timePoint, symbol);

          if (prediction) {
          console.log(`补充生成预测数据: ${timePoint.toISOString()}`);
          generated++;
          } else {
          console.error(`补充生成预测数据失败: ${timePoint.toISOString()}`);
        }
      } else {
        console.log(`预测数据已存在: ${timePoint.toISOString()}`);
        skipped++;
      }
    }

      return {
        success: true,
      message: `检查完成。缺失: ${missed}, 生成: ${generated}, 跳过: ${skipped}`,
      missed,
      generated,
      skipped
    };
  }

  /**
   * 检查指定时间点是否存在预测数据
   *
   * @param date 时间点
   * @param symbol 交易对符号
   * @returns 是否存在预测
   */
  private async checkPredictionExists(date: Date, symbol: string): Promise<boolean> {
    // 使用与isPredictionTimePoint相同的逻辑判断是否是预测时间点
    const predictionPoint = this.isPredictionTimePoint(date);

    // 如果不是预测时间点，视为已存在（不需要补齐）
    if (!predictionPoint) {
      return true;
    }

    // 获取时间点所属的节气周期信息
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();

    // 判断是否是当月第一个节气日
    const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

    // 获取当前月和前一个月的节气日信息
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    let prevMonthDate = new Date(date);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    // 确定当前时间所属的节气周期（与isPredictionTimePoint逻辑一致）
    let solarTermYear: number;
    let solarTermMonth: number;
    let solarTermDay: number;
    let targetStartTime: number;

    // 创建当月节气日期对象
    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    // 创建前月节气日期对象
    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    const currentTime = date.getTime();

    // 判断当前时间属于哪个节气周期
    if (currentSolarTermDate && currentTime >= currentSolarTermDate.getTime()) {
      // 当前时间 >= 当月节气日，属于当月节气周期
      solarTermYear = year;
      solarTermMonth = month;
      solarTermDay = currentSolarTerm!.day;
    } else if (prevSolarTermDate) {
      // 当前时间 < 当月节气日，属于前月节气周期
      solarTermYear = prevYear;
      solarTermMonth = prevMonth;
      solarTermDay = prevSolarTerm!.day;
    } else {
      // 无法确定节气周期，视为已存在
      return true;
    }

    // 计算目标时间范围
    if (isSolarTermDay && hour === 0) {
      // 节气日当日00:00特殊预测
      const prevDay = new Date(date);
      prevDay.setDate(prevDay.getDate() - 1);
      prevDay.setHours(23, 0, 0, 0);
      targetStartTime = prevDay.getTime();
    } else {
      // 其他所有预测（节气日23:00和节气日后的23:00）
      targetStartTime = date.getTime();
    }

    // 查询数据库检查是否存在
    try {
      const prediction = await SolarTermPredictionLine.findOne({
        symbol,
        targetStartTime
      });

      return !!prediction;
    } catch (error) {
      console.error('检查预测存在性失败:', error);
      return false;
    }
  }

  /**
   * 查找最近的预测时间点
   * 支持跨节气周期检查，正确识别不同周期的预测时间点
   *
   * @param date 当前日期
   * @param daysToCheck 检查天数
   * @returns 需要检查的预测时间点
   */
  private findRecentPredictionTimePoints(date: Date, daysToCheck: number = 10): Date[] {
    const timePoints: Date[] = [];
    const currentDate = new Date(date);

    console.log(`[完整性检查] 开始查找最近${daysToCheck}天的预测时间点，当前时间: ${currentDate.toISOString()}`);

    // 计算起始检查日期（当前日期往前推N天）
    const startDate = new Date(currentDate);
    startDate.setDate(startDate.getDate() - daysToCheck);
    startDate.setHours(0, 0, 0, 0);

    console.log(`[完整性检查] 检查时间范围: ${startDate.toISOString()} 到 ${currentDate.toISOString()}`);

    // 遍历每一天
    const checkDate = new Date(startDate);
    while (checkDate <= currentDate) {
      const year = checkDate.getFullYear();
      const month = checkDate.getMonth() + 1;
      const day = checkDate.getDate();

      // 判断是否是当月第一个节气日
      const isSolarTermDay = isMonthFirstSolarTermDay(year, month, day);

      // 节气日当天0:00特殊预测点
      if (isSolarTermDay) {
        const specialPoint = new Date(checkDate);
        specialPoint.setHours(0, 0, 0, 0);

        // 只添加已经过去的时间点
        if (specialPoint <= currentDate) {
          timePoints.push(specialPoint);
          console.log(`[完整性检查] 添加节气日特殊预测点: ${specialPoint.toISOString()} (${year}-${month}-${day} 节气日00:00)`);
        }
      }

      // 每天23:00的预测点 - 使用isPredictionTimePoint判断是否有效
      const regularPoint = new Date(checkDate);
      regularPoint.setHours(23, 0, 0, 0);

      // 只添加已经过去的时间点，并且是有效的预测时间点
      if (regularPoint <= currentDate) {
        const predictionPoint = this.isPredictionTimePoint(regularPoint);
        if (predictionPoint) {
          timePoints.push(regularPoint);

          // 确定该预测点属于哪个节气周期
          const cycleInfo = this.determineSolarTermCycle(regularPoint);
          console.log(`[完整性检查] 添加常规预测点: ${regularPoint.toISOString()} (${predictionPoint.cycle}, 属于${cycleInfo.cycleYear}-${cycleInfo.cycleMonth}-${cycleInfo.cycleDay}节气周期)`);
        } else {
          console.log(`[完整性检查] 跳过无效预测点: ${regularPoint.toISOString()} (不在有效预测时间范围内)`);
        }
      }

      // 移到下一天
      checkDate.setDate(checkDate.getDate() + 1);
    }

    console.log(`[完整性检查] 共找到 ${timePoints.length} 个需要检查的预测时间点`);
    return timePoints;
  }

  /**
   * 确定给定时间点属于哪个节气周期
   * 用于跨周期检查时的准确判断
   *
   * @param date 时间点
   * @returns 节气周期信息
   */
  private determineSolarTermCycle(date: Date): {
    cycleYear: number;
    cycleMonth: number;
    cycleDay: number;
    cycleName: string;
  } {
    const beijingDate = date;
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1;

    // 获取当前月和前一个月的节气日信息
    const currentSolarTerm = getMonthFirstSolarTerm(year, month);

    let prevMonthDate = new Date(beijingDate);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);
    const prevYear = prevMonthDate.getFullYear();
    const prevMonth = prevMonthDate.getMonth() + 1;
    const prevSolarTerm = getMonthFirstSolarTerm(prevYear, prevMonth);

    // 创建当月节气日期对象
    const currentSolarTermDate = currentSolarTerm ?
      new Date(year, month - 1, currentSolarTerm.day) : null;
    currentSolarTermDate?.setHours(0, 0, 0, 0);

    // 创建前月节气日期对象
    const prevSolarTermDate = prevSolarTerm ?
      new Date(prevYear, prevMonth - 1, prevSolarTerm.day) : null;
    prevSolarTermDate?.setHours(0, 0, 0, 0);

    const currentTime = beijingDate.getTime();

    // 判断当前时间属于哪个节气周期
    if (currentSolarTermDate && currentTime >= currentSolarTermDate.getTime()) {
      // 当前时间 >= 当月节气日，属于当月节气周期
      return {
        cycleYear: year,
        cycleMonth: month,
        cycleDay: currentSolarTerm!.day,
        cycleName: currentSolarTerm!.name
      };
    } else if (prevSolarTermDate && prevSolarTerm) {
      // 当前时间 < 当月节气日，属于前月节气周期
      return {
        cycleYear: prevYear,
        cycleMonth: prevMonth,
        cycleDay: prevSolarTerm.day,
        cycleName: prevSolarTerm.name
      };
    } else {
      // 无法确定节气周期，返回当前月信息
      return {
        cycleYear: year,
        cycleMonth: month,
        cycleDay: currentSolarTerm?.day || 1,
        cycleName: currentSolarTerm?.name || '未知'
      };
    }
  }

}

export default new SolarTermPredictionService();