import { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import authApi from '../api/auth';
import tokenService from '../api/tokenService';
import useUserStore from '../store/useUserStore';

const INACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000; // 24小时 (毫秒)，给用户更好的体验
const REFRESH_INTERVAL = 25 * 60 * 1000; // 25分钟刷新一次，提前5分钟避免令牌过期

export const useTokenRefresh = (disabled: boolean = false) => {
  const navigate = useNavigate();
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isSessionValidRef = useRef<boolean>(true); // 记录会话是否有效

  // 获取用户状态管理的logout方法和认证状态
  const { logout, isAuthenticated, token } = useUserStore();

  // 清除令牌并跳转到登录页
  const invalidateSession = () => {
    if (!isSessionValidRef.current) {
      return; // 防止重复调用
    }
    
    console.log('用户不活跃超时，会话失效');
    
    // 标记会话已无效
    isSessionValidRef.current = false;
    
    // 清除定时器
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
    
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
    
    // 清除令牌
    tokenService.setToken(null);
    
    // 调用store的登出方法，确保状态一致性
    logout();
    
    // 触发自定义事件
    const event = new CustomEvent('unauthorized', {
      detail: { reason: 'session_expired' }
    });
    window.dispatchEvent(event);
    
    // 重定向到登录页
    navigate('/login', {
      state: {
        reason: 'session_expired',
        message: 'sessionExpiredInactivity'
      }
    });
  };
  
  // 开始不活跃计时器
  const startInactivityTimer = () => {
    // 先清除之前的计时器
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }
    
    // 存储当前时间为最后活跃时间
    localStorage.setItem('lastUserActivity', new Date().getTime().toString());
    
    // 设置新的计时器
    inactivityTimerRef.current = setTimeout(() => {
      // 超时后使会话失效
      if (isSessionValidRef.current) {
        invalidateSession();
      }
    }, INACTIVITY_TIMEOUT);
  };
  
  // 刷新令牌
  const refreshToken = async () => {
    // 首先检查当前token的有效性
    const currentToken = tokenService.getToken();
    if (currentToken && !tokenService.isTokenValid(currentToken)) {
      console.log('定时检查发现token无效，立即刷新');
      // 清除无效token，继续执行刷新流程
      tokenService.setToken(null);
    }

    // 检查当前时间
    const now = new Date().getTime();
    
    // 从 localStorage 获取上次活跃时间
    const lastActivity = localStorage.getItem('lastUserActivity');
    const lastActivityTime = lastActivity ? parseInt(lastActivity) : now;
    
    // 计算不活跃时间
    const inactiveTime = now - lastActivityTime;
    
    // 检查是否超过了不活跃时间限制
    if (inactiveTime >= INACTIVITY_TIMEOUT) {
      console.log(`用户已超过${INACTIVITY_TIMEOUT/3600000}小时不活跃，自动登出`);
      invalidateSession();
      return;
    }
    
    // 如果会话已经无效，不再刷新
    if (!isSessionValidRef.current) {
      return;
    }
    
    try {
      const result = await authApi.refreshAccessToken();
      if (result) {
        // 刷新成功
        console.log('令牌刷新成功');
      } else {
        // 如果刷新失败(返回null)
        console.warn('令牌刷新失败或返回了null');
        invalidateSession();
      }
    } catch (error) {
      console.error('令牌刷新出错:', error);
      invalidateSession();
    }
  };
  
  useEffect(() => {
    // 如果被禁用（管理员路径），跳过所有逻辑
    if (disabled) {
      console.log('Token刷新已禁用（管理员路径），跳过不活跃监测');
      return;
    }

    // 只有在用户已登录且有token时才启动不活跃监测
    if (!isAuthenticated || !token) {
      console.log('用户未登录，跳过不活跃监测');
      // 清理可能存在的定时器
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
        inactivityTimerRef.current = null;
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
      return;
    }

    console.log('用户已登录，启动不活跃监测');

    // 一开始就设置会话有效
    isSessionValidRef.current = true;

    // 定义用户活跃事件
    const activityEvents = [
      'mousedown', 'keydown', 'scroll', 'touchstart', 'click', 'mousemove'
    ];

    // 用户活跃处理函数
    const handleUserActivity = () => {
      if (isSessionValidRef.current) {
        // 更新最后活跃时间
        localStorage.setItem('lastUserActivity', new Date().getTime().toString());
        startInactivityTimer();
      }
    };

    // 添加事件监听
    activityEvents.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    // 初始化定时器
    startInactivityTimer();

    // 刷新令牌的定时器
    refreshIntervalRef.current = setInterval(() => {
      if (isSessionValidRef.current) {
        refreshToken();
      }
    }, REFRESH_INTERVAL);

    // 清理函数
    return () => {
      // 移除事件监听
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });

      // 清除定时器
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }

      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [disabled, navigate, isAuthenticated, token, logout]);
  
  return null;
};
