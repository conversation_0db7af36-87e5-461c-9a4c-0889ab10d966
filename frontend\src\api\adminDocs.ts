// 使用统一的管理员API客户端
import { adminApiInstance } from './admin';
import axios from 'axios';

export interface AdminDocument {
  _id: string;
  title: string;
  content: string;
  category: 'guide' | 'faq' | 'announcement';
  language: 'zh' | 'en';
  isVisible: boolean;
  order: number;
  originalId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDocumentData {
  title: string;
  content: string;
  category: 'guide' | 'faq' | 'announcement';
  language: 'zh' | 'en';
  isVisible?: boolean;
  order?: number;
}

export interface UpdateDocumentData {
  title?: string;
  content?: string;
  category?: 'guide' | 'faq' | 'announcement';
  language?: 'zh' | 'en';
  isVisible?: boolean;
  order?: number;
}

/**
 * 获取管理员文档列表
 */
export const getAdminDocuments = async (params?: {
  category?: string;
  language?: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  try {
    const response = await adminApiInstance.get('/admin/docs', { params });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取文档列表失败');
    }
    throw error;
  }
};

/**
 * 获取文档详情（管理员）
 */
export const getAdminDocumentById = async (id: string) => {
  try {
    const response = await adminApiInstance.get(`/admin/docs/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取文档详情失败');
    }
    throw error;
  }
};

/**
 * 创建文档
 */
export const createDocument = async (data: CreateDocumentData) => {
  try {
    const response = await adminApiInstance.post('/admin/docs', data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '创建文档失败');
    }
    throw error;
  }
};

/**
 * 更新文档
 */
export const updateDocument = async (id: string, data: UpdateDocumentData) => {
  try {
    const response = await adminApiInstance.put(`/admin/docs/${id}`, data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新文档失败');
    }
    throw error;
  }
};

/**
 * 删除文档
 */
export const deleteDocument = async (id: string) => {
  try {
    const response = await adminApiInstance.delete(`/admin/docs/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '删除文档失败');
    }
    throw error;
  }
};

/**
 * 翻译文档
 */
export const translateDocument = async (id: string, targetLanguage: 'zh' | 'en') => {
  try {
    const response = await adminApiInstance.post(`/admin/docs/${id}/translate`, {
      targetLanguage
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '翻译文档失败');
    }
    throw error;
  }
};

/**
 * 上传文档图片
 */
export const uploadDocumentImage = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('image', file);

    // 对于文件上传，使用adminApiInstance但覆盖Content-Type
    const response = await adminApiInstance.post('/admin/docs/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '上传图片失败');
    }
    throw error;
  }
};

export default {
  getAdminDocuments,
  getAdminDocumentById,
  createDocument,
  updateDocument,
  deleteDocument,
  translateDocument,
  uploadDocumentImage
};
