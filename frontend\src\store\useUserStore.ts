import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../api/auth';
import tokenService from '../api/tokenService';
import { User, UserState } from '../types/user';

// 使用类型断言确保类型安全
const useUserStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      isInitializing: true,

      // 登录
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await api.login(email, password);

          // 设置到内存中
          if (response.token) {
            tokenService.setToken(response.token);
          }

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '登录失败，请稍后重试';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 登出
      logout: () => {
        // 清除内存中的token
        tokenService.setToken(null);

        // 触发自定义事件，通知其他标签页会话已过期
        tokenService.notifySessionExpired();

        // 更新状态
        set({
          user: null,
          token: null,
          isAuthenticated: false
        });
      },

      // 注册
      register: async (email: string, password: string, inviteCode?: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.register(email, password, inviteCode);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '注册失败，请稍后重试';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // verifyEmail方法已移除，现在使用验证码验证方式

      // 忘记密码
      forgotPassword: async (email: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.forgotPassword(email);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '发送重置密码邮件失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 重置密码
      resetPassword: async (token: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });
          await api.resetPassword(token, newPassword);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '重置密码失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 修改密码
      changePassword: async (oldPassword: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });
          const response = await api.changePassword(oldPassword, newPassword);
          set({ isLoading: false });

          // 不在这里清除认证状态，让页面组件显示成功卡片后再清除
          // 只返回响应，让页面组件控制后续逻辑
          return response;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '修改密码失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 获取用户信息
      fetchUserInfo: async () => {
        try {
          set({ isLoading: true, error: null });

          // 尝试从内存或localStorage加载token
          let token = tokenService.getToken() || (() => {
            const state = get() as UserState;
            if (state.token) {
              tokenService.setToken(state.token);
              return state.token;
            }
            return null;
          })();

          // 检查token有效性
          if (token && !tokenService.isTokenValid(token)) {
            console.log('检测到无效token，清除并依赖刷新机制');
            tokenService.setToken(null);
            set({ token: null }); // 同时清除store中的token
            token = null;
          }

          // 如果没有有效token，直接设置为未认证状态，不调用API
          if (!token) {
            console.log('没有有效token，设置为未认证状态');
            set({
              isLoading: false,
              isAuthenticated: false,
              user: null,
              token: null,
              isInitializing: false
            });
            return;
          }

          // 只有在有token时才调用API
          try {
            const userData = await api.getMe();
            set({
              user: userData,
              isAuthenticated: true,
              isLoading: false,
              isInitializing: false
            });
          } catch (error) {
            // API调用失败，让拦截器处理刷新
            console.error('获取用户信息失败:', error);
            tokenService.setToken(null);
            set({
              isLoading: false,
              isAuthenticated: false,
              user: null,
              token: null,
              isInitializing: false
            });
          }
        } catch (error) {
          // 处理其他可能的错误
          console.error('获取用户信息过程中出现错误:', error);
          tokenService.setToken(null);
          set({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null,
            isInitializing: false
          });
        }
      },



      // 发送验证码
      sendVerificationCode: async (email: string, purpose: 'email-verification' | 'password-reset') => {
        try {
          set({ isLoading: true, error: null });
          await api.sendVerificationCode(email, purpose);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '发送验证码失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 验证验证码
      verifyCode: async (email: string, code: string, purpose: 'email-verification' | 'password-reset') => {
        try {
          set({ isLoading: true, error: null });
          await api.verifyCode(email, code, purpose);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : '验证码验证失败';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'user-storage', // localStorage 中的键名
      partialize: (state) => ({
        // 使用类型断言确保类型安全
        token: (state as UserState).token,
        user: (state as UserState).user
      }),
    }
  )
);

export default useUserStore;