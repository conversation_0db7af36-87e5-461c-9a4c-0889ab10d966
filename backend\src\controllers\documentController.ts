import { Request, Response } from 'express';
import Document from '../models/Document';
import translationService from '../services/translationService';

/**
 * 获取文档列表
 * GET /api/docs
 */
export const getDocuments = async (req: Request, res: Response) => {
  try {
    const { category, language = 'zh', page = 1, limit = 50 } = req.query;
    const user = req.user;

    // 构建查询条件
    const query: any = {
      language: language as string
    };

    // 如果指定了分类，添加分类筛选
    if (category && category !== 'all') {
      query.category = category;
    }

    // 权限控制：未登录用户只能看到公开文档
    if (!user) {
      query.isVisible = true;
    } else {
      // 登录用户可以看到所有公开文档
      query.isVisible = true;
    }

    // 分页参数
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    // 查询文档列表
    const documents = await Document.find(query)
      .select('_id title content category language isVisible order createdAt updatedAt')
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // 获取总数
    const total = await Document.countDocuments(query);

    res.json({
      success: true,
      data: {
        documents,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });
  } catch (error) {
    console.error('获取文档列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取文档列表失败'
    });
  }
};

/**
 * 获取文档详情
 * GET /api/docs/:id
 */
export const getDocumentById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const document = await Document.findById(id);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: '文档不存在'
      });
    }

    // 权限检查：未登录用户只能查看公开文档
    if (!user && !document.isVisible) {
      return res.status(403).json({
        success: false,
        message: '此文档需要登录后查看'
      });
    }

    res.json({
      success: true,
      data: document
    });
  } catch (error) {
    console.error('获取文档详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取文档详情失败'
    });
  }
};

/**
 * 创建文档（管理员）
 * POST /api/admin/docs
 */
export const createDocument = async (req: Request, res: Response) => {
  try {
    const { title, content, category, language, isVisible = true, order = 0 } = req.body;

    // 验证必要字段
    if (!title || !content || !category || !language) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的文档信息'
      });
    }

    // 创建文档
    const document = new Document({
      title,
      content,
      category,
      language,
      isVisible,
      order
    });

    await document.save();

    res.status(201).json({
      success: true,
      message: '文档创建成功',
      data: document
    });
  } catch (error) {
    console.error('创建文档失败:', error);
    res.status(500).json({
      success: false,
      message: '创建文档失败'
    });
  }
};

/**
 * 更新文档（管理员）
 * PUT /api/admin/docs/:id
 */
export const updateDocument = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { title, content, category, language, isVisible, order } = req.body;

    const document = await Document.findById(id);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: '文档不存在'
      });
    }

    // 更新字段
    if (title !== undefined) document.title = title;
    if (content !== undefined) document.content = content;
    if (category !== undefined) document.category = category;
    if (language !== undefined) document.language = language;
    if (isVisible !== undefined) document.isVisible = isVisible;
    if (order !== undefined) document.order = order;

    await document.save();

    res.json({
      success: true,
      message: '文档更新成功',
      data: document
    });
  } catch (error) {
    console.error('更新文档失败:', error);
    res.status(500).json({
      success: false,
      message: '更新文档失败'
    });
  }
};

/**
 * 删除文档（管理员）
 * DELETE /api/admin/docs/:id
 */
export const deleteDocument = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const document = await Document.findById(id);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: '文档不存在'
      });
    }

    await Document.findByIdAndDelete(id);

    res.json({
      success: true,
      message: '文档删除成功'
    });
  } catch (error) {
    console.error('删除文档失败:', error);
    res.status(500).json({
      success: false,
      message: '删除文档失败'
    });
  }
};

/**
 * 获取管理员文档列表（包含非公开文档）
 * GET /api/admin/docs
 */
export const getAdminDocuments = async (req: Request, res: Response) => {
  try {
    const { category, language, search, page = 1, limit = 20 } = req.query;

    // 构建查询条件
    const query: any = {};

    if (category && category !== 'all') {
      query.category = category;
    }

    if (language && language !== 'all') {
      query.language = language;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } }
      ];
    }

    // 分页参数
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    // 查询文档列表
    const documents = await Document.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // 获取总数
    const total = await Document.countDocuments(query);

    res.json({
      success: true,
      data: {
        documents,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });
  } catch (error) {
    console.error('获取管理员文档列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取文档列表失败'
    });
  }
};

/**
 * 翻译文档（管理员）
 * POST /api/admin/docs/:id/translate
 */
export const translateDocument = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { targetLanguage } = req.body;

    if (!targetLanguage || !['zh', 'en'].includes(targetLanguage)) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的目标语言'
      });
    }

    const originalDocument = await Document.findById(id);

    if (!originalDocument) {
      return res.status(404).json({
        success: false,
        message: '原文档不存在'
      });
    }

    if (originalDocument.language === targetLanguage) {
      return res.status(400).json({
        success: false,
        message: '目标语言与原文档语言相同'
      });
    }

    // 检查是否已存在翻译版本
    const existingTranslation = await Document.findOne({
      originalId: originalDocument._id,
      language: targetLanguage
    });

    if (existingTranslation) {
      return res.status(400).json({
        success: false,
        message: '该文档的翻译版本已存在'
      });
    }

    // 检查翻译服务是否可用
    if (!(await translationService.isAvailable())) {
      return res.status(503).json({
        success: false,
        message: '翻译服务暂时不可用，请检查翻译设置和API配置'
      });
    }

    // 翻译标题和内容
    const translatedTitle = await translationService.translateText(
      originalDocument.title,
      targetLanguage,
      originalDocument.language
    );

    const translatedContent = await translationService.translateMarkdown(
      originalDocument.content,
      targetLanguage,
      originalDocument.language
    );

    // 创建翻译文档
    const translatedDocument = new Document({
      title: translatedTitle,
      content: translatedContent,
      category: originalDocument.category,
      language: targetLanguage,
      isVisible: originalDocument.isVisible,
      order: originalDocument.order,
      originalId: originalDocument._id
    });

    await translatedDocument.save();

    res.status(201).json({
      success: true,
      message: '文档翻译成功',
      data: translatedDocument
    });
  } catch (error) {
    console.error('翻译文档失败:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '翻译文档失败'
    });
  }
};

export default {
  getDocuments,
  getDocumentById,
  createDocument,
  updateDocument,
  deleteDocument,
  getAdminDocuments,
  translateDocument
};
