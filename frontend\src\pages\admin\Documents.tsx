import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  getAdminDocuments, 
  deleteDocument, 
  translateDocument,
  AdminDocument 
} from '../../api/adminDocs';
import { useToast } from '../../components/ui/use-toast';
import { Button, Input, Select } from '../../components/admin/ui';
import AdminLayout from '../../components/admin/AdminLayout';
import {
  FileText,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Languages,
  Filter
} from 'lucide-react';

const Documents: React.FC = () => {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<AdminDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [languageFilter, setLanguageFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 加载文档列表
  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      const response = await getAdminDocuments({
        search: searchTerm || undefined,
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
        language: languageFilter !== 'all' ? languageFilter : undefined,
        page: currentPage,
        limit: 20
      });

      if (response.success) {
        setDocuments(response.data.documents);
        setTotalPages(response.data.pagination.pages);
      }
    } catch (error) {
      console.error('加载文档列表失败:', error);
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '加载文档列表失败',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDocuments();
  }, [searchTerm, categoryFilter, languageFilter, currentPage]);

  // 删除文档
  const handleDelete = async (id: string, title: string) => {
    if (!window.confirm(`确定要删除文档"${title}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await deleteDocument(id);
      toast({
        title: '成功',
        description: '文档删除成功'
      });
      loadDocuments();
    } catch (error) {
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '删除文档失败',
        variant: 'destructive'
      });
    }
  };

  // 翻译文档
  const handleTranslate = async (id: string, currentLang: string, title: string) => {
    const targetLang = currentLang === 'zh' ? 'en' : 'zh';
    const targetLangName = targetLang === 'zh' ? '中文' : '英文';

    if (!window.confirm(`确定要将文档"${title}"翻译为${targetLangName}吗？`)) {
      return;
    }

    try {
      await translateDocument(id, targetLang);
      toast({
        title: '成功',
        description: `文档翻译为${targetLangName}成功`
      });
      loadDocuments();
    } catch (error) {
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '翻译文档失败',
        variant: 'destructive'
      });
    }
  };

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'guide':
        return '使用指南';
      case 'faq':
        return '常见问题';
      case 'announcement':
        return '公告';
      default:
        return category;
    }
  };

  // 获取语言名称
  const getLanguageName = (language: string) => {
    return language === 'zh' ? '中文' : 'English';
  };

  return (
    <AdminLayout>
      <div className="p-6">
        {/* 页面标题和操作 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">文档管理</h1>
            <p className="text-gray-600 mt-1">管理网站文档内容</p>
          </div>
          <Link to="/admin/documents/create">
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              创建文档
            </Button>
          </Link>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 搜索 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="搜索文档标题或内容..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* 分类筛选 */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                options={[
                  { value: 'all', label: '所有分类' },
                  { value: 'guide', label: '使用指南' },
                  { value: 'faq', label: '常见问题' },
                  { value: 'announcement', label: '公告' }
                ]}
                className="pl-10"
              />
            </div>

            {/* 语言筛选 */}
            <div className="relative">
              <Languages className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
              <Select
                value={languageFilter}
                onChange={(e) => setLanguageFilter(e.target.value)}
                options={[
                  { value: 'all', label: '所有语言' },
                  { value: 'zh', label: '中文' },
                  { value: 'en', label: 'English' }
                ]}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* 文档列表 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">加载中...</p>
            </div>
          ) : documents.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">暂无文档</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      标题
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      分类
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      语言
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      更新时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {documents.map((doc) => (
                    <tr key={doc._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {doc.title}
                            </div>
                            <div className="text-sm text-gray-500">
                              排序: {doc.order}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getCategoryName(doc.category)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {getLanguageName(doc.language)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          doc.isVisible 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {doc.isVisible ? (
                            <>
                              <Eye className="h-3 w-3 mr-1" />
                              公开
                            </>
                          ) : (
                            <>
                              <EyeOff className="h-3 w-3 mr-1" />
                              隐藏
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(doc.updatedAt).toLocaleDateString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Link
                            to={`/admin/documents/edit/${doc._id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleTranslate(doc._id, doc.language, doc.title)}
                            className="text-green-600 hover:text-green-900"
                            title="翻译文档"
                          >
                            <Languages className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(doc._id, doc.title)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="px-6 py-3 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  第 {currentPage} 页，共 {totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Documents;
