import React from 'react';
import { Link, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Document } from '../../api/docs';
import { FileText, HelpCircle, Megaphone } from 'lucide-react';

interface DocsSidebarProps {
  documents: Document[];
  isLoading: boolean;
  selectedDocumentId?: string;
  currentCategory: 'guide' | 'faq' | 'announcement';
}

const DocsSidebar: React.FC<DocsSidebarProps> = ({ documents, isLoading, selectedDocumentId, currentCategory }) => {
  const { t } = useTranslation();
  const { id } = useParams();

  // 使用传入的 selectedDocumentId 或 URL 参数中的 id
  const currentDocumentId = selectedDocumentId || id;

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'guide':
        return <FileText className="h-4 w-4" />;
      case 'faq':
        return <HelpCircle className="h-4 w-4" />;
      case 'announcement':
        return <Megaphone className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'guide':
        return t('docs.categories.guide', '使用指南');
      case 'faq':
        return t('docs.categories.faq', '常见问题');
      case 'announcement':
        return t('docs.categories.announcement', '公告');
      default:
        return category;
    }
  };

  // 获取当前分类的路由前缀
  const getCategoryPath = (category: string) => {
    switch (category) {
      case 'guide':
        return '/docs/article';
      case 'faq':
        return '/faq/article';
      case 'announcement':
        return '/announcement/article';
      default:
        return '/docs/article';
    }
  };

  // 按order字段排序文档
  const sortedDocuments = [...documents].sort((a, b) => a.order - b.order);

  if (isLoading) {
    return (
      <div className="w-80 bg-white border-r border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
        </div>
        <div className="p-6 space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="animate-pulse bg-gray-100 h-8 w-full rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200">
      {/* 分类标题 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          {getCategoryIcon(currentCategory)}
          <h2 className="text-lg font-semibold text-gray-900">
            {getCategoryName(currentCategory)}
          </h2>
        </div>
      </div>

      {/* 文档列表 - 支持滚动，最大高度限制在一屏内 */}
      <div className="max-h-[calc(100vh-200px)] overflow-y-auto">
        <div className="p-6">
          {sortedDocuments.length > 0 ? (
            <div className="space-y-1">
              {sortedDocuments.map((doc) => (
                <Link
                  key={doc._id}
                  to={`${getCategoryPath(currentCategory)}/${doc._id}`}
                  className={`block px-3 py-2 text-sm rounded-md transition-colors text-left ${
                    currentDocumentId === doc._id
                      ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  {doc.title}
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-sm">
                {t('docs.noDocuments', '暂无文档')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocsSidebar;
