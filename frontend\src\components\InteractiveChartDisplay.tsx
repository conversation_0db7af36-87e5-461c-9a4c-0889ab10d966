"use client"

import { useEffect, useState, useMemo } from "react"
import { Card } from "./ui/card"
import { TrendingUp, BarChart3 } from "lucide-react"

export function InteractiveChartDisplay() {
  const [activeChart, setActiveChart] = useState(0)
  const [time, setTime] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setTime((prev) => prev + 0.02)
    }, 50)
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveChart((prev) => (prev + 1) % 2)
    }, 15000) // 15秒切换一次
    return () => clearInterval(interval)
  }, [])

  const charts = [
    {
      id: "trend-analysis",
      title: "Trend Analysis",
      subtitle: "Real-time market predictions",
      icon: TrendingUp,
      component: <TrendChart time={time} />,
    },
    {
      id: "candlestick",
      title: "Trading Signals",
      subtitle: "Advanced pattern recognition",
      icon: BarChart3,
      component: <CandlestickChart time={time} />,
    },
  ]

  const ActiveChartIcon = charts[activeChart].icon
  const ActiveChartTitle = charts[activeChart].title

  return (
    <div className="relative w-full h-[500px]">
      {/* Chart Container */}
      <Card className="w-full h-full bg-cyber-card/20 backdrop-blur-xl border border-cyber-border/50 rounded-2xl overflow-hidden relative">
        {/* Header */}
        <div className="absolute top-6 left-6 right-6 z-10">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <ActiveChartIcon className="h-5 w-5 text-cyber-cyan" />
              <h3 className="text-lg font-bold text-cyber-text font-mono">{ActiveChartTitle}</h3>
            </div>
            <p className="text-sm text-cyber-muted font-mono">{charts[activeChart].subtitle}</p>
          </div>
        </div>

        {/* Chart Content */}
        <div className="absolute inset-0 pt-20">{charts[activeChart].component}</div>

        {/* Chart Indicators */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {charts.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveChart(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === activeChart
                  ? "bg-cyber-cyan shadow-[0_0_8px_rgba(0,245,255,0.6)]"
                  : "bg-cyber-border/50 hover:bg-cyber-border"
              }`}
            />
          ))}
        </div>
      </Card>
    </div>
  )
}

// Trend Chart Component
function TrendChart({ time }: { time: number }) {
  // 使用 useMemo 生成固定的预测线数据
  const predictionLineData = useMemo(() => {
    const points = []
    for (let i = 0; i < 100; i++) {
      const x = i * 5
      const y = 300 + Math.sin(i * 0.08) * 40 + Math.cos(i * 0.06) * 25
      points.push({ x, y })
    }
    return points
  }, [])

  // 计算实时数据线的进度
  const progress = ((time * 50) % 400) / 4

  // 生成实时数据线，跟随预测线轨迹
  const realDataLine = useMemo(() => {
    const currentLength = Math.min(Math.floor(progress), predictionLineData.length)
    return predictionLineData.slice(0, currentLength).map((point, index) => ({
      x: point.x,
      y: point.y - 100 + Math.sin(index * 0.1) * 30,
    }))
  }, [progress, predictionLineData])

  const generatePath = (points: { x: number; y: number }[]) => {
    if (points.length === 0) return ""
    let path = `M ${points[0].x} ${points[0].y}`
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`
    }
    return path
  }

  // 生成面积图路径
  const generateAreaPath = (points: { x: number; y: number }[]) => {
    if (points.length === 0) return ""
    let path = `M 0 400`
    path += ` L ${points[0].x} ${points[0].y}`
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`
    }
    path += ` L 500 400 Z`
    return path
  }

  const predictionPath = generatePath(predictionLineData)
  const predictionAreaPath = generateAreaPath(predictionLineData)
  const realDataPath = generatePath(realDataLine)

  const currentEndPoint = realDataLine[realDataLine.length - 1]

  return (
    <div className="w-full h-full relative">
      {/* Grid Background */}
      <svg className="absolute inset-0 w-full h-full opacity-20">
        <defs>
          <pattern id="trend-grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(0, 245, 255, 0.3)" strokeWidth="1" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#trend-grid)" />
      </svg>

      {/* Chart */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 500 400">
        <defs>
          {/* 紫色渐变 - 预测区域 */}
          <linearGradient id="predictionGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgba(181, 55, 242, 0.25)" />
            <stop offset="100%" stopColor="rgba(181, 55, 242, 0)" />
          </linearGradient>

          {/* 发光滤镜 */}
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* 预测面积图 - 紫色 */}
        <path d={predictionAreaPath} fill="url(#predictionGradient)" />

        {/* 预测线 - 紫色 */}
        <path
          d={predictionPath}
          fill="none"
          stroke="#b537f2"
          strokeWidth="3"
          opacity="0.8"
          style={{ filter: "drop-shadow(0 0 4px rgba(181, 55, 242, 0.4))" }}
        />

        {/* 实时数据线 - 青色 */}
        <path
          d={realDataPath}
          fill="none"
          stroke="#00f5ff"
          strokeWidth="3"
          style={{ filter: "drop-shadow(0 0 6px rgba(0, 245, 255, 0.5))" }}
        />

        {/* 实时数据终点圆点 */}
        {currentEndPoint && (
          <circle
            cx={currentEndPoint.x}
            cy={currentEndPoint.y}
            r="5"
            fill="#00f5ff"
            className="animate-pulse"
            style={{
              filter: "drop-shadow(0 0 12px rgba(0, 245, 255, 0.8))",
            }}
          />
        )}

        {/* 数据点标记 */}
        {realDataLine.map((point, index) => {
          if (index % 10 === 0 && index > 0) {
            return <circle key={index} cx={point.x} cy={point.y} r="2" fill="#00f5ff" opacity="0.6" />
          }
          return null
        })}
      </svg>

      {/* 图例 */}
      <div className="absolute top-4 right-4 space-y-2 text-xs font-mono bg-cyber-card/40 backdrop-blur-md rounded-lg p-3 border border-cyber-border/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-0.5 bg-purple-500 opacity-80"></div>
          <span className="text-cyber-muted">AI PREDICTION</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-0.5 bg-cyber-cyan shadow-sm shadow-cyber-cyan/50"></div>
          <span className="text-cyber-text">REAL DATA</span>
        </div>
      </div>
    </div>
  )
}

// Candlestick Chart Component
function CandlestickChart({ time }: { time: number }) {
  // 使用 useMemo 来稳定数据，避免抖动
  const chartData = useMemo(() => {
    const candles = []
    const volumes = []

    // 生成稳定的基础数据
    for (let i = 0; i < 30; i++) {
      const basePrice = 200
      const trend = Math.sin(i * 0.2) * 30
      const volatility = Math.sin(i * 0.5) * 15

      const open = basePrice + trend + volatility
      const closeChange = (Math.sin(i * 0.7) - 0.2) * 25
      const close = open + closeChange
      const high = Math.max(open, close) + Math.abs(Math.sin(i * 0.4)) * 15
      const low = Math.min(open, close) - Math.abs(Math.cos(i * 0.6)) * 12
      const volume = Math.abs(Math.sin(i * 0.8)) * 60 + 20

      const isGreen = close > open

      candles.push({
        open,
        high,
        low,
        close,
        x: i * 15 + 20,
        isGreen,
      })

      volumes.push({
        volume,
        x: i * 15 + 20,
        isGreen,
      })
    }

    return { candles, volumes }
  }, [])

  const animationOffset = Math.sin(time * 0.5) * 2

  return (
    <div className="w-full h-full relative">
      {/* Grid Background */}
      <svg className="absolute inset-0 w-full h-full opacity-20">
        <defs>
          <pattern id="candle-grid" width="30" height="30" patternUnits="userSpaceOnUse">
            <path d="M 30 0 L 0 0 0 30" fill="none" stroke="rgba(0, 245, 255, 0.3)" strokeWidth="1" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#candle-grid)" />
      </svg>

      {/* Chart */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 500 400">
        {/* Volume bars */}
        {chartData.volumes.map((vol, index) => {
          const isLastBar = index === chartData.volumes.length - 1
          return (
            <rect
              key={`vol-${index}`}
              x={vol.x - 4}
              y={350 - vol.volume + animationOffset}
              width="8"
              height={vol.volume}
              fill={vol.isGreen ? "rgba(0, 255, 136, 0.4)" : "rgba(255, 68, 68, 0.4)"}
              stroke={vol.isGreen ? "rgba(0, 255, 136, 0.6)" : "rgba(255, 68, 68, 0.6)"}
              strokeWidth="1"
              opacity={isLastBar ? Math.abs(Math.sin(time * 4)) * 0.6 + 0.4 : 1}
              style={
                isLastBar
                  ? {
                      filter: `drop-shadow(0 0 8px ${vol.isGreen ? "#00ff88" : "#ff4444"})`,
                    }
                  : {}
              }
            />
          )
        })}

        {/* Candlesticks */}
        {chartData.candles.slice(0, -1).map((candle, index) => {
          const bodyTop = Math.max(candle.open, candle.close)
          const bodyBottom = Math.min(candle.open, candle.close)
          const bodyHeight = Math.abs(candle.close - candle.open)

          return (
            <g key={`candle-${index}`}>
              {/* Wick */}
              <line
                x1={candle.x}
                y1={candle.high + animationOffset}
                x2={candle.x}
                y2={candle.low + animationOffset}
                stroke={candle.isGreen ? "#00ff88" : "#ff4444"}
                strokeWidth="1"
              />

              {/* Body */}
              <rect
                x={candle.x - 4}
                y={bodyTop + animationOffset}
                width="8"
                height={Math.max(bodyHeight, 2)}
                fill={candle.isGreen ? "#00ff88" : "#ff4444"}
                opacity="0.8"
                stroke={candle.isGreen ? "#00ff88" : "#ff4444"}
                strokeWidth="1"
              />
            </g>
          )
        })}
      </svg>
    </div>
  )
}
