import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { getUserFeedbacks } from '../../api/feedback';
import { Button } from '../../components/ui/button';
import { useToast } from '../ui/use-toast';
import FeedbackDetail from './FeedbackDetail';

interface Feedback {
  _id: string;
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'replied';
  adminReply?: string;
  replyAt?: string;
  createdAt: string;
}

interface FeedbackListProps {
  refreshTrigger?: number;
}

const FeedbackList: React.FC<FeedbackListProps> = ({ refreshTrigger = 0 }) => {
  const { t, i18n } = useTranslation();
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const { toast } = useToast();

  // 获取反馈列表
  const fetchFeedbacks = async () => {
    try {
      setIsLoading(true);
      const response = await getUserFeedbacks(page, 10);
      setFeedbacks(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      toast({
        title: t('feedback.loadFeedbacksFailed'),
        description: error instanceof Error ? error.message : t('feedback.loadFailedDesc'),
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载和刷新
  useEffect(() => {
    fetchFeedbacks();
  }, [page, refreshTrigger]);

  // 查看反馈详情
  const handleViewDetail = (feedback: Feedback) => {
    setSelectedFeedback(feedback);
  };

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setSelectedFeedback(null);
  };

  // 渲染状态标签
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-purple/20 text-cyber-purple border border-cyber-purple/30 font-mono">{t('feedback.pending')}</span>;
      case 'processing':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-cyan/20 text-cyber-cyan border border-cyber-cyan/30 font-mono">{t('feedback.processing')}</span>;
      case 'replied':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-green/20 text-cyber-green border border-cyber-green/30 font-mono">{t('feedback.replied')}</span>;
      default:
        return null;
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(i18n.language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: i18n.language === 'en'
    });
  };

  return (
    <>
      {/* 反馈记录 */}
      <div className="border border-cyber-cyan/30 rounded-xl p-5 bg-cyber-dark/50 backdrop-blur-sm">
        <h3 className="text-lg font-semibold mb-4 text-cyber-cyan font-mono">{t('feedback.feedbackRecords')}</h3>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyber-cyan"></div>
            <span className="ml-2 text-cyber-muted font-mono">{t('common.loading')}</span>
          </div>
        ) : feedbacks.length === 0 ? (
          <div className="p-4 rounded-xl text-center border border-cyber-cyan/20 bg-cyber-dark/30">
            <p className="text-cyber-muted text-sm font-mono">{t('feedback.noFeedbackRecords')}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-cyber-cyan/10 border-b border-cyber-cyan/20">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('feedback.submitTime')}</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('common.operation')}</th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-cyber-muted whitespace-nowrap font-mono">{t('feedback.status')}</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-cyber-cyan/20">
                {feedbacks.map((feedback) => (
                  <tr key={feedback._id} className="hover:bg-cyber-cyan/5 transition-colors">
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap text-cyber-muted font-mono">
                      {formatDate(feedback.createdAt)}
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetail(feedback)}
                        className="text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
                      >
                        {t('feedback.viewDetails')}
                      </Button>
                    </td>
                    <td className="px-4 py-3 text-left text-sm whitespace-nowrap">
                      {renderStatusBadge(feedback.status)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页控制 */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-4 pt-4 border-t border-cyber-cyan/20">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
              disabled={page === 1 || isLoading}
              className="text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
            >
              {t('feedback.previousPage')}
            </Button>
            <span className="text-sm text-cyber-muted font-mono">
              {t('feedback.pageInfo', { current: page, total: totalPages })}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={page === totalPages || isLoading}
              className="text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
            >
              {t('feedback.nextPage')}
            </Button>
          </div>
        )}
      </div>

      {/* 反馈详情弹窗 */}
      {selectedFeedback && (
        <FeedbackDetail
          feedback={selectedFeedback}
          onClose={handleCloseDetail}
        />
      )}
    </>
  );
};

export default FeedbackList;
