import { Request, Response } from 'express';
import User from '../models/User';
import InviteCode from '../models/InviteCode';
import { generateToken, generateAccessToken, generateRefreshToken, verifyRefreshToken } from '../middlewares/authMiddleware';
import { sendPasswordResetEmail, verifyPasswordResetToken, checkPasswordResetToken, sendVerificationCodeEmail } from '../utils/sendVerificationEmail';
import verificationCodeService from '../services/verificationCodeService';

import { t, createSuccessResponse, createErrorResponse } from '../utils/i18nHelper';


import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { AuthErrorCode } from '../types/errors';
import systemSettingsService from '../services/systemSettingsService';
import inviteRewardService from '../services/inviteRewardService';
import configService from '../services/configService';
import notificationService from '../services/notificationService';


/**
 * 注册新用户
 */
export const register = async (req: Request, res: Response) => {
  try {
    const { email, password, inviteCode } = req.body;

    // 获取用户语言偏好
    const userLanguage = (req.headers['accept-language']?.includes('en') ? 'en' : 'zh') as 'zh' | 'en';

    // 验证必要字段
    if (!email || !password) {
      return res.status(400).json(createErrorResponse(req, 'auth.email_password_required'));
    }

    // 检查是否允许注册
    const isRegistrationEnabled = await systemSettingsService.isRegistrationEnabled();
    if (!isRegistrationEnabled) {
      return res.status(403).json(createErrorResponse(req, 'auth.registration_disabled'));
    }

    // 检查邮箱是否已注册
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json(createErrorResponse(req, 'auth.email_already_registered'));
    }

    // 检查密码复杂度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (password.length < passwordMinLength) {
      const language = req.headers['accept-language']?.includes('en') ? 'en' : 'zh';
      return res.status(400).json(createErrorResponse(req, 'auth.password_too_short', undefined, { min: passwordMinLength }));
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return res.status(400).json(createErrorResponse(req, 'auth.password_require_special_char'));
    }

    // 检查邮箱域名限制
    const allowedEmailDomains = await systemSettingsService.getAllowedEmailDomains();
    if (allowedEmailDomains.length > 0) {
      const emailDomain = email.split('@')[1];
      if (!allowedEmailDomains.includes(emailDomain)) {
        return res.status(400).json(createErrorResponse(req, 'auth.email_domain_not_allowed'));
      }
    }

    // 获取注册 IP
    const registeredIp = req.ip;

    // 检查同一 IP 的注册数量限制为 1
    const ipRegistrationsCount = await User.countDocuments({ registeredIp });
    if (ipRegistrationsCount >= 1) {
      return res.status(403).json(createErrorResponse(req, 'auth.ip_registration_limit_exceeded'));
    }
    // 验证邀请码
    let invitedBy: mongoose.Types.ObjectId | undefined;

    // 从系统设置中获取是否需要邀请码
    const inviteCodeRequired = await systemSettingsService.isInviteCodeRequired();

    if (inviteCode) {
      const inviteCodeDoc = await InviteCode.findOne({ code: inviteCode, usedBy: null });

      if (!inviteCodeDoc) {
        return res.status(400).json(createErrorResponse(req, 'auth.invalid_invite_code'));
      }

      invitedBy = inviteCodeDoc.createdBy;
    } else if (inviteCodeRequired) {
      return res.status(400).json(createErrorResponse(req, 'auth.invalid_invite_code'));
    }

    // 从系统设置中获取默认试用天数
    const trialDays = await systemSettingsService.getDefaultTrialDays();

    // 计算试用期结束时间
    const trialEndsAt = new Date();
    trialEndsAt.setDate(trialEndsAt.getDate() + trialDays);

    // 从邮箱生成用户名，确保唯一性
    // 提取邮箱前缀作为用户名基础
    const emailPrefix = email.split('@')[0];
    // 添加随机字符串确保唯一性
    const uniqueUsername = `${emailPrefix}_${uuidv4().substring(0, 8)}`;

    // 从系统设置中获取默认注册角色
    const defaultRole = await systemSettingsService.getDefaultRole();

    // 创建用户
    const newUser = new User({
      email,
      username: uniqueUsername, // 设置唯一用户名
      password,
      role: defaultRole,
      trialEndsAt,
      isVerified: false,
      registeredIp: registeredIp || req.ip || 'unknown',
      invitedBy,
      createdAt: new Date()
    });

    await newUser.save();

    // 如果有使用邀请码，更新邀请码状态
    if (inviteCode) {
      await InviteCode.findOneAndUpdate(
        { code: inviteCode },
        {
          usedBy: newUser._id,
          usedAt: new Date()
        }
      );
    }

    // 为用户生成5个邀请码
    await InviteCode.generateCodesForUser(newUser._id, 5);

    // 发送验证码邮件
    const codeResult = await verificationCodeService.createVerificationCode(
      email,
      'email-verification',
      (newUser._id as mongoose.Types.ObjectId).toString()
    );

    let emailSent = false;
    if (codeResult.success) {
      // 从请求头获取用户语言偏好，默认为中文
      const userLanguage = (req.headers['accept-language']?.includes('en') ? 'en' : 'zh') as 'zh' | 'en';
      emailSent = await sendVerificationCodeEmail(email, codeResult.code!, 'email-verification', userLanguage);
    }

    // 创建注册成功获得试用通知
    try {
      await notificationService.createTrialGrantedNotification(newUser._id, trialDays);
    } catch (error) {
      console.error('创建试用通知失败:', error);
      // 不影响注册流程，仅记录错误
    }

    if (!emailSent) {
      console.error('验证码邮件发送失败，但用户已创建');
      // 即使邮件发送失败，也返回成功，用户可以稍后重新发送验证码
    }

    return res.status(201).json(createSuccessResponse(req, 'auth.registration_success', {
      email: email,
      needVerification: true,
      codeExpireMinutes: 10
    }));
  } catch (error) {
    console.error('注册失败:', error);
    return res.status(500).json(createErrorResponse(req, 'common.server_error'));
  }
};

/**
 * 用户登录
 */
export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;



    // 验证必要字段
    if (!email || !password) {
      return res.status(400).json(createErrorResponse(req, 'auth.email_password_required'));
    }

    // 查找用户
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(401).json(createErrorResponse(req, 'auth.email_or_password_error'));
    }

    // 检查账号是否被锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      // 计算剩余锁定时间（分钟）
      const remainingMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / (60 * 1000));

      return res.status(403).json(createErrorResponse(req, 'auth.account_locked_with_time', undefined, { minutes: remainingMinutes }));
    }

    // 验证密码
    const isPasswordValid = await user.comparePassword(password);

    // 从配置服务获取登录失败限制次数和锁定时间
    const loginFailLimit = await configService.get('securitySettings.loginFailLimit', 5);
    const loginLockTime = await configService.get('securitySettings.loginLockTime', 2);

    if (!isPasswordValid) {
      // 增加登录失败计数
      user.loginFailCount = (user.loginFailCount || 0) + 1;

      // 如果达到失败限制次数，锁定账号
      if (user.loginFailCount >= loginFailLimit) {
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + loginLockTime); // 锁定指定小时
        user.lockedUntil = lockUntil;

        await user.save();

        return res.status(403).json(createErrorResponse(req, 'auth.account_locked', undefined, { hours: loginLockTime }));
      }

      await user.save();

      return res.status(401).json(createErrorResponse(req, 'auth.email_or_password_error_with_attempts', undefined, {
        attempts: loginFailLimit - user.loginFailCount
      }));
    }

    // 检查用户是否被封禁
    if (user.role === 'banned') {
      return res.status(403).json(createErrorResponse(req, 'auth.user_banned'));
    }

    // 检查邮箱是否已验证
    if (!user.isVerified) {
      return res.status(403).json(createErrorResponse(req, 'auth.email_not_verified'));
    }

    // 登录成功，重置登录失败计数和锁定时间
    user.loginFailCount = 0;
    user.lockedUntil = undefined;

    // 更新最后登录时间、最后活跃时间和在线状态
    user.lastLoginAt = new Date();
    user.lastActiveAt = new Date(); // 设置最后活跃时间
    user.isOnline = true;
    await user.save();

    const userId = (user._id as mongoose.Types.ObjectId).toString();

    // 生成访问令牌和刷新令牌
    const accessToken = generateAccessToken(userId);
    const refreshToken = generateRefreshToken(userId);

    // 设置刷新令牌到 HttpOnly Cookie，有效期为3天，与JWT一致
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // 在生产环境中使用 HTTPS
      sameSite: 'lax',  // 改为lax模式以允许跨站请求带上Cookie
      path: '/',        // 确保路径设置正确
      maxAge: 3 * 24 * 60 * 60 * 1000 // 3天有效期，与JWT刷新令牌一致
    });

    // 去除密码字段再返回用户信息
    const userResponse = {
      _id: user._id,
      email: user.email,
      role: user.role,
      trialEndsAt: user.trialEndsAt,
      isVerified: user.isVerified,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      lastActiveAt: user.lastActiveAt,
      isOnline: user.isOnline,
      invitedBy: user.invitedBy,
      subscription: user.subscription,
      rewardHistory: user.rewardHistory
    };

    return res.status(200).json({
      success: true,
      message: t(req, 'auth.login_success'),
      token: accessToken, // 为了向后兼容，保留 token 字段名
      accessToken, // 同时提供新字段名
      user: userResponse
    });
  } catch (error) {
    console.error('登录失败:', error);
    return res.status(500).json(createErrorResponse(req, 'common.server_error'));
  }
};

/**
 * 验证邮箱 (兼容新旧两种方式)
 */
export const verifyEmail = async (req: Request, res: Response) => {
  try {
    const { email, code } = req.body;

    // 新方式：使用验证码验证
    if (email && code) {
      // 验证验证码格式
      if (!/^\d{6}$/.test(code)) {
        return res.status(400).json(createErrorResponse(req, 'auth.code_format_error'));
      }

      // 验证验证码
      const verifyResult = await verificationCodeService.verifyCode(email, code, 'email-verification');

      if (!verifyResult.success) {
        return res.status(400).json({
          success: false,
          message: verifyResult.message,
          remainingAttempts: verifyResult.remainingAttempts
        });
      }

      // 更新用户验证状态
      const user = await User.findOneAndUpdate(
        { email },
        { isVerified: true },
        { new: true }
      );

      if (!user) {
        return res.status(404).json(createErrorResponse(req, 'auth.user_not_found'));
      }

      // 如果用户是通过邀请注册的，处理邀请奖励
      if (user.invitedBy) {
        inviteRewardService.processInviteReward(user.invitedBy)
          .catch(err => console.error('处理邀请奖励失败:', err));
      }

      return res.status(200).json(createSuccessResponse(req, 'auth.email_verification_success'));
    }

    // 如果没有提供验证码
    return res.status(400).json(createErrorResponse(req, 'auth.code_format_error'));
  } catch (error) {
    console.error('邮箱验证失败:', error);
    return res.status(500).json(createErrorResponse(req, 'common.server_error'));
  }
};

/**
 * 忘记密码 - 发送重置密码邮件
 */
export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json(createErrorResponse(req, 'auth.email_required'));
    }

    // 查找用户
    const user = await User.findOne({ email });

    // 即使用户不存在，也返回成功消息，避免泄露用户信息
    if (!user) {
      return res.status(200).json(createSuccessResponse(req, 'auth.password_reset_email_sent'));
    }

    // 发送重置密码邮件
    // 从请求头获取用户语言偏好，默认为中文
    const userLanguage = (req.headers['accept-language']?.includes('en') ? 'en' : 'zh') as 'zh' | 'en';
    await sendPasswordResetEmail(email, (user._id as mongoose.Types.ObjectId).toString(), userLanguage);

    return res.status(200).json(createSuccessResponse(req, 'auth.password_reset_email_sent'));
  } catch (error) {
    console.error('忘记密码处理失败:', error);
    return res.status(500).json(createErrorResponse(req, 'common.server_error'));
  }
};

/**
 * 验证密码重置token（仅验证，不使用）
 */
export const validateResetToken = async (req: Request, res: Response) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json(createErrorResponse(req, 'auth.token_required'));
    }

    // 验证令牌（仅验证，不标记为已使用）
    const decoded = await checkPasswordResetToken(token);

    if (!decoded) {
      return res.status(400).json(createErrorResponse(req, 'auth.token_invalid'));
    }

    return res.status(200).json(createSuccessResponse(req, 'auth.token_valid'));
  } catch (error) {
    console.error('验证重置令牌失败:', error);
    return res.status(500).json(createErrorResponse(req, 'common.server_error'));
  }
};

/**
 * 重置密码
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json(createErrorResponse(req, 'auth.token_and_password_required'));
    }

    // 密码长度验证 - 使用系统设置中的密码最小长度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (newPassword.length < passwordMinLength) {
      return res.status(400).json(createErrorResponse(req, 'auth.password_too_short', undefined, { min: passwordMinLength }));
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message: '密码必须包含至少一个特殊字符'
      });
    }

    // 验证令牌（异步验证，包含一次性使用检查）
    const decoded = await verifyPasswordResetToken(token);

    if (!decoded) {
      return res.status(400).json({
        success: false,
        message: '重置链接已失效、已使用或不正确'
      });
    }

    // 查找用户并更新密码
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新密码
    user.password = newPassword;
    await user.save(); // 密码会在save前自动哈希

    return res.status(200).json(createSuccessResponse(req, 'auth.password_reset_success'));
  } catch (error) {
    console.error('重置密码失败:', error);
    return res.status(500).json({
      success: false,
      message: t(req, 'common.server_error')
    });
  }
};

/**
 * 获取当前用户信息
 */
export const getMe = async (req: Request, res: Response) => {
  try {
    // req.user 由认证中间件设置
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    // 排除密码等敏感字段
    const userResponse = {
      _id: user._id,
      email: user.email,
      role: user.role,
      trialEndsAt: user.trialEndsAt,
      isVerified: user.isVerified,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      lastActiveAt: user.lastActiveAt,
      isOnline: user.isOnline,
      invitedBy: user.invitedBy,
      subscription: user.subscription,
      rewardHistory: user.rewardHistory
    };

    return res.status(200).json({
      success: true,
      user: userResponse
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 刷新访问令牌
 */
export const refresh = async (req: Request, res: Response) => {
  try {
    // 从 Cookie 中获取刷新令牌
    const refreshToken = req.cookies.refreshToken;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: t(req, 'auth.refresh_token_missing'),
        code: AuthErrorCode.REFRESH_TOKEN_MISSING
      });
    }

    // 验证刷新令牌
    const decoded = verifyRefreshToken(refreshToken);

    if (!decoded) {
      // 清除无效的刷新令牌 Cookie
      res.clearCookie('refreshToken');

      return res.status(401).json({
        success: false,
        message: t(req, 'auth.refresh_token_invalid'),
        code: AuthErrorCode.REFRESH_TOKEN_INVALID
      });
    }

    // 查找用户
    const user = await User.findById(decoded.userId);

    if (!user) {
      res.clearCookie('refreshToken');

      return res.status(401).json({
        success: false,
        message: t(req, 'auth.user_not_found'),
        code: AuthErrorCode.USER_NOT_FOUND
      });
    }

    // 检查用户是否被封禁
    if (user.role === 'banned') {
      res.clearCookie('refreshToken');

      return res.status(403).json({
        success: false,
        message: t(req, 'auth.user_banned'),
        code: AuthErrorCode.USER_BANNED
      });
    }

    // 更新用户最后活跃时间
    user.lastActiveAt = new Date();
    await user.save();

    // 生成新的访问令牌
    const accessToken = generateAccessToken(decoded.userId);

    // 生成新的刷新令牌（令牌轮换）
    const newRefreshToken = generateRefreshToken(decoded.userId);

    // 设置 Cookie，每次刷新都重置为3天过期时间，与JWT一致
    res.cookie('refreshToken', newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // 在生产环境中使用 HTTPS
      sameSite: 'lax',  // 改为lax模式以允许跨站请求带上Cookie
      path: '/',        // 确保路径设置正确
      maxAge: 3 * 24 * 60 * 60 * 1000 // 3天有效期，与JWT刷新令牌一致
    });

    return res.status(200).json({
      success: true,
      accessToken
    });
  } catch (error) {
    console.error('刷新令牌失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试',
      code: 'common/server-error'
    });
  }
};

/**
 * 修改密码（需要认证）
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = (req as any).user.id; // 从认证中间件获取用户ID

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供当前密码和新密码'
      });
    }

    // 密码长度验证 - 使用系统设置中的密码最小长度
    const passwordMinLength = await systemSettingsService.getPasswordMinLength();
    if (newPassword.length < passwordMinLength) {
      return res.status(400).json(createErrorResponse(req, 'auth.new_password_too_short', undefined, { min: passwordMinLength }));
    }

    // 检查是否需要特殊字符
    const passwordRequireSpecialChar = await systemSettingsService.isPasswordRequireSpecialChar();
    if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message: '新密码必须包含至少一个特殊字符'
      });
    }

    // 查找用户
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证当前密码
    const isCurrentPasswordValid = await user.comparePassword(oldPassword);

    if (!isCurrentPasswordValid) {
      return res.status(400).json(createErrorResponse(req, 'auth.current_password_incorrect'));
    }

    // 更新密码
    user.password = newPassword;
    await user.save(); // 密码会在save前自动哈希

    // 清除刷新令牌Cookie，强制用户重新登录
    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    });

    return res.status(200).json({
      success: true,
      message: t(req, 'auth.password_change_success'),
      requireReauth: true // 标识需要重新认证
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 发送验证码
 */
export const sendVerificationCode = async (req: Request, res: Response) => {
  try {
    const { email, purpose } = req.body;

    // 验证必要字段
    if (!email || !purpose) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱地址和验证用途'
      });
    }

    // 验证用途是否有效
    if (!['email-verification', 'password-reset'].includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: '无效的验证用途'
      });
    }

    // 如果是邮箱验证，检查用户是否存在且未验证
    if (purpose === 'email-verification') {
      const user = await User.findOne({ email });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }
      if (user.isVerified) {
        return res.status(400).json({
          success: false,
          message: '邮箱已验证，无需重复验证'
        });
      }
    }

    // 如果是密码重置，检查用户是否存在
    if (purpose === 'password-reset') {
      const user = await User.findOne({ email });
      if (!user) {
        // 为了安全，即使用户不存在也返回成功消息
        return res.status(200).json({
          success: true,
          message: '如果邮箱已注册，验证码将发送到该邮箱'
        });
      }
    }

    // 创建验证码
    const result = await verificationCodeService.createVerificationCode(email, purpose);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }

    // 发送验证码邮件
    // 从请求头获取用户语言偏好，默认为中文
    const userLanguage = (req.headers['accept-language']?.includes('en') ? 'en' : 'zh') as 'zh' | 'en';
    const emailSent = await sendVerificationCodeEmail(email, result.code!, purpose, userLanguage);

    if (!emailSent) {
      return res.status(500).json({
        success: false,
        message: '验证码邮件发送失败，请稍后重试'
      });
    }

    return res.status(200).json({
      success: true,
      message: '验证码已发送到您的邮箱',
      data: {
        email,
        expiresAt: result.expiresAt
      }
    });
  } catch (error) {
    console.error('发送验证码失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 验证验证码
 */
export const verifyCode = async (req: Request, res: Response) => {
  try {
    const { email, code, purpose } = req.body;

    // 验证必要字段
    if (!email || !code || !purpose) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱、验证码和验证用途'
      });
    }

    // 验证用途是否有效
    if (!['email-verification', 'password-reset'].includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: '无效的验证用途'
      });
    }

    // 验证验证码格式
    if (!/^\d{6}$/.test(code)) {
      return res.status(400).json({
        success: false,
        message: '验证码格式错误，请输入6位数字'
      });
    }

    // 验证验证码
    const verifyResult = await verificationCodeService.verifyCode(email, code, purpose);

    if (!verifyResult.success) {
      return res.status(400).json({
        success: false,
        message: verifyResult.message,
        remainingAttempts: verifyResult.remainingAttempts
      });
    }

    // 如果是邮箱验证，更新用户验证状态
    if (purpose === 'email-verification') {
      const user = await User.findOneAndUpdate(
        { email },
        { isVerified: true },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 如果用户是通过邀请注册的，处理邀请奖励
      if (user.invitedBy) {
        inviteRewardService.processInviteReward(user.invitedBy)
          .catch(err => console.error('处理邀请奖励失败:', err));
      }
    }

    return res.status(200).json({
      success: true,
      message: '验证码验证成功',
      data: {
        email,
        purpose
      }
    });
  } catch (error) {
    console.error('验证验证码失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  register,
  login,
  verifyEmail,
  forgotPassword,
  validateResetToken,
  resetPassword,
  getMe,
  refresh,
  changePassword,
  sendVerificationCode,
  verifyCode
};