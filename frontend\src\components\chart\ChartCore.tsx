/**
 * 图表核心组件
 * 
 * 负责创建和管理图表实例，设置基本配置，以及提供图表实例给子组件
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { createChart, IChartApi } from 'lightweight-charts';
import { getDefaultChartOptions } from '../../utils/chartUtils';
import { chartManager } from '../../utils/chartSeriesManager';

interface ChartCoreProps {
  containerClassName?: string;
  onChartReady?: (chart: IChartApi) => void;
  children?: React.ReactNode;
  height?: number;
  chartManager?: typeof chartManager; // 添加图表管理器属性
  watermarkText?: string; // 水印文本
}

/**
 * 图表核心组件
 * 负责创建和管理图表实例的基础组件
 */
const ChartCore: React.FC<ChartCoreProps> = ({
  containerClassName = 'w-full h-[740px]',
  height = 740,
  onChartReady,
  children,
  chartManager: chartManagerProp, // 使用传入的图表管理器
  watermarkText // 水印文本
}) => {
  // 图表容器引用
  const chartContainerRef = useRef<HTMLDivElement>(null);
  // 图表实例引用
  const chartRef = useRef<IChartApi | null>(null);
  // 图表是否就绪状态
  const [isChartReady, setIsChartReady] = useState<boolean>(false);
  // 存储原始尺寸
  const originalSizeRef = useRef<{width: number, height: number} | null>(null);
  // 全屏状态
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  // 存储原始可见范围
  const visibleRangeRef = useRef<{from: number, to: number} | null>(null);

  // 窗口大小变化处理函数
  const handleResize = useCallback(() => {
    if (chartContainerRef.current && chartRef.current) {
      try {
        // 使用requestAnimationFrame确保在重绘周期内执行
        requestAnimationFrame(() => {
          const newWidth = chartContainerRef.current?.clientWidth || 0;
          const newHeight = chartContainerRef.current?.clientHeight || 0;
          
          // 检查是否在移动设备上
          const isMobileView = window.innerWidth < 640;
          
          // 如果有图表管理器，则使用它调整大小
          if (chartManagerProp) {
            chartManagerProp.resize(newWidth, newHeight);
            
            // 在移动设备上，调整时间刻度配置以优化显示
            if (isMobileView) {
              chartRef.current?.timeScale().applyOptions({
                barSpacing: 4, // 减小K线间距
                rightOffset: 8, // 减小右侧边距
                tickMarkMaxCharacterLength: 5, // 减小标签长度
              });
            } else {
              // 恢复桌面版设置
              chartRef.current?.timeScale().applyOptions({
                barSpacing: 6,
                rightOffset: 50,
                tickMarkMaxCharacterLength: 12,
              });
            }
          } else {
            chartRef.current?.applyOptions({ 
              width: newWidth,
              height: newHeight
            });
          }
          
        });
      } catch (error) {
        console.error('调整图表大小时出错:', error);
      }
    }
  }, [chartManagerProp]);

  // 创建图表实例
  useEffect(() => {
    // 如果已存在图表或容器不存在，则返回
    if (!chartContainerRef.current || chartRef.current) return;
    
    console.log('初始化图表核心...');
    
    // 获取容器宽度和高度
    const containerWidth = chartContainerRef.current.clientWidth;
    const containerHeight = chartContainerRef.current.clientHeight || height;
    
    // 保存原始尺寸
    originalSizeRef.current = {
      width: containerWidth,
      height: containerHeight
    };
    
    console.log('记录原始尺寸:', originalSizeRef.current);
    
    let chart: IChartApi;
    
    console.log('ChartCore 初始化图表，水印文本:', watermarkText);

    // 如果提供了图表管理器，则使用它初始化图表
    if (chartManagerProp) {
      chart = chartManagerProp.initChart(chartContainerRef.current, containerWidth, containerHeight, watermarkText);
    } else {
      // 原有的创建方式作为备选
      const options = getDefaultChartOptions(containerWidth, watermarkText);
      options.height = containerHeight; // 使用传入的高度或默认值
      console.log('图表配置选项:', options);
      chart = createChart(chartContainerRef.current, options);
    }
    
    // 保存图表引用
    chartRef.current = chart;
    
    // 设置图表就绪状态
    setIsChartReady(true);
    
    // 如果提供了onChartReady回调，则调用
    if (onChartReady) {
      onChartReady(chart);
    }
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
    
    // 添加全屏变化监听
    const handleFullscreenChange = () => {
      const newFullscreenState = Boolean(document.fullscreenElement);
      
      // 保存当前可见的逻辑范围，用于恢复视图位置
      if (!newFullscreenState && isFullscreen && chartRef.current) {
        // 退出全屏，尝试恢复原始视图位置
        setTimeout(() => {
          if (chartRef.current && visibleRangeRef.current) {
            console.log('恢复原始视图位置:', visibleRangeRef.current);
            chartRef.current.timeScale().setVisibleLogicalRange(visibleRangeRef.current);
          }
        }, 150);
      }
      
      setIsFullscreen(newFullscreenState);
      
      if (newFullscreenState) {
        // 进入全屏前保存当前视图位置
        if (chartRef.current) {
          const visibleLogicalRange = chartRef.current.timeScale().getVisibleLogicalRange();
          if (visibleLogicalRange) {
            visibleRangeRef.current = visibleLogicalRange;
            console.log('保存当前视图位置:', visibleLogicalRange);
          }
        }
        
        // 进入全屏时立即调整大小以适应全屏
        handleResize();
      } else {
        // 退出全屏时恢复原始尺寸
        if (originalSizeRef.current && chartContainerRef.current) {
          setTimeout(() => {
            if (chartManagerProp && chartRef.current && originalSizeRef.current) {
              // 恢复原始尺寸
              const { width, height } = originalSizeRef.current;
              console.log('恢复原始尺寸:', width, height);
              
              chartManagerProp.resize(width, height);
            }
          }, 100); // 添加短暂延迟确保DOM已更新
        }
      }
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      
      if (chartRef.current) {
        if (chartManagerProp) {
          // 使用图表管理器销毁图表
          chartManagerProp.destroy();
        } else {
          chartRef.current.remove();
        }
        chartRef.current = null;
      }
      
      console.log('图表核心已清理');
    };
  }, [height, handleResize, onChartReady, chartManagerProp]);

  // 退出全屏时的额外处理
  useEffect(() => {
    if (!isFullscreen && originalSizeRef.current && chartRef.current && chartManagerProp) {
      // 确保在退出全屏后恢复原始尺寸
      const origSize = originalSizeRef.current;
      setTimeout(() => {
        console.log('退出全屏后确保恢复原始尺寸:', origSize.width, origSize.height);
        chartManagerProp.resize(origSize.width, origSize.height);
      }, 200);
    }
  }, [isFullscreen, chartManagerProp]);

  // 将图表实例传递给子组件
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { 
        chartApi: chartRef.current,
        isChartReady 
      } as any);
    }
    return child;
  });

  // 渲染图表容器
  return (
    <div className="chart-core-container">
      <div ref={chartContainerRef} className={containerClassName} />
      {isChartReady && chartRef.current && childrenWithProps}
    </div>
  );
};

export default ChartCore; 