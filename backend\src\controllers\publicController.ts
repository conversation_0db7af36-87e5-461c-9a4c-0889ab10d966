import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';
import configService from '../services/configService';

/**
 * 获取公共系统设置
 * GET /api/public/settings
 */
export const getPublicSettings = async (req: Request, res: Response) => {
  try {
    // 获取系统设置（通过缓存服务）
    const settings = await systemSettingsService.getSystemSettings();

    // 只返回公开的设置信息
    res.json({
      success: true,
      settings: {
        siteInfo: {
          siteName: settings.siteInfo.siteName,
          siteDescription: settings.siteInfo.siteDescription,
          siteKeywords: settings.siteInfo.siteKeywords,
          copyright: settings.siteInfo.copyright,
          socialLinks: settings.siteInfo.socialLinks,
          defaultLanguage: settings.siteInfo.defaultLanguage
        },
        subscriptionPrices: settings.subscriptionPrices,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('获取公共系统设置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getPublicSettings
};
