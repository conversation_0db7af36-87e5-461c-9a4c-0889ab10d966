/**
 * 认证错误码枚举
 * 用于提供规范化的错误响应
 */
export enum AuthErrorCode {
  TOKEN_EXPIRED = 'auth/token-expired',        // 令牌已过期
  TOKEN_INVALID = 'auth/token-invalid',        // 令牌无效或格式错误
  TOKEN_MISSING = 'auth/token-missing',        // 未提供令牌
  USER_NOT_FOUND = 'auth/user-not-found',      // 用户不存在
  EMAIL_NOT_VERIFIED = 'auth/email-not-verified', // 邮箱未验证
  INSUFFICIENT_PERMISSIONS = 'auth/insufficient-permissions', // 权限不足
  USER_BANNED = 'auth/user-banned',            // 用户被封禁
  REFRESH_TOKEN_EXPIRED = 'auth/refresh-token-expired', // 刷新令牌过期
  REFRESH_TOKEN_INVALID = 'auth/refresh-token-invalid', // 刷新令牌无效
  REFRESH_TOKEN_MISSING = 'auth/refresh-token-missing'  // 未提供刷新令牌
}

/**
 * 通用错误码枚举
 */
export enum CommonErrorCode {
  VALIDATION_ERROR = 'common/validation-error',   // 表单验证错误
  SERVER_ERROR = 'common/server-error',           // 服务器内部错误
  RESOURCE_NOT_FOUND = 'common/not-found',        // 资源不存在
  DUPLICATE_ENTRY = 'common/duplicate-entry',     // 资源已存在 
  RATE_LIMIT_EXCEEDED = 'common/rate-limit-exceeded' // 请求过于频繁
}

/**
 * 构造标准化错误响应
 */
export interface ErrorResponse {
  success: false;
  message: string;
  code: AuthErrorCode | CommonErrorCode | string;
  details?: any;
} 