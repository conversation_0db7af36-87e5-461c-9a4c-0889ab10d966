import User from '../models/User';
import InviteCode from '../models/InviteCode';
import mongoose from 'mongoose';
import systemSettingsService from './systemSettingsService';
import notificationService from './notificationService';
import logger from '../utils/logger';

/**
 * 检查用户是否达到邀请奖励条件
 * 条件：成功邀请5个用户注册并验证邮箱
 * @param userId 用户ID
 */
export const checkInviteReward = async (userId: mongoose.Types.ObjectId): Promise<boolean> => {
  try {
    // 查询该用户创建的所有邀请码
    const inviteCodes = await InviteCode.find({ createdBy: userId });
    
    // 获取已使用的邀请码ID列表
    const usedInviteCodeIds = inviteCodes
      .filter(code => code.usedBy !== null && code.usedBy !== undefined)
      .map(code => code.usedBy);
    
    // 查询通过这些邀请码注册的用户中已验证邮箱的用户数量
    const verifiedUsersCount = await User.countDocuments({
      _id: { $in: usedInviteCodeIds },
      isVerified: true
    });
    
    // 检查是否达到奖励条件（5个已验证用户）
    return verifiedUsersCount >= 5;
  } catch (error) {
    logger.error('检查邀请奖励条件失败', error);
    return false;
  }
};

/**
 * 给用户发放邀请奖励
 * @param userId 用户ID
 */
export const grantInviteReward = async (userId: mongoose.Types.ObjectId): Promise<boolean> => {
  try {
    // 检查用户是否达到奖励条件
    const isEligible = await checkInviteReward(userId);

    if (!isEligible) {
      return false;
    }

    // 查询用户
    const user = await User.findById(userId);

    if (!user) {
      return false;
    }

    // 检查用户是否已经获得过邀请奖励
    const hasInviteReward = user.rewardHistory?.some(reward => reward.type === 'invite');
    if (hasInviteReward) {
      console.log(`用户 ${userId} 已经获得过邀请奖励，跳过发放`);
      return false;
    }

    // 获取邀请奖励天数
    const inviteRewardDays = await systemSettingsService.getInviteRewardDays();

    // 计算新的试用结束日期
    const currentEndDate = user.role === 'trial' ? new Date(user.trialEndsAt) : new Date();
    const newEndDate = new Date(currentEndDate);
    newEndDate.setDate(newEndDate.getDate() + inviteRewardDays);

    // 更新用户试用期
    await User.findByIdAndUpdate(userId, {
      role: 'trial',
      trialEndsAt: newEndDate,
      $push: {
        rewardHistory: {
          type: 'invite',
          days: inviteRewardDays,
          grantedAt: new Date(),
          reason: '成功邀请5位用户注册并验证邮箱'
        }
      }
    });

    // 创建邀请奖励通知
    try {
      await notificationService.createInviteRewardNotification(
        userId,
        inviteRewardDays,
        5 // 邀请人数
      );
    } catch (error) {
      console.error('创建邀请奖励通知失败:', error);
      // 不影响奖励发放流程，仅记录错误
    }

    return true;
  } catch (error) {
    logger.error('发放邀请奖励失败', error);
    return false;
  }
};

/**
 * 检查并处理用户邀请奖励
 * 当用户邀请的第5个用户验证邮箱时调用
 * @param inviterId 邀请人ID
 */
export const processInviteReward = async (inviterId: mongoose.Types.ObjectId): Promise<void> => {
  try {
    // 检查并发放奖励
    await grantInviteReward(inviterId);
  } catch (error) {
    logger.error('处理邀请奖励失败', error);
  }
};

export default {
  checkInviteReward,
  grantInviteReward,
  processInviteReward
};
