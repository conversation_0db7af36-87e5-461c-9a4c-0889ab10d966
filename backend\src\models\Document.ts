import mongoose, { Document, Schema } from 'mongoose';

/**
 * 文档接口定义
 */
export interface DocumentDocument extends Document {
  _id: mongoose.Types.ObjectId;
  title: string;                    // 文档标题
  content: string;                  // Markdown 正文内容
  category: 'guide' | 'faq' | 'announcement';  // 分类
  language: 'zh' | 'en';           // 语言
  isVisible: boolean;              // 是否公开显示
  order: number;                   // 排序权重
  originalId?: mongoose.Types.ObjectId;  // 原文档ID（用于翻译关联）
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 文档模式定义
 */
const documentSchema = new Schema({
  // 文档标题
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  
  // Markdown 正文内容
  content: {
    type: String,
    required: true
  },
  
  // 分类
  category: {
    type: String,
    enum: ['guide', 'faq', 'announcement'],
    required: true,
    index: true
  },
  
  // 语言
  language: {
    type: String,
    enum: ['zh', 'en'],
    required: true,
    index: true
  },
  
  // 是否公开显示
  isVisible: {
    type: Boolean,
    default: true,
    index: true
  },
  
  // 排序权重（数字越小越靠前）
  order: {
    type: Number,
    default: 0,
    index: true
  },
  
  // 原文档ID（用于翻译关联）
  originalId: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
    default: null
  }
}, {
  timestamps: true  // 自动添加 createdAt 和 updatedAt
});

// 创建复合索引以优化查询
documentSchema.index({ category: 1, language: 1, isVisible: 1, order: 1 });
documentSchema.index({ language: 1, isVisible: 1, order: 1 });

// 创建并导出模型
const DocumentModel = mongoose.model<DocumentDocument>('Document', documentSchema);

export default DocumentModel;
