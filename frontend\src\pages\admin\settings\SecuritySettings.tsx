import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import useAdminStore from '../../../store/useAdminStore';
import { useToast } from '../../../components/ui/use-toast';
import { Button, Input, Label, Textarea, Card } from '../../../components/admin/ui';

/**
 * 系统安全设置页面
 * 管理系统安全相关配置
 */
const SecuritySettings: React.FC = () => {
  const { getSystemSettings, updateSystemSettings } = useAdminStore();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  const [formData, setFormData] = useState({
    securitySettings: {
      siteDomain: '',
      apiDomain: '',
      binanceApiUrls: [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api-gcp.binance.com'
      ],
      binanceApiTimeout: 30000,
      binanceApiRetryTimes: 3,
      loginFailLimit: 5,
      loginLockTime: 2,
      enableKlinePrediction: true,
      enableLinePrediction: true
    }
  });

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await getSystemSettings();

      if (response.settings.securitySettings) {
        setFormData({
          securitySettings: response.settings.securitySettings
        });
      }

      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取系统设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  // 初次加载时获取设置
  useEffect(() => {
    fetchSettings();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // 处理嵌套属性
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // 处理数字输入变化
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // 处理嵌套属性
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: value === '' ? '' : Number(value)
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : Number(value)
      });
    }
  };

  // 处理币安API URL列表变化
  const handleApiUrlsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    const urls = value.split('\n').filter(url => url.trim() !== '');

    setFormData({
      ...formData,
      securitySettings: {
        ...formData.securitySettings,
        binanceApiUrls: urls
      }
    });
  };

  // 处理布尔值变化
  const handleBooleanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    // 处理嵌套属性
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData],
          [child]: checked
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: checked
      });
    }
  };

  // 保存设置
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      await updateSystemSettings({
        securitySettings: formData.securitySettings
      });
      toast({
        title: '安全设置已成功更新',
        variant: 'default'
      });
      setIsSaving(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新安全设置失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">系统安全设置</h1>
          <p className="text-gray-600 mt-1">管理系统安全相关配置和登录限制</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 站点域名 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.siteDomain">站点域名</Label>
                <Input
                  id="securitySettings.siteDomain"
                  name="securitySettings.siteDomain"
                  value={formData.securitySettings.siteDomain}
                  onChange={handleChange}
                  placeholder="站点域名"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: example.com（不含http://）</p>
              </div>

              {/* API域名 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.apiDomain">API域名</Label>
                <Input
                  id="securitySettings.apiDomain"
                  name="securitySettings.apiDomain"
                  value={formData.securitySettings.apiDomain}
                  onChange={handleChange}
                  placeholder="API域名"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">例如: api.example.com（不含http://）</p>
              </div>

              {/* 币安API基础地址列表 */}
              <div className="col-span-2">
                <Label htmlFor="securitySettings.binanceApiUrls">币安API基础地址列表</Label>
                <Textarea
                  id="securitySettings.binanceApiUrls"
                  value={formData.securitySettings.binanceApiUrls.join('\n')}
                  onChange={handleApiUrlsChange}
                  placeholder="每行一个URL"
                  disabled={isSaving}
                  rows={5}
                />
                <p className="text-xs text-gray-500 mt-1">每行输入一个URL，按优先级排序</p>
              </div>

              {/* 币安API请求超时时间 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.binanceApiTimeout">币安API请求超时时间（毫秒）</Label>
                <Input
                  id="securitySettings.binanceApiTimeout"
                  name="securitySettings.binanceApiTimeout"
                  type="number"
                  min="5000"
                  value={formData.securitySettings.binanceApiTimeout}
                  onChange={handleNumberChange}
                  placeholder="币安API请求超时时间"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">建议值: 30000（30秒）</p>
              </div>

              {/* 币安API请求重试次数 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.binanceApiRetryTimes">币安API请求重试次数</Label>
                <Input
                  id="securitySettings.binanceApiRetryTimes"
                  name="securitySettings.binanceApiRetryTimes"
                  type="number"
                  min="1"
                  value={formData.securitySettings.binanceApiRetryTimes}
                  onChange={handleNumberChange}
                  placeholder="币安API请求重试次数"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">请求失败时的重试次数</p>
              </div>

              {/* 登录失败限制次数 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.loginFailLimit">登录失败限制次数</Label>
                <Input
                  id="securitySettings.loginFailLimit"
                  name="securitySettings.loginFailLimit"
                  type="number"
                  min="1"
                  value={formData.securitySettings.loginFailLimit}
                  onChange={handleNumberChange}
                  placeholder="登录失败限制次数"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">连续失败多少次后锁定账号</p>
              </div>

              {/* 登录失败锁定时间 */}
              <div className="col-span-1">
                <Label htmlFor="securitySettings.loginLockTime">登录失败锁定时间（小时）</Label>
                <Input
                  id="securitySettings.loginLockTime"
                  name="securitySettings.loginLockTime"
                  type="number"
                  min="0.5"
                  step="0.5"
                  value={formData.securitySettings.loginLockTime}
                  onChange={handleNumberChange}
                  placeholder="登录失败锁定时间"
                  disabled={isSaving}
                />
                <p className="text-xs text-gray-500 mt-1">锁定多少小时后可再次尝试</p>
              </div>
            </div>

            {/* 预测图显示设置 */}
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-4">预测图显示设置</h3>
              <div className="grid grid-cols-2 gap-4">
                {/* K线预测图开关 */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="securitySettings.enableKlinePrediction"
                    name="securitySettings.enableKlinePrediction"
                    checked={formData.securitySettings.enableKlinePrediction}
                    onChange={handleBooleanChange}
                    disabled={isSaving}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <Label htmlFor="securitySettings.enableKlinePrediction" className="text-sm font-medium">
                    启用K线预测图显示
                  </Label>
                </div>

                {/* 折线预测图开关 */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="securitySettings.enableLinePrediction"
                    name="securitySettings.enableLinePrediction"
                    checked={formData.securitySettings.enableLinePrediction}
                    onChange={handleBooleanChange}
                    disabled={isSaving}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <Label htmlFor="securitySettings.enableLinePrediction" className="text-sm font-medium">
                    启用折线预测图显示
                  </Label>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">控制用户是否可以看到预测图功能</p>
            </div>

            <div className="mt-6 flex justify-end">
              <Button
                type="submit"
                className="bg-primary text-white"
                disabled={isSaving}
              >
                {isSaving ? '保存中...' : '保存设置'}
              </Button>
            </div>
            </form>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default SecuritySettings;
