declare module 'zustand' {
  export * from 'zustand/vanilla';
  export { default } from 'zustand/vanilla';
  export const create: any;
}

declare module 'zustand/middleware' {
  import { StateCreator, StoreApi } from 'zustand';

  export interface PersistOptions<T, U> {
    name: string;
    getStorage?: () => Storage;
    partialize?: (state: T) => U;
    version?: number;
    migrate?: (persistedState: unknown, version: number) => U;
    merge?: (persistedState: unknown, currentState: T) => T;
    onRehydrateStorage?: (state: T | undefined) => ((state?: T) => void) | void;
  }

  export function persist<T, U>(
    config: StateCreator<T>,
    options: PersistOptions<T, U>
  ): StateCreator<T>;

  export function devtools<T>(
    initializer: StateCreator<T>,
    devtoolsOptions?: any
  ): StateCreator<T>;

  export function subscribeWithSelector<T>(
    initializer: StateCreator<T>
  ): StateCreator<T>;

  export function combine<PrimaryState, SecondaryState>(
    initialState: PrimaryState,
    stateCreator: StateCreator<
      SecondaryState,
      [["zustand/subscribeWithSelector", never], ["zustand/devtools", never]],
      [],
      SecondaryState
    >
  ): StateCreator<PrimaryState & SecondaryState>;
} 