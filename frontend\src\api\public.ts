import axios from 'axios';

// 创建带有基础URL的axios实例
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 获取公共系统设置
export const getPublicSettings = async () => {
  try {
    const response = await api.get('/public/settings');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取系统设置失败');
    }
    throw error;
  }
};

export default {
  getPublicSettings
};
