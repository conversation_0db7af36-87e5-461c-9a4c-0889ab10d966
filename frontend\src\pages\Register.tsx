import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CyberAuthForm from '../components/CyberAuthForm';
import useUserStore from '../store/useUserStore';
import configService from '../services/configService';
import api from '../api/config';

const Register: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { register, isLoading, error, isAuthenticated, clearError, isInitializing } = useUserStore();

  // 表单错误状态（5秒自动消失）
  const [formError, setFormError] = useState<string | null>(null);
  const [errorTimer, setErrorTimer] = useState<NodeJS.Timeout | null>(null);

  const [inviteCodeRequired, setInviteCodeRequired] = useState(false);
  const [passwordMinLength, setPasswordMinLength] = useState(6);
  const [isRegistrationEnabled, setIsRegistrationEnabled] = useState(true);
  const [isConfigLoading, setIsConfigLoading] = useState(true);

  // 如果用户已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimer) {
        clearTimeout(errorTimer);
      }
    };
  }, [errorTimer]);

  // 设置表单错误，5秒后自动清除
  const setFormErrorWithTimeout = (message: string) => {
    // 清除之前的定时器
    if (errorTimer) {
      clearTimeout(errorTimer);
    }

    // 设置错误信息
    setFormError(message);

    // 5秒后清除错误信息
    const timer = setTimeout(() => {
      setFormError(null);
      setErrorTimer(null);
    }, 5000);

    setErrorTimer(timer);
  };

  // 手动清除表单错误
  const clearFormError = () => {
    if (errorTimer) {
      clearTimeout(errorTimer);
      setErrorTimer(null);
    }
    setFormError(null);
  };

  // 处理表单输入变化，清除错误提示
  const handleFormChange = (formData: Record<string, string>) => {
    // 当用户开始输入时，清除表单错误
    if (formError) {
      clearFormError();
    }
  };

  // 判断错误是否需要5秒自动消失（注册页面的所有错误都应该自动消失）
  const shouldAutoHideError = (errorMessage: string) => {
    // 注册页面的错误都是用户输入相关的，应该自动消失
    return true;
  };

  // 监听store中的error变化，判断是否需要5秒自动消失
  useEffect(() => {
    if (error && shouldAutoHideError(error)) {
      // 需要自动消失的错误，设置到formError中
      setFormErrorWithTimeout(error);
      clearError(); // 清除store中的error，避免重复显示
    }
  }, [error, clearError]);

  // 加载注册配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsConfigLoading(true);

        // 获取注册配置
        const config = await configService.getConfig();

        // 使用统一的axios实例获取用户设置
        let userSettings = null;
        try {
          const response = await api.get('/config/user-settings');
          userSettings = response.data.success ? response.data.settings : null;
        } catch (error) {
          console.error('获取用户设置失败:', error);
          userSettings = null;
        }

        // 如果API返回了用户设置，使用API返回的值
        if (userSettings) {
          setInviteCodeRequired(userSettings.inviteCodeRequired);
          setPasswordMinLength(userSettings.passwordMinLength);
          setIsRegistrationEnabled(userSettings.enableRegistration);
        } else {
          // 否则使用默认值
          setInviteCodeRequired(false);
          setPasswordMinLength(6);
          setIsRegistrationEnabled(true);
        }
      } catch (error) {
        console.error('加载注册配置失败:', error);
        // 使用默认值
        setInviteCodeRequired(false);
        setPasswordMinLength(6);
        setIsRegistrationEnabled(true);
      } finally {
        setIsConfigLoading(false);
      }
    };

    loadConfig();
  }, []);

  // 处理注册表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // 清除之前的表单错误
      clearFormError();
      clearError();

      await register(formData.email, formData.password, formData.inviteCode);
      // 注册成功，跳转到验证码输入页面
      navigate('/email-verification', {
        state: { email: formData.email },
        replace: true
      });
    } catch (error) {
      // 错误已在store中处理，会通过useEffect自动设置为5秒消失
      console.error('注册失败', error);
    }
  };

  // 注册表单字段配置
  const fields = [
    {
      name: 'email',
      label: t('auth.email'),
      type: 'email',
      placeholder: t('auth.emailPlaceholder')
    },
    {
      name: 'password',
      label: t('auth.password'),
      type: 'password',
      placeholder: t('auth.passwordTooShort', { min: passwordMinLength })
    },
    {
      name: 'inviteCode',
      label: inviteCodeRequired ? t('auth.inviteCode') : t('auth.inviteCodeOptional'),
      type: 'text',
      placeholder: inviteCodeRequired ? t('auth.inviteCodePlaceholder') : t('auth.inviteCodeOptionalPlaceholder'),
      required: inviteCodeRequired
    }
  ];

  // 如果正在初始化或加载配置，显示加载状态（cyber风格）
  if (isInitializing || isConfigLoading) {
    return (
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
    );
  }

  // 如果注册功能已关闭，显示提示信息（cyber风格）
  if (!isRegistrationEnabled) {
    return (
      <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
            {/* Card glow effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
            <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

            <div className="relative z-10">
              <div className="text-yellow-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('auth.registrationClosed')}</h2>
              <p className="text-cyber-muted mb-4 font-mono">{t('auth.registrationClosedDesc')}</p>
              <Link
                to="/login"
                className="font-bold text-cyber-cyan hover:text-cyber-cyan/80 transition-colors duration-200 outline-none focus:outline-none font-mono"
              >
                {t('auth.backToSignIn')}
              </Link>
            </div>
      </div>
    );
  }



  return (
    <div className="w-full max-w-md">
      <CyberAuthForm
        title={t('auth.signUp')}
        fields={fields}
        submitText={t('auth.signUp')}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={formError || error}
        footerText={
          <div>
            {t('auth.hasAccount')}{' '}
            <Link
              to="/login"
              className="font-bold text-cyber-cyan hover:text-cyber-cyan/80 transition-colors duration-200 outline-none focus:outline-none"
            >
              {t('auth.signInNow')}
            </Link>
          </div>
        }
        onChange={handleFormChange}
      />
    </div>
  );
};

export default Register;