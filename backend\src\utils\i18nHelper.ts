import { Request } from 'express';
import i18nService, { SupportedLanguage } from '../services/i18nService';

/**
 * 从请求头中检测用户语言偏好
 * @param req Express请求对象
 * @returns 语言代码
 */
export const detectLanguage = (req: Request): SupportedLanguage => {
  const acceptLanguage = req.headers['accept-language'];
  
  if (acceptLanguage && acceptLanguage.includes('en')) {
    return 'en';
  }
  
  return 'zh'; // 默认中文
};

/**
 * 获取翻译文本的便捷函数
 * @param req Express请求对象
 * @param key 翻译键
 * @param params 参数对象
 * @returns 翻译后的文本
 */
export const t = (req: Request, key: string, params?: Record<string, any>): string => {
  const language = detectLanguage(req);
  return i18nService.t(key, language, params);
};

/**
 * 获取指定语言的翻译文本
 * @param key 翻译键
 * @param language 语言代码
 * @param params 参数对象
 * @returns 翻译后的文本
 */
export const tl = (key: string, language: SupportedLanguage, params?: Record<string, any>): string => {
  return i18nService.t(key, language, params);
};

/**
 * 创建响应消息对象
 * @param req Express请求对象
 * @param key 翻译键
 * @param params 参数对象
 * @returns 包含翻译消息的对象
 */
export const createMessage = (req: Request, key: string, params?: Record<string, any>) => {
  return {
    message: t(req, key, params)
  };
};

/**
 * 创建成功响应
 * @param req Express请求对象
 * @param key 翻译键
 * @param data 额外数据
 * @param params 翻译参数
 * @returns 成功响应对象
 */
export const createSuccessResponse = (
  req: Request, 
  key: string, 
  data?: any, 
  params?: Record<string, any>
) => {
  const response: any = {
    success: true,
    message: t(req, key, params)
  };

  if (data !== undefined) {
    response.data = data;
  }

  return response;
};

/**
 * 创建错误响应
 * @param req Express请求对象
 * @param key 翻译键
 * @param code 错误代码
 * @param params 翻译参数
 * @returns 错误响应对象
 */
export const createErrorResponse = (
  req: Request, 
  key: string, 
  code?: string, 
  params?: Record<string, any>
) => {
  const response: any = {
    success: false,
    message: t(req, key, params)
  };

  if (code) {
    response.code = code;
  }

  return response;
};
