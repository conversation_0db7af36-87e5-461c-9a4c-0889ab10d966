import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CyberAuthForm from '../components/CyberAuthForm';
import useUserStore from '../store/useUserStore';

const Login: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading, error, isAuthenticated, clearError, isInitializing } = useUserStore();

  // 表单错误状态（5秒自动消失）
  const [formError, setFormError] = useState<string | null>(null);
  const [errorTimer, setErrorTimer] = useState<NodeJS.Timeout | null>(null);

  // 获取用户之前尝试访问的页面，如果没有则默认为首页
  const from = (location.state as any)?.from || '/';

  // 获取会话过期消息
  const messageKey = (location.state as any)?.message;
  const message = messageKey ? t(`errors.${messageKey}`) : null;
  
  // 如果用户已经登录，重定向到之前的页面
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);
  
  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 监听store中的error变化，判断是否需要5秒自动消失
  useEffect(() => {
    if (error && shouldAutoHideError(error)) {
      // 需要自动消失的错误，设置到formError中
      setFormErrorWithTimeout(error);
      clearError(); // 清除store中的error，避免重复显示
    }
  }, [error, clearError]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimer) {
        clearTimeout(errorTimer);
      }
    };
  }, [errorTimer]);

  // 设置表单错误，5秒后自动清除
  const setFormErrorWithTimeout = (message: string) => {
    // 清除之前的定时器
    if (errorTimer) {
      clearTimeout(errorTimer);
    }

    // 设置错误信息
    setFormError(message);

    // 5秒后清除错误信息
    const timer = setTimeout(() => {
      setFormError(null);
      setErrorTimer(null);
    }, 5000);

    setErrorTimer(timer);
  };

  // 手动清除表单错误
  const clearFormError = () => {
    if (errorTimer) {
      clearTimeout(errorTimer);
      setErrorTimer(null);
    }
    setFormError(null);
  };

  // 处理表单输入变化，清除错误提示
  const handleFormChange = (formData: Record<string, string>) => {
    // 当用户开始输入时，清除表单错误
    if (formError) {
      clearFormError();
    }
  };

  // 判断错误是否需要5秒自动消失
  const shouldAutoHideError = (errorMessage: string) => {
    // 需要持续显示的错误（不自动消失）
    const persistentErrors = [
      '账号已被锁定',
      '账号已被封禁',
      '会话',
      '登出',
      'session',
      'expired',
      'locked',
      'banned'
    ];

    return !persistentErrors.some(keyword => errorMessage.includes(keyword));
  };
  
  // 处理登录表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // 清除之前的表单错误
      clearFormError();
      clearError();

      await login(formData.email, formData.password);
      // 登录成功，将在useEffect中重定向
    } catch (error) {
      // 错误已在store中处理，但我们需要判断是否需要5秒自动消失
      console.error('登录失败', error);
    }
  };
  
  // 登录表单字段配置
  const fields = [
    {
      name: 'email',
      label: t('auth.email'),
      type: 'email',
      placeholder: t('auth.emailPlaceholder')
    },
    {
      name: 'password',
      label: t('auth.password'),
      type: 'password',
      placeholder: t('auth.passwordPlaceholder')
    }
  ];
  
  // 底部注册链接（cyber风格）
  const footerText = (
    <div>
      {t('auth.noAccount')}{' '}
      <Link
        to="/register"
        className="font-bold text-cyber-cyan hover:text-cyber-cyan/80 transition-colors duration-200 outline-none focus:outline-none"
      >
        {t('auth.signUpNow')}
      </Link>
    </div>
  );

  // 忘记密码链接（放在密码输入框下面）
  const forgotPasswordLink = (
    <Link
      to="/forgot-password"
      className="font-bold text-cyber-cyan hover:text-cyber-cyan/80 transition-colors duration-200 outline-none focus:outline-none"
    >
      {t('auth.forgotPassword')}
    </Link>
  );
  
  // 如果正在初始化，显示加载状态（cyber风格）
  if (isInitializing) {
    return (
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
    );
  }

  return (
    <div className="w-full max-w-md">
      <CyberAuthForm
        title={t('auth.signIn')}
        fields={fields}
        submitText={t('auth.signIn')}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={message || formError || error}
        footerText={footerText}
        forgotPasswordLink={forgotPasswordLink}
        onChange={handleFormChange}
      />
    </div>
  );
};

export default Login; 