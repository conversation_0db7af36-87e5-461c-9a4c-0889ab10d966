import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import VerificationCodeInput from '../components/VerificationCodeInput';
import useUserStore from '../store/useUserStore';

const EmailVerification: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const { verifyCode, sendVerificationCode, isLoading, error, clearError } = useUserStore();

  const [email, setEmail] = useState<string>('');
  const [resendCountdown, setResendCountdown] = useState(0);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(5);

  // 表单错误状态（5秒自动消失）
  const [formError, setFormError] = useState<string | null>(null);
  const [errorTimer, setErrorTimer] = useState<NodeJS.Timeout | null>(null);

  // 从URL参数或state获取邮箱
  useEffect(() => {
    const emailParam = searchParams.get('email');
    const emailFromState = (location.state as any)?.email;

    if (emailParam) {
      setEmail(emailParam);
      // 立即开始倒计时
      setResendCountdown(60);
    } else if (emailFromState) {
      setEmail(emailFromState);
      // 立即开始倒计时
      setResendCountdown(60);
    } else {
      // 如果没有邮箱参数，重定向到注册页
      navigate('/register', { replace: true });
    }
  }, [searchParams, location.state, navigate]);



  // 清除错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimer) {
        clearTimeout(errorTimer);
      }
    };
  }, [errorTimer]);

  // 设置表单错误，5秒后自动清除
  const setFormErrorWithTimeout = (message: string) => {
    // 清除之前的定时器
    if (errorTimer) {
      clearTimeout(errorTimer);
    }

    // 设置错误信息
    setFormError(message);

    // 5秒后清除错误信息
    const timer = setTimeout(() => {
      setFormError(null);
      setErrorTimer(null);
    }, 5000);

    setErrorTimer(timer);
  };

  // 手动清除表单错误
  const clearFormError = () => {
    if (errorTimer) {
      clearTimeout(errorTimer);
      setErrorTimer(null);
    }
    setFormError(null);
  };

  // 判断错误是否需要5秒自动消失（验证码页面的所有错误都应该自动消失）
  const shouldAutoHideError = (errorMessage: string) => {
    // 验证码页面的错误都是验证码相关的，应该自动消失
    return true;
  };

  // 监听store中的error变化，判断是否需要5秒自动消失
  useEffect(() => {
    if (error && shouldAutoHideError(error)) {
      // 需要自动消失的错误，设置到formError中
      setFormErrorWithTimeout(error);
      clearError(); // 清除store中的error，避免重复显示
    }
  }, [error, clearError]);

  // 重发验证码倒计时
  useEffect(() => {
    if (resendCountdown > 0) {
      const timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCountdown]);

  // 处理验证码完成输入
  const handleCodeComplete = async (code: string) => {
    try {
      // 清除之前的表单错误
      clearFormError();
      clearError();

      await verifyCode(email, code, 'email-verification');

      // 验证成功，显示成功卡片
      setVerificationSuccess(true);

      // 启动5秒倒计时
      let timeLeft = 5;
      setRedirectCountdown(timeLeft);

      const timer = setInterval(() => {
        timeLeft -= 1;
        setRedirectCountdown(timeLeft);

        if (timeLeft <= 0) {
          clearInterval(timer);
          navigate('/login');
        }
      }, 1000);
    } catch (error) {
      // 错误已在store中处理，会通过useEffect自动设置为5秒消失
      console.error('验证码验证失败:', error);
    }
  };

  // 重发验证码
  const handleResendCode = async () => {
    try {
      // 清除之前的表单错误
      clearFormError();
      clearError();

      await sendVerificationCode(email, 'email-verification');
      setResendCountdown(60); // 60秒倒计时
    } catch (error) {
      // 错误已在store中处理，会通过useEffect自动设置为5秒消失
      console.error('重发验证码失败:', error);
    }
  };

  // 如果验证成功，显示成功卡片
  if (verificationSuccess) {
    return (
      <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
        <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

        <div className="relative z-10">
          <div className="text-cyber-green mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('verification.emailVerificationSuccess')}</h2>
          <p className="text-cyber-muted mb-4 font-mono">{t('verification.emailVerificationSuccessDesc')}</p>
          <p className="text-sm text-cyber-muted font-mono">{t('auth.autoRedirectIn5s').replace('5', redirectCountdown.toString())}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md">
      <Card className="w-full bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl animate-fade-in relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyber-card/20 via-transparent to-cyber-blue/10 pointer-events-none" />
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent" />

        <CardHeader className="space-y-6 text-center py-8 relative z-10">
          {/* 标题 */}
          <div className="space-y-3">
            <CardTitle className="text-3xl font-bold text-cyber-text tracking-wider font-sans">
              {t('verification.emailVerification')}
            </CardTitle>
            <p className="text-cyber-muted font-mono text-sm">
              {t('verification.enterCodeSentTo')}
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 px-8 pb-8 relative z-10">
          {/* 验证码输入组件 */}
          <VerificationCodeInput
            length={6}
            onComplete={handleCodeComplete}
            onResend={handleResendCode}
            isLoading={isLoading}
            error={formError || error}
            email={email}
            resendCountdown={resendCountdown}
          />

          {/* 返回注册页面按钮 */}
          <div className="text-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/register')}
              className="text-cyber-cyan hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm border-cyber-cyan/30 border hover:border-cyber-cyan/60 rounded-xl py-2 px-6"
            >
              {t('verification.backToRegister')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailVerification;
