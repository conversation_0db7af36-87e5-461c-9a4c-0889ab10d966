import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import User from '../models/User';
import { AuthErrorCode } from '../types/errors';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// 管理员专用JWT密钥
const JWT_ADMIN_SECRET = process.env.JWT_ADMIN_SECRET || 'fallback-admin-secret';
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret'; // 向后兼容

// 管理员Token撤销机制
const revokedAdminTokens = new Set<string>();

/**
 * 撤销管理员token
 */
export const revokeAdminToken = (jti: string): void => {
  revokedAdminTokens.add(jti);
  console.log(`管理员Token已撤销: ${jti}`);
};

/**
 * 检查管理员token是否被撤销
 */
export const isAdminTokenRevoked = (jti: string): boolean => {
  return revokedAdminTokens.has(jti);
};

/**
 * 生成管理员token
 */
export const generateAdminToken = (userId: string): string => {
  return jwt.sign(
    {
      userId: userId.toString(),
      type: 'admin',
      jti: uuidv4(),
      iat: Math.floor(Date.now() / 1000)
    },
    JWT_ADMIN_SECRET,
    { expiresIn: '2h' }
  );
};

/**
 * 专用管理员认证中间件
 */
export const authenticateAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 从 Authorization header 中获取 token
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '未提供管理员认证令牌',
        code: AuthErrorCode.TOKEN_MISSING
      });
    }

    // 提取 token
    const token = authHeader.split(' ')[1];
    
    // 验证管理员token
    let decoded: { userId: string; type?: string; jti?: string };
    let isOldToken = false;
    
    try {
      // 优先使用管理员专用密钥
      decoded = jwt.verify(token, JWT_ADMIN_SECRET) as { userId: string; type?: string; jti?: string };

      // 只对新token检查type字段（旧token可能没有type字段）
      if (decoded.type && decoded.type !== 'admin') {
        throw new Error('非管理员token');
      }
    } catch (error) {
      try {
        // 备用：使用旧密钥（向后兼容）
        decoded = jwt.verify(token, JWT_SECRET) as { userId: string; type?: string; jti?: string };
        isOldToken = true;
        console.log('使用旧密钥验证管理员token成功，建议重新登录');
      } catch (oldError) {
        // 处理不同类型的JWT错误
        if (error instanceof jwt.TokenExpiredError) {
          return res.status(401).json({
            success: false,
            message: '管理员认证令牌已过期',
            code: AuthErrorCode.TOKEN_EXPIRED
          });
        } else {
          return res.status(401).json({
            success: false,
            message: '管理员认证令牌无效',
            code: AuthErrorCode.TOKEN_INVALID
          });
        }
      }
    }
    
    // 检查token是否被撤销（只对新token检查）
    if (!isOldToken && decoded.jti && isAdminTokenRevoked(decoded.jti)) {
      return res.status(401).json({
        success: false,
        message: '管理员认证令牌已被撤销',
        code: AuthErrorCode.TOKEN_INVALID
      });
    }
    
    // 查找用户并验证管理员权限
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '管理员用户不存在',
        code: AuthErrorCode.USER_NOT_FOUND
      });
    }
    
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无管理员权限',
        code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
      });
    }
    
    // 管理员不需要检查邮箱验证、封禁状态、试用期等用户特定逻辑
    
    // 设置用户信息到请求对象
    req.user = user;
    next();
  } catch (error) {
    logger.error('管理员认证过程中发生错误', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试',
      code: 'common/server-error'
    });
  }
};
