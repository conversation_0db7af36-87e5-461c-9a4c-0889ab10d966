import api from '../api/config';

export interface Notification {
  _id: string;
  userId: string;
  type: 'trial_granted' | 'trial_expiring' | 'subscription_success' | 'subscription_expiring' | 'invite_reward';
  title: string;
  content: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  metadata?: any;
  createdAt: string;
  readAt?: string;
  updatedAt: string;
}

export interface NotificationListResponse {
  success: boolean;
  data: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UnreadCountResponse {
  success: boolean;
  count: number;
}

/**
 * 通知服务类
 */
class NotificationService {
  /**
   * 获取用户通知列表
   * @param page 页码
   * @param limit 每页数量
   * @param unreadOnly 是否只获取未读通知
   */
  async getNotifications(
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false
  ): Promise<NotificationListResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      unreadOnly: unreadOnly.toString()
    });

    const response = await api.get(`/notifications?${params}`);
    return response.data;
  }

  /**
   * 获取未读通知数量
   */
  async getUnreadCount(): Promise<number> {
    const response = await api.get('/notifications/unread-count');
    return response.data.count;
  }

  /**
   * 标记通知为已读
   * @param notificationId 通知ID
   */
  async markAsRead(notificationId: string): Promise<void> {
    await api.patch(`/notifications/${notificationId}/read`);
  }

  /**
   * 标记所有通知为已读
   */
  async markAllAsRead(): Promise<void> {
    await api.patch('/notifications/mark-all-read');
  }

  /**
   * 删除通知
   * @param notificationId 通知ID
   */
  async deleteNotification(notificationId: string): Promise<void> {
    await api.delete(`/notifications/${notificationId}`);
  }

  /**
   * 获取通知类型的显示名称
   * @param type 通知类型
   */
  getTypeDisplayName(type: Notification['type']): string {
    const typeNames = {
      trial_granted: '试用授权',
      trial_expiring: '试用到期',
      subscription_success: '订阅成功',
      subscription_expiring: '订阅到期',
      invite_reward: '邀请奖励'
    };
    return typeNames[type] || type;
  }

  /**
   * 获取通知类型的图标
   * @param type 通知类型
   */
  getTypeIcon(type: Notification['type']): string {
    const typeIcons = {
      trial_granted: '🎉',
      trial_expiring: '⏰',
      subscription_success: '✅',
      subscription_expiring: '🔔',
      invite_reward: '🎁'
    };
    return typeIcons[type] || '📢';
  }

  /**
   * 获取通知优先级的颜色类
   * @param priority 优先级
   */
  getPriorityColorClass(priority: Notification['priority']): string {
    const priorityColors = {
      low: 'text-content-muted',
      medium: 'text-primary',
      high: 'text-error'
    };
    return priorityColors[priority] || 'text-content-muted';
  }

  /**
   * 格式化时间显示
   * @param dateString 时间字符串
   * @param language 语言代码
   */
  formatTime(dateString: string, language: string = 'zh'): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return language === 'zh' ? '刚刚' : 'just now';
    } else if (diffInMinutes < 60) {
      return language === 'zh' ? `${diffInMinutes}分钟前` : `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) { // 24小时
      const hours = Math.floor(diffInMinutes / 60);
      return language === 'zh' ? `${hours}小时前` : `${hours} hours ago`;
    } else if (diffInMinutes < 10080) { // 7天
      const days = Math.floor(diffInMinutes / 1440);
      return language === 'zh' ? `${days}天前` : `${days} days ago`;
    } else {
      const locale = language === 'zh' ? 'zh-CN' : 'en-US';
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }
}

export default new NotificationService();
