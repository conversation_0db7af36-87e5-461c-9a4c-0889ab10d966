import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import { useToast } from '../../components/ui/use-toast';
import useAdminStore from '../../store/useAdminStore';

// 支付状态标签颜色映射
const statusColors: Record<string, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirming: 'bg-blue-100 text-blue-800',
  confirmed: 'bg-green-100 text-green-800',
  sending: 'bg-purple-100 text-purple-800',
  partially_paid: 'bg-orange-100 text-orange-800',
  finished: 'bg-green-500 text-white',
  failed: 'bg-red-100 text-red-800',
  refunded: 'bg-gray-100 text-gray-800',
  expired: 'bg-red-50 text-red-600'
};

// 支付状态中文描述
const statusLabels: Record<string, string> = {
  pending: '等待支付',
  confirming: '确认中',
  confirmed: '已确认',
  sending: '发送中',
  partially_paid: '部分支付',
  finished: '已完成',
  failed: '失败',
  refunded: '已退款',
  expired: '已过期'
};

// 计划名称中文描述
const planLabels: Record<string, string> = {
  monthly: '月度套餐',
  quarterly: '季度套餐',
  yearly: '年度套餐'
};

const PaymentDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getPaymentDetails, updatePaymentStatus } = useAdminStore();
  const { toast } = useToast();
  
  const [payment, setPayment] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  
  // 获取支付详情
  const fetchPaymentDetails = async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      const response = await getPaymentDetails(id);
      setPayment(response.payment);
      setUser(response.user);
      setSelectedStatus(response.payment.paymentStatus);
      setIsLoading(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取支付详情失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };
  
  // 更新支付状态
  const handleStatusUpdate = async () => {
    if (!id || !selectedStatus || selectedStatus === payment?.paymentStatus) return;
    
    try {
      setIsUpdating(true);
      await updatePaymentStatus(id, selectedStatus);
      toast({
        title: '支付状态已更新',
        variant: 'default'
      });
      fetchPaymentDetails(); // 重新获取最新数据
      setIsUpdating(false);
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新支付状态失败';
      toast({
        title: message,
        variant: 'destructive'
      });
      setIsUpdating(false);
    }
  };
  
  // 初次加载时获取支付详情
  useEffect(() => {
    fetchPaymentDetails();
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps
  
  // 格式化金额
  const formatAmount = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };
  
  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '无';
    return new Date(dateString).toLocaleString();
  };
  
  if (isLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }
  
  if (!payment) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-content-primary mb-4">未找到支付记录</h2>
            <p className="text-content-muted dark:text-content-secondary mb-4">无法找到指定的支付记录或记录不存在。</p>
            <button
              onClick={() => navigate('/admin/payments')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              返回支付列表
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }
  
  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-content-primary">支付详情</h1>
          <button
            onClick={() => navigate('/admin/payments')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            返回列表
          </button>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 基本信息卡片 */}
          <div className="lg:col-span-2 bg-white dark:bg-background-card shadow-card rounded-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-content-primary mb-4">基本信息</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">支付ID</p>
                <p className="text-base text-gray-900 dark:text-content-primary">{payment.paymentId}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">支付状态</p>
                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[payment.paymentStatus] || 'bg-gray-100 text-gray-800'}`}>
                  {statusLabels[payment.paymentStatus] || payment.paymentStatus}
                </span>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">订单ID</p>
                <p className="text-base text-gray-900 dark:text-content-primary">{payment.orderId}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">金额</p>
                <p className="text-base text-gray-900 dark:text-content-primary font-medium">
                  {formatAmount(payment.amount)} {payment.currency}
                </p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">套餐</p>
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  {planLabels[payment.plan] || payment.plan}
                </span>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">创建时间</p>
                <p className="text-base text-gray-900 dark:text-content-primary">{formatDate(payment.createdAt)}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">发票ID</p>
                <p className="text-base text-gray-900 dark:text-content-primary">{payment.invoiceId || '无'}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">最后更新时间</p>
                <p className="text-base text-gray-900 dark:text-content-primary">{formatDate(payment.updatedAt)}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">支付地址</p>
                <p className="text-base text-gray-900 dark:text-content-primary break-all">{payment.paymentAddress || '无'}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">交易哈希</p>
                <p className="text-base text-gray-900 dark:text-content-primary break-all">{payment.txId || '无'}</p>
              </div>
            </div>
          </div>
          
          {/* 用户信息和操作卡片 */}
          <div className="bg-white dark:bg-background-card shadow-card rounded-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-content-primary mb-4">用户信息</h2>
            
            {user ? (
              <div className="mb-6">
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">用户邮箱</p>
                  <p className="text-base text-gray-900 dark:text-content-primary">{user.email}</p>
                </div>
                
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">用户ID</p>
                  <p className="text-base text-gray-900 dark:text-content-primary">{user._id}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-content-secondary">用户角色</p>
                  <p className="text-base text-gray-900 dark:text-content-primary">{user.role}</p>
                </div>
              </div>
            ) : (
              <div className="mb-6">
                <p className="text-base text-gray-500 dark:text-content-secondary">未找到用户信息</p>
              </div>
            )}
            
            <h2 className="text-xl font-bold text-gray-900 dark:text-content-primary mb-4">更新状态</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-content-secondary mb-2">
                支付状态
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-background-input dark:border-border dark:text-content-primary"
              >
                <option value="pending">等待支付</option>
                <option value="confirming">确认中</option>
                <option value="confirmed">已确认</option>
                <option value="sending">发送中</option>
                <option value="partially_paid">部分支付</option>
                <option value="finished">已完成</option>
                <option value="failed">失败</option>
                <option value="refunded">已退款</option>
                <option value="expired">已过期</option>
              </select>
            </div>
            
            <button
              onClick={handleStatusUpdate}
              disabled={isUpdating || selectedStatus === payment.paymentStatus}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdating ? '更新中...' : '更新状态'}
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default PaymentDetails; 