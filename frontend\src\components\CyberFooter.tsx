import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '../lib/utils';
import configService, { SiteInfo } from '../services/configService';

// X (Twitter) 图标组件
const XIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

// Telegram 图标组件
const TelegramIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
  </svg>
);

interface CyberFooterProps {
  theme?: 'cyber' | 'white';
  className?: string;
  showSocialLinks?: boolean;
  showNavLinks?: boolean;
}

const CyberFooter: React.FC<CyberFooterProps> = ({ 
  theme = 'cyber',
  className,
  showSocialLinks = true,
  showNavLinks = true
}) => {
  const { t } = useTranslation();
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        const config = await configService.getConfig();
        setSiteInfo(config.siteInfo);
      } catch (error) {
        console.error('加载网站配置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSiteInfo();
  }, []);

  // 主题样式
  const themeStyles = {
    cyber: {
      container: "bg-cyber-card/10 backdrop-blur-sm border-t border-cyber-border/30 text-cyber-muted",
      animatedBorder: "bg-gradient-to-r from-transparent via-cyber-purple/50 to-transparent animate-pulse-neon",
      text: "text-cyber-muted font-mono",
      link: "text-cyber-muted hover:text-cyber-cyan transition-colors font-mono"
    },
    white: {
      container: "bg-white border-t border-gray-200 text-gray-500",
      animatedBorder: "bg-gradient-to-r from-transparent via-gray-300 to-transparent",
      text: "text-gray-500 font-mono",
      link: "text-gray-500 hover:text-gray-700 transition-colors font-mono"
    }
  };

  const styles = themeStyles[theme];

  if (isLoading) {
    return (
      <footer className={cn("w-full px-6 py-4 relative", styles.container, className)}>
        <div className="text-center">
          <div className="animate-pulse bg-gray-200 h-4 w-48 mx-auto rounded"></div>
        </div>
      </footer>
    );
  }

  return (
    <footer className={cn("w-full py-4 relative", styles.container, className)}>
      {/* 动画边框 */}
      <div className={cn("absolute top-0 left-0 w-full h-px", styles.animatedBorder)}></div>

      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          {/* 版权信息 */}
          <div className={cn("text-sm", styles.text)}>
            {siteInfo?.copyright || `© ${new Date().getFullYear()} ${siteInfo?.siteName || t('app.title')}. All rights reserved.`}
          </div>

          {/* 导航链接和社交链接 */}
          <div className="flex items-center space-x-4">
            {showNavLinks && (
              <>
                <a href="/privacy" className={cn("text-sm", styles.link)}>
                  {t('footer.privacy')}
                </a>
                <a href="/terms" className={cn("text-sm", styles.link)}>
                  {t('footer.terms')}
                </a>
              </>
            )}
            
            {showSocialLinks && (
              <>
                {siteInfo?.socialLinks?.twitter && (
                  <a
                    href={siteInfo.socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn("transition-colors", theme === 'cyber' ? 'text-cyber-muted hover:text-cyber-cyan' : 'text-gray-500 hover:text-gray-700')}
                    title="Twitter"
                  >
                    <XIcon className="h-5 w-5" />
                  </a>
                )}
                {siteInfo?.socialLinks?.telegram && (
                  <a
                    href={siteInfo.socialLinks.telegram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn("transition-colors", theme === 'cyber' ? 'text-cyber-muted hover:text-cyber-cyan' : 'text-gray-500 hover:text-gray-700')}
                    title="Telegram"
                  >
                    <TelegramIcon className="h-5 w-5" />
                  </a>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default CyberFooter;
