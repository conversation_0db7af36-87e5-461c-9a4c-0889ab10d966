/* 管理员后台专用修复样式 */

/* 修复浏览器自动填充的深色背景问题 */
.admin-page input:-webkit-autofill,
.admin-page input:-webkit-autofill:hover,
.admin-page input:-webkit-autofill:focus,
.admin-page input:-webkit-autofill:active,
.admin-page select:-webkit-autofill,
.admin-page select:-webkit-autofill:hover,
.admin-page select:-webkit-autofill:focus,
.admin-page select:-webkit-autofill:active,
.admin-page textarea:-webkit-autofill,
.admin-page textarea:-webkit-autofill:hover,
.admin-page textarea:-webkit-autofill:focus,
.admin-page textarea:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: #374151 !important;
  background-color: white !important;
  color: #374151 !important;
}

/* 确保所有输入框在管理员页面都有正确的样式 */
.admin-page input,
.admin-page select,
.admin-page textarea {
  background-color: white !important;
  color: #374151 !important;
  border: 1px solid #d1d5db !important;
}

.admin-page input:focus,
.admin-page select:focus,
.admin-page textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* 修复下拉菜单选项的颜色 */
.admin-page select option {
  background-color: white !important;
  color: #374151 !important;
}

/* 修复Portal组件（下拉菜单等）的样式 */
body > div[data-radix-portal] .admin-portal-content,
body > div[data-radix-portal] .admin-portal-content * {
  background-color: white !important;
  color: #374151 !important;
}

body > div[data-radix-portal] .admin-portal-content [role="option"],
body > div[data-radix-portal] .admin-portal-content [role="menuitem"] {
  background-color: white !important;
  color: #374151 !important;
}

body > div[data-radix-portal] .admin-portal-content [role="option"]:hover,
body > div[data-radix-portal] .admin-portal-content [role="menuitem"]:hover {
  background-color: #f3f4f6 !important;
  color: #111827 !important;
}

/* 确保标签文字颜色正确 */
.admin-page label {
  color: #374151 !important;
}

/* 确保表格文字颜色正确 */
.admin-page table,
.admin-page th,
.admin-page td {
  color: #374151 !important;
}

/* 确保卡片内容颜色正确 */
.admin-page .card,
.admin-page .card * {
  color: #374151 !important;
}
