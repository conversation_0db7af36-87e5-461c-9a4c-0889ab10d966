/**
 * 节气数据文件更新模块（精简版）
 * 负责读取、更新和写入节气常量文件
 */

const fs = require('fs');
const path = require('path');

// 节气数据文件路径
const SOLAR_TERMS_SRC_PATH = path.join(__dirname, '../src/data/solarTermData.json');
const SOLAR_TERMS_DIST_PATH = path.join(__dirname, '../dist/data/solarTermData.json');

/**
 * 读取现有的节气数据文件
 * @returns {Object} 解析后的节气数据
 */
function readExistingSolarTermsData() {
  try {
    // 优先读取源码目录的文件
    let filePath = SOLAR_TERMS_SRC_PATH;
    if (!fs.existsSync(SOLAR_TERMS_SRC_PATH)) {
      // 如果源码文件不存在，尝试读取编译目录的文件
      if (fs.existsSync(SOLAR_TERMS_DIST_PATH)) {
        filePath = SOLAR_TERMS_DIST_PATH;
      } else {
        console.log('📝 节气数据文件不存在，将创建新文件');
        return {};
      }
    }

    const fileContent = fs.readFileSync(filePath, 'utf8');
    const existingData = JSON.parse(fileContent);

    console.log(`📖 成功读取现有节气数据，包含${Object.keys(existingData).length}年的数据`);
    return existingData;

  } catch (error) {
    console.error('❌ 读取现有节气数据失败:', error.message);
    console.log('🔄 将创建新的数据文件');
    return {};
  }
}



/**
 * 合并现有数据和新数据，并清理过期数据
 * @param {Object} existingData 现有数据
 * @param {Object} newData 新数据
 * @returns {Object} 合并后的数据
 */
function mergeAndCleanData(existingData, newData) {
  console.log('🔄 合并和清理节气数据...');

  const currentYear = new Date().getFullYear();
  const cutoffYear = currentYear - 1; // 清理1年前的数据，保留当前年份及以后的数据

  console.log(`📅 当前年份: ${currentYear}，将清理${cutoffYear}年之前的数据`);

  // 1. 清理过期数据
  const cleanedData = {};
  let removedYears = 0;

  for (const year in existingData) {
    const yearNum = parseInt(year);
    if (yearNum >= cutoffYear) {
      cleanedData[year] = existingData[year];
      console.log(`✅ 保留${year}年数据`);
    } else {
      console.log(`🗑️ 清理${year}年过期数据`);
      removedYears++;
    }
  }

  // 2. 合并新数据
  let addedYears = 0;
  let updatedYears = 0;

  for (const year in newData) {
    if (cleanedData[year]) {
      console.log(`⚠️ ${year}年数据已存在，将被更新`);
      updatedYears++;
    } else {
      console.log(`➕ 添加${year}年节气数据`);
      addedYears++;
    }

    cleanedData[year] = newData[year];
  }

  console.log(`📊 数据处理完成: 清理${removedYears}年，新增${addedYears}年，更新${updatedYears}年`);
  return cleanedData;
}

/**
 * 生成格式化的数据字符串
 * @param {Object} data 节气数据
 * @returns {string} 格式化的数据字符串
 */
function generateFormattedDataString(data) {
  const years = Object.keys(data).sort((a, b) => parseInt(a) - parseInt(b));
  let dataString = '{\n';
  
  years.forEach((year, index) => {
    const yearData = data[year];
    dataString += `  // ${year}年每月第一个节气日期\n`;
    dataString += `  '${year}': {\n`;
    
    for (let month = 1; month <= 12; month++) {
      const day = yearData[month.toString()];
      if (day) {
        const monthName = getMonthSolarTermName(parseInt(month));
        dataString += `    '${month}': ${day},  // ${monthName} (${month}月${day}日)\n`;
      }
    }
    
    dataString += '  }';
    if (index < years.length - 1) {
      dataString += ',\n\n';
    } else {
      dataString += '\n';
    }
  });
  
  dataString += '}';
  return dataString;
}

/**
 * 获取月份对应的节气名称（简化版）
 * @param {number} month 月份
 * @returns {string} 节气名称
 */
function getMonthSolarTermName(month) {
  const solarTermNames = {
    1: '小寒',
    2: '立春',
    3: '惊蛰',
    4: '清明',
    5: '立夏',
    6: '芒种',
    7: '小暑',
    8: '立秋',
    9: '白露',
    10: '寒露',
    11: '立冬',
    12: '大雪'
  };
  
  return solarTermNames[month] || `${month}月节气`;
}

/**
 * 生成完整的文件内容
 * @param {Object} data 节气数据
 * @returns {string} 完整的文件内容
 */
function generateFileContent(data) {
  const formattedData = generateFormattedDataString(data);
  const currentDate = new Date().toISOString().split('T')[0];

  return `/**
 * 节气数据
 * 每年每月第一个节气的日期
 * 格式: { 年份: { 月份: 日期 } }
 *
 * 自动生成，请勿手动修改
 * 最后更新: ${currentDate}
 */

export const solarTermData: { [year: string]: { [month: string]: number } } = ${formattedData};

export default solarTermData;
`;
}

/**
 * 写入更新后的文件
 * @param {Object} data 节气数据
 * @returns {boolean} 是否写入成功
 */
function writeUpdatedFile(data) {
  try {
    console.log('📝 写入更新后的节气数据文件...');

    // 生成 JSON 文件内容
    const jsonContent = JSON.stringify(data, null, 2);

    // 确保目录存在
    const srcDir = path.dirname(SOLAR_TERMS_SRC_PATH);
    const distDir = path.dirname(SOLAR_TERMS_DIST_PATH);

    if (!fs.existsSync(srcDir)) {
      fs.mkdirSync(srcDir, { recursive: true });
    }
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }

    // 同时写入源码和编译目录
    fs.writeFileSync(SOLAR_TERMS_SRC_PATH, jsonContent, 'utf8');
    fs.writeFileSync(SOLAR_TERMS_DIST_PATH, jsonContent, 'utf8');

    console.log(`✅ 节气数据文件更新成功:`);
    console.log(`   源码文件: ${SOLAR_TERMS_SRC_PATH}`);
    console.log(`   编译文件: ${SOLAR_TERMS_DIST_PATH}`);
    return true;

  } catch (error) {
    console.error('❌ 写入文件失败:', error.message);
    return false;
  }
}



/**
 * 更新节气数据文件的主函数（精简版）
 * @param {Object} newData 新的节气数据
 * @returns {boolean} 是否更新成功
 */
async function updateSolarTermsFile(newData) {
  try {
    console.log('🔄 开始更新节气数据文件...');

    // 1. 读取现有数据
    const existingData = readExistingSolarTermsData();

    // 2. 合并和清理数据
    const mergedData = mergeAndCleanData(existingData, newData);

    // 3. 写入更新后的文件
    const writeSuccess = writeUpdatedFile(mergedData);

    if (writeSuccess) {
      console.log('✅ 节气数据文件更新完成');
      return true;
    } else {
      throw new Error('文件写入失败');
    }

  } catch (error) {
    console.error('❌ 更新节气数据文件失败:', error.message);
    return false;
  }
}

module.exports = {
  updateSolarTermsFile,
  readExistingSolarTermsData,
  mergeAndCleanData
};
