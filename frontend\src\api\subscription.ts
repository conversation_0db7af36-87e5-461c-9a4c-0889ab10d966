import axios from 'axios';
import api from './config';
import i18n from '../i18n';

// 获取翻译的错误消息
const getErrorMessage = (key: string, fallback: string) => {
  return i18n.t(key, fallback);
};

// 接口类型定义
export interface SubscriptionInfo {
  status: 'active' | 'trial' | 'inactive';
  type: 'monthly' | 'quarterly' | 'yearly' | 'trial' | null;
  daysRemaining: number;
  endDate: string | null;
}

export interface PaymentHistory {
  id: string;
  amount: number;
  currency: string;
  plan: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface PaymentResponse {
  id: string;
  paymentId: string;
  invoiceUrl: string;
  status: string;
}

interface SubscriptionResponse {
  success: boolean;
  subscription: SubscriptionInfo;
}

interface PaymentHistoryResponse {
  success: boolean;
  payments: PaymentHistory[];
}

interface CreateSubscriptionResponse {
  success: boolean;
  payment: PaymentResponse;
}

// 创建订阅
export const createSubscription = async (plan: string): Promise<CreateSubscriptionResponse> => {
  try {
    const response = await api.post('/subscription/create', { plan });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.createSubscriptionFailed', '创建订阅失败'));
    }
    throw error;
  }
};

// 获取订阅信息
export const getSubscriptionInfo = async (): Promise<SubscriptionResponse> => {
  try {
    const response = await api.get('/subscription/info');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.getSubscriptionInfoFailed', '获取订阅信息失败'));
    }
    throw error;
  }
};

// 获取支付历史
export const getPaymentHistory = async (): Promise<PaymentHistoryResponse> => {
  try {
    const response = await api.get('/subscription/payments');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || getErrorMessage('errors.getPaymentHistoryFailed', '获取支付历史失败'));
    }
    throw error;
  }
};

export default {
  createSubscription,
  getSubscriptionInfo,
  getPaymentHistory
}; 