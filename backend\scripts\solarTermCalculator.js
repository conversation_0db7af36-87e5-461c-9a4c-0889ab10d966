/**
 * 节气计算模块
 * 使用 lunar-javascript 库计算节气数据
 * 获取传统日历上显示的节气日期（基于节气时刻的日期归属）
 */

const { Solar } = require('lunar-javascript');

/**
 * 中国24节气名称（按时间顺序）
 */
const CHINESE_SOLAR_TERMS = [
  '小寒', '大寒', '立春', '雨水', '惊蛰', '春分',
  '清明', '谷雨', '立夏', '小满', '芒种', '夏至',
  '小暑', '大暑', '立秋', '处暑', '白露', '秋分',
  '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
];

/**
 * 判断是否为中文节气名
 * @param {string} name 节气名
 * @returns {boolean} 是否为中文节气名
 */
function isChineseSolarTerm(name) {
  return CHINESE_SOLAR_TERMS.includes(name);
}

/**
 * 获取指定年份每月第一个节气的日期
 * 基于节气时刻计算传统日历上的节气日期
 * @param {number} year 年份
 * @returns {Object} 格式: {1: 6, 2: 4, 3: 5, ...}
 */
function getSolarTermsForYear(year) {
  console.log(`正在计算${year}年的节气数据（传统日历日期）...`);

  try {
    // 获取指定年份的节气时刻表
    const solar = Solar.fromYmd(year, 1, 1);
    const lunar = solar.getLunar();
    const jieQiTable = lunar.getJieQiTable();

    console.log(`${year}年共获取到${Object.keys(jieQiTable).length}个节气时刻`);

    // 提取中文节气的日历日期
    const chineseTermsCalendarDates = extractChineseTermsCalendarDates(jieQiTable, year);

    // 提取每月第一个节气
    const monthlyFirstTerms = extractMonthlyFirstTerms(chineseTermsCalendarDates, year);

    return monthlyFirstTerms;

  } catch (error) {
    console.error(`计算${year}年节气数据时出错:`, error.message);
    throw error;
  }
}

/**
 * 从节气表中提取中文节气的日历日期
 * 将节气时刻转换为传统日历上显示的日期
 * @param {Object} jieQiTable 原始节气表
 * @param {number} year 年份
 * @returns {Object} 中文节气的日历日期表
 */
function extractChineseTermsCalendarDates(jieQiTable, year) {
  const chineseTermsCalendarDates = {};

  for (const termName in jieQiTable) {
    if (isChineseSolarTerm(termName)) {
      const termSolar = jieQiTable[termName];

      // 获取节气的精确时刻信息
      const termYear = termSolar.getYear();
      const termMonth = termSolar.getMonth();
      const termDay = termSolar.getDay();
      const termHour = termSolar.getHour();
      const termMinute = termSolar.getMinute();
      const termSecond = termSolar.getSecond();

      // 显示节气的精确时刻（用于调试和验证）
      const timeString = `${termHour.toString().padStart(2, '0')}:${termMinute.toString().padStart(2, '0')}:${termSecond.toString().padStart(2, '0')}`;
      console.log(`   ${termName}: ${termYear}-${termMonth.toString().padStart(2, '0')}-${termDay.toString().padStart(2, '0')} ${timeString} (北京时间)`);

      // 传统日历日期就是节气时刻所在的日期
      // lunar-javascript已经处理了时区转换，直接使用其日期即可
      chineseTermsCalendarDates[termName] = {
        year: termYear,
        month: termMonth,
        day: termDay,
        hour: termHour,
        minute: termMinute,
        second: termSecond,
        solar: termSolar
      };
    }
  }

  console.log(`${year}年提取到${Object.keys(chineseTermsCalendarDates).length}个中文节气的日历日期`);
  return chineseTermsCalendarDates;
}

/**
 * 从中文节气日历日期表中提取每月第一个节气
 * @param {Object} chineseTermsCalendarDates 中文节气日历日期表
 * @param {number} year 年份
 * @returns {Object} 每月第一个节气的日期
 */
function extractMonthlyFirstTerms(chineseTermsCalendarDates, year) {
  const monthlyFirstTerms = {};

  // 按月份分组节气
  const termsByMonth = {};

  for (const termName in chineseTermsCalendarDates) {
    const termData = chineseTermsCalendarDates[termName];
    const month = termData.month;
    const day = termData.day;

    if (!termsByMonth[month]) {
      termsByMonth[month] = [];
    }

    termsByMonth[month].push({
      name: termName,
      day: day,
      hour: termData.hour,
      minute: termData.minute,
      termData: termData
    });
  }

  // 获取每月第一个节气（按日期和时间排序）
  for (let month = 1; month <= 12; month++) {
    if (termsByMonth[month] && termsByMonth[month].length > 0) {
      // 按日期和时间排序，取第一个（最早的）
      const sortedTerms = termsByMonth[month].sort((a, b) => {
        if (a.day !== b.day) {
          return a.day - b.day;
        }
        // 如果同一天，按时间排序
        if (a.hour !== b.hour) {
          return a.hour - b.hour;
        }
        return a.minute - b.minute;
      });

      const firstTerm = sortedTerms[0];
      const timeString = `${firstTerm.hour.toString().padStart(2, '0')}:${firstTerm.minute.toString().padStart(2, '0')}`;

      monthlyFirstTerms[month.toString()] = firstTerm.day;
      console.log(`${year}年${month}月第一个节气: ${firstTerm.name} (${month}月${firstTerm.day}日 ${timeString})`);

      // 如果有多个节气，显示其他节气信息
      if (sortedTerms.length > 1) {
        const otherTerms = sortedTerms.slice(1).map(t => {
          const tTimeString = `${t.hour.toString().padStart(2, '0')}:${t.minute.toString().padStart(2, '0')}`;
          return `${t.name}(${t.day}日 ${tTimeString})`;
        });
        console.log(`   该月其他节气: ${otherTerms.join(', ')}`);
      }
    } else {
      console.warn(`${year}年${month}月未找到中文节气`);
    }
  }

  return monthlyFirstTerms;
}

/**
 * 验证节气数据的合理性
 * @param {Object} yearData 年份节气数据
 * @param {number} year 年份
 * @returns {boolean} 验证是否通过
 */
function validateSolarTermsData(yearData, year) {
  console.log(`验证${year}年节气数据...`);
  
  // 1. 检查是否包含12个月的数据
  if (Object.keys(yearData).length !== 12) {
    console.error(`${year}年节气数据不完整，只有${Object.keys(yearData).length}个月`);
    return false;
  }
  
  // 2. 检查每个月的日期是否合理
  for (let month = 1; month <= 12; month++) {
    const day = yearData[month.toString()];
    
    if (!day || day < 1 || day > 31) {
      console.error(`${year}年${month}月节气日期异常: ${day}`);
      return false;
    }
    
    // 检查日期是否在该月范围内
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day > daysInMonth) {
      console.error(`${year}年${month}月节气日期超出月份范围: ${day} > ${daysInMonth}`);
      return false;
    }
  }
  
  // 3. 检查节气间隔是否合理
  if (!validateSolarTermIntervals(yearData, year)) {
    return false;
  }
  
  console.log(`${year}年节气数据验证通过`);
  return true;
}

/**
 * 验证节气间隔的合理性
 * @param {Object} yearData 年份节气数据
 * @param {number} year 年份
 * @returns {boolean} 验证是否通过
 */
function validateSolarTermIntervals(yearData, year) {
  // 检查相邻月份节气间隔（大致28-32天）
  for (let month = 1; month < 12; month++) {
    const currentDay = yearData[month.toString()];
    const nextDay = yearData[(month + 1).toString()];
    
    if (currentDay && nextDay) {
      const currentDate = new Date(year, month - 1, currentDay);
      const nextDate = new Date(year, month, nextDay);
      const daysDiff = (nextDate - currentDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff < 25 || daysDiff > 35) {
        console.error(`${year}年${month}月到${month + 1}月节气间隔异常: ${daysDiff}天`);
        return false;
      }
    }
  }
  
  return true;
}

/**
 * 与已知准确数据对比验证
 * @param {Object} calculatedData 计算得出的数据
 * @param {number} year 年份
 * @returns {boolean} 验证是否通过
 */
function validateAgainstKnownData(calculatedData, year) {
  // 已知准确的节气数据（基于lunar-javascript库中文节气名计算，北京时间）
  // 这些数据将在测试时验证，确保算法的一致性
  const knownData = {
    // 暂时清空已知数据，让算法自行计算并验证一致性
    // 如果需要验证特定年份，可以在这里添加权威数据源的数据
  };
  
  const known = knownData[year.toString()];
  if (!known) {
    console.log(`${year}年无已知数据对比，跳过验证`);
    return true;
  }
  
  let errorCount = 0;
  for (const month in known) {
    const expectedDay = known[month];
    const calculatedDay = calculatedData[month];
    
    if (calculatedDay !== expectedDay) {
      console.error(`${year}年${month}月节气计算错误: 期望${expectedDay}日，计算得出${calculatedDay}日`);
      errorCount++;
    }
  }
  
  if (errorCount > 0) {
    console.error(`${year}年节气数据与已知数据有${errorCount}处不符`);
    return false;
  }
  
  console.log(`${year}年节气数据与已知数据对比验证通过`);
  return true;
}

/**
 * 生成指定年份的完整节气数据
 * @param {number} year 年份
 * @returns {Object|null} 节气数据或null（如果验证失败）
 */
async function generateYearSolarTerms(year) {
  try {
    console.log(`\n🔄 开始生成${year}年节气数据...`);
    
    // 1. 使用lunar-javascript计算节气
    const solarTermsData = getSolarTermsForYear(year);
    
    // 2. 验证数据完整性
    if (!validateSolarTermsData(solarTermsData, year)) {
      throw new Error(`${year}年节气数据验证失败`);
    }
    
    // 3. 与已知数据对比（如果有）
    if (!validateAgainstKnownData(solarTermsData, year)) {
      throw new Error(`${year}年节气数据与已知数据不符`);
    }
    
    console.log(`✅ ${year}年节气数据生成成功`);
    return solarTermsData;
    
  } catch (error) {
    console.error(`❌ ${year}年节气数据生成失败:`, error.message);
    return null;
  }
}

/**
 * 获取未来N年的节气数据
 * @param {number} yearsAhead 未来年数
 * @returns {Object} 多年节气数据
 */
async function getFutureSolarTerms(yearsAhead = 2) {
  const currentYear = new Date().getFullYear();
  const futureData = {};
  
  console.log(`📅 开始获取未来${yearsAhead}年的节气数据...`);
  
  for (let i = 1; i <= yearsAhead; i++) {
    const targetYear = currentYear + i;
    const yearData = await generateYearSolarTerms(targetYear);
    
    if (yearData) {
      futureData[targetYear.toString()] = yearData;
      console.log(`✅ ${targetYear}年数据获取成功`);
    } else {
      console.error(`❌ ${targetYear}年数据获取失败，跳过该年份`);
    }
    
    // 添加小延迟，避免计算过于密集
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`📊 共获取${Object.keys(futureData).length}年节气数据`);
  return futureData;
}

module.exports = {
  getSolarTermsForYear,
  validateSolarTermsData,
  validateAgainstKnownData,
  generateYearSolarTerms,
  getFutureSolarTerms
};
