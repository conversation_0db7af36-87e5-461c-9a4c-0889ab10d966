import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

// 内存中存储 accessToken，不持久化到 localStorage
let inMemoryToken: string | null = null;

// 是否正在刷新令牌的标志
let isRefreshing = false;

// 等待令牌刷新的请求队列
let refreshSubscribers: Array<(token: string) => void> = [];

// Token存储接口
interface TokenStorage {
  token: string;
  timestamp: number;
  expiresAt?: number; // JWT过期时间
}

// Token最大存储时间（30分钟，与accessToken有效期一致）
const TOKEN_MAX_AGE = 30 * 60 * 1000;

/**
 * 检查JWT token的有效性
 */
export const isTokenValid = (token: string): boolean => {
  try {
    // 简单的JWT格式检查
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // 检查payload
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);

    // 检查是否过期（提前5分钟判断为过期）
    if (payload.exp && payload.exp < (now + 5 * 60)) {
      console.log('Token即将过期或已过期');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Token格式无效:', error);
    return false;
  }
};

/**
 * 清除 localStorage 中的 token
 */
const clearStorageToken = () => {
  try {
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      const parsedStorage = JSON.parse(userStorage);
      if (parsedStorage.state) {
        delete parsedStorage.state.tokenData;
        delete parsedStorage.state.token;
        localStorage.setItem('user-storage', JSON.stringify(parsedStorage));
      }
    }
  } catch (error) {
    console.error('清除 localStorage token 失败:', error);
  }
};

/**
 * 设置访问令牌到内存
 */
export const setToken = (token: string | null) => {
  inMemoryToken = token;
};

/**
 * 获取当前内存中的访问令牌
 */
export const getToken = (): string | null => {
  return inMemoryToken;
};

/**
 * 从 localStorage 加载初始令牌到内存（增强版，包含有效性检查）
 */
export const loadTokenFromStorage = (): boolean => {
  try {
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      const parsedStorage = JSON.parse(userStorage);
      const tokenData = parsedStorage.state?.tokenData;
      const fallbackToken = parsedStorage.state?.token;

      if (tokenData) {
        // 检查token是否过期
        const now = Date.now();
        if (tokenData.expiresAt && now > tokenData.expiresAt) {
          console.log('localStorage中的token已过期，清除');
          clearStorageToken();
          return false;
        }

        // 检查JWT token有效性
        if (!isTokenValid(tokenData.token)) {
          console.log('localStorage中的token无效，清除');
          clearStorageToken();
          return false;
        }

        inMemoryToken = tokenData.token;
        console.log('从localStorage恢复了有效token');
        return true;
      } else if (fallbackToken) {
        // 向后兼容：处理旧格式的token
        console.log('检测到旧格式token，检查有效性并升级存储格式');

        if (!isTokenValid(fallbackToken)) {
          console.log('旧格式token无效，清除');
          clearStorageToken();
          return false;
        }

        inMemoryToken = fallbackToken;
        updateStorageToken(fallbackToken); // 升级到新格式
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('从 localStorage 解析 token 失败:', error);
    return false;
  }
};

/**
 * 更新 localStorage 中的令牌（增强版，包含时间戳和过期时间）
 */
export const updateStorageToken = (newToken: string) => {
  try {
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      const parsedStorage = JSON.parse(userStorage);

      if (parsedStorage.state) {
        // 添加时间戳和过期时间
        const now = Date.now();
        const tokenData: TokenStorage = {
          token: newToken,
          timestamp: now,
          expiresAt: now + TOKEN_MAX_AGE
        };

        parsedStorage.state.tokenData = tokenData;
        // 保持向后兼容
        parsedStorage.state.token = newToken;

        localStorage.setItem('user-storage', JSON.stringify(parsedStorage));
        console.log('Token已更新到localStorage，过期时间:', new Date(tokenData.expiresAt!).toISOString());
      }
    }
  } catch (error) {
    console.error('更新 localStorage 中的 token 失败:', error);
  }
};

/**
 * 添加请求到刷新订阅队列
 */
export const subscribeTokenRefresh = (callback: (token: string) => void) => {
  refreshSubscribers.push(callback);
};

/**
 * 通知所有等待的请求刷新完成
 */
export const onTokenRefreshed = (newToken: string) => {
  refreshSubscribers.forEach(callback => callback(newToken));
  refreshSubscribers = [];
};

/**
 * 创建一个自定义事件，用于通知 Session 过期
 */
export const notifySessionExpired = () => {
  const event = new CustomEvent('unauthorized', {
    detail: { reason: 'session_expired' }
  });
  window.dispatchEvent(event);
};

/**
 * 刷新访问令牌
 */
export const refreshToken = async (): Promise<string | null> => {
  try {
    if (isRefreshing) {
      // 返回一个Promise，当令牌刷新后会被解析
      return new Promise<string>((resolve) => {
        subscribeTokenRefresh(token => {
          resolve(token);
        });
      });
    }
    
    isRefreshing = true;
    
    // 请求新的访问令牌
    const response = await axios.post(
      `${API_URL}/auth/refresh`,
      {},
      { withCredentials: true } // 确保发送和接收cookie
    );
    
    if (response.data.success && response.data.accessToken) {
      const newToken = response.data.accessToken;

      // 验证新token的有效性
      if (isTokenValid(newToken)) {
        setToken(newToken);
        updateStorageToken(newToken);
        onTokenRefreshed(newToken);

        isRefreshing = false;
        return newToken;
      } else {
        console.error('服务器返回的新token无效');
        setToken(null);
        isRefreshing = false;
        notifySessionExpired();
        return null;
      }
    } else {
      // 刷新失败，清除令牌
      setToken(null);
      isRefreshing = false;
      // 通知应用 session 已过期
      notifySessionExpired();
      return null;
    }
  } catch (error) {
    console.error('刷新令牌失败:', error);
    setToken(null);
    isRefreshing = false;
    // 通知应用 session 已过期
    notifySessionExpired();
    return null;
  }
};

/**
 * 启动定期token清理机制
 */
export const startTokenCleanup = () => {
  // 每5分钟检查一次localStorage中的token
  setInterval(() => {
    try {
      const userStorage = localStorage.getItem('user-storage');
      if (userStorage) {
        const parsedStorage = JSON.parse(userStorage);
        const tokenData = parsedStorage.state?.tokenData;

        if (tokenData && tokenData.expiresAt) {
          const now = Date.now();
          if (now > tokenData.expiresAt) {
            console.log('定期清理：发现过期token，清除');
            clearStorageToken();
          }
        }
      }
    } catch (error) {
      console.error('定期token清理失败:', error);
    }
  }, 5 * 60 * 1000); // 5分钟

  console.log('Token定期清理机制已启动');
};

export default {
  setToken,
  getToken,
  loadTokenFromStorage,
  refreshToken,
  updateStorageToken,
  notifySessionExpired,
  startTokenCleanup,
  isTokenValid
};