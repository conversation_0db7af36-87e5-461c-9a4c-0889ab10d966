import { useState, useEffect, useCallback } from 'react';
import { getPredictionSettings, PredictionSettings } from '../api/predictionSettings';

/**
 * 预测图设置管理Hook
 * 负责获取和管理预测图的显示设置
 */
export const usePredictionSettings = () => {
  const [settings, setSettings] = useState<PredictionSettings>({
    enableKlinePrediction: true,
    enableLinePrediction: true
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载预测图设置
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const predictionSettings = await getPredictionSettings();
      setSettings(predictionSettings);
      
      console.log('[预测设置] 预测图设置加载完成:', predictionSettings);
    } catch (err) {
      console.error('[预测设置] 加载预测图设置失败:', err);
      setError('加载预测图设置失败');
      
      // 使用默认设置
      setSettings({
        enableKlinePrediction: true,
        enableLinePrediction: true
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 组件挂载时加载设置
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // 刷新设置
  const refreshSettings = useCallback(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    error,
    refreshSettings
  };
};
