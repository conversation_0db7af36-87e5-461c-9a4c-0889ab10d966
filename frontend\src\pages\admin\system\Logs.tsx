import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../../components/admin/AdminLayout';
import { useToast } from '../../../components/ui/use-toast';
import { <PERSON><PERSON>, Card } from '../../../components/admin/ui';
import { adminApiInstance } from '../../../api/admin';
import { 
  Loader2, 
  Download, 
  Trash2, 
  Settings, 
  FileText, 
  HardDrive,
  Calendar,
  Clock
} from 'lucide-react';

interface LogFile {
  name: string;
  size: number;
  date: string;
  mtime: Date;
}

interface LogStats {
  totalFiles: number;
  totalSize: number;
  oldestFile: string | null;
  newestFile: string | null;
}

interface LogSettings {
  retentionDays: number;
  enableAutoCleanup: boolean;
  maxFileSize: string;
}

const SystemLogs: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [logFiles, setLogFiles] = useState<LogFile[]>([]);
  const [stats, setStats] = useState<LogStats>({
    totalFiles: 0,
    totalSize: 0,
    oldestFile: null,
    newestFile: null
  });
  const [settings, setSettings] = useState<LogSettings>({
    retentionDays: 15,
    enableAutoCleanup: true,
    maxFileSize: '5MB'
  });
  const [loading, setLoading] = useState(true);
  const [cleanupLoading, setCleanupLoading] = useState(false);

  useEffect(() => {
    loadLogData();
  }, []);

  const loadLogData = async () => {
    try {
      setLoading(true);
      const response = await adminApiInstance.get('/admin/logs/files');
      
      if (response.data.success) {
        setLogFiles(response.data.files);
        setStats(response.data.stats);
        setSettings(response.data.settings);
      }
    } catch (error: any) {
      console.error('加载日志数据失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载日志数据',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = async (filename: string) => {
    try {
      const response = await adminApiInstance.get(`/admin/logs/download/${filename}`, {
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast({
        title: '下载成功',
        description: `日志文件 ${filename} 已下载`,
        variant: 'default'
      });
    } catch (error: any) {
      console.error('下载文件失败:', error);
      toast({
        title: '下载失败',
        description: error.response?.data?.message || '下载文件失败',
        variant: 'destructive'
      });
    }
  };

  const manualCleanup = async (days: number) => {
    try {
      setCleanupLoading(true);
      
      const response = await adminApiInstance.post('/admin/logs/cleanup', { days });
      
      if (response.data.success) {
        toast({
          title: '清理成功',
          description: response.data.message,
          variant: 'default'
        });
        
        // 重新加载数据
        await loadLogData();
      }
    } catch (error: any) {
      console.error('清理日志失败:', error);
      toast({
        title: '清理失败',
        description: error.response?.data?.message || '清理日志失败',
        variant: 'destructive'
      });
    } finally {
      setCleanupLoading(false);
    }
  };

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">系统日志</h1>
            <p className="text-gray-600 mt-2">查看和管理系统运行日志</p>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate('/admin/settings/logs')}
            className="flex items-center space-x-2"
          >
            <Settings className="h-4 w-4" />
            <span>日志设置</span>
          </Button>
        </div>

        {/* 日志配置概览 */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">当前配置</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{settings.retentionDays}</div>
              <div className="text-sm text-gray-700">保留天数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {settings.enableAutoCleanup ? '已启用' : '已禁用'}
              </div>
              <div className="text-sm text-gray-700">自动清理</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{settings.maxFileSize}</div>
              <div className="text-sm text-gray-700">文件大小限制</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">03:10</div>
              <div className="text-sm text-gray-700">清理时间</div>
            </div>
          </div>
        </Card>

        {/* 日志统计 */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">日志概览</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-blue-500" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.totalFiles}</div>
                <div className="text-sm text-gray-700">文件总数</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <HardDrive className="h-8 w-8 text-green-500" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{formatSize(stats.totalSize)}</div>
                <div className="text-sm text-gray-700">占用空间</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-8 w-8 text-purple-500" />
              <div>
                <div className="text-lg font-bold text-gray-900">
                  {stats.oldestFile ? stats.oldestFile.replace('app-', '').replace('.log', '') : '-'}
                </div>
                <div className="text-sm text-gray-700">最早日志</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="h-8 w-8 text-orange-500" />
              <div>
                <div className="text-lg font-bold text-gray-900">
                  {stats.newestFile ? stats.newestFile.replace('app-', '').replace('.log', '') : '-'}
                </div>
                <div className="text-sm text-gray-700">最新日志</div>
              </div>
            </div>
          </div>
        </Card>

        {/* 管理操作 */}
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">管理操作</h3>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => manualCleanup(7)}
                disabled={cleanupLoading}
                className="flex items-center space-x-2"
              >
                {cleanupLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                <span>清理7天前</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => manualCleanup(settings.retentionDays)}
                disabled={cleanupLoading}
                className="flex items-center space-x-2"
              >
                {cleanupLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                <span>立即清理过期日志</span>
              </Button>
            </div>
          </div>
        </Card>

        {/* 日志文件列表 */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">日志文件</h3>
          {logFiles.length === 0 ? (
            <div className="text-center py-8 text-gray-700">
              暂无日志文件
            </div>
          ) : (
            <div className="space-y-2">
              {logFiles.map((file) => (
                <div
                  key={file.name}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <FileText className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900">{file.name}</div>
                      <div className="text-sm text-gray-700">
                        {formatSize(file.size)} • {formatDate(file.date)}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadFile(file.name)}
                    className="flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>下载</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>
    </AdminLayout>
  );
};

export default SystemLogs;
