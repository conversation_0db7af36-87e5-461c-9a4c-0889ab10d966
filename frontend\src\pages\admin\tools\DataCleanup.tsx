import React, { useState } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import { Button, Input, Card } from '../../../components/admin/ui';
import { useToast } from '../../../components/ui/use-toast';
import { Trash2, Database, AlertTriangle } from 'lucide-react';
import { adminApiInstance } from '../../../api/admin';

interface CleanupOptions {
  dataType: 'solar-term-lines' | 'prediction-klines' | 'real-time-klines' | 'solar-term-configs';
  action: 'all' | 'before' | 'after' | 'range';
  symbol?: string;
  beforeDate?: string;
  afterDate?: string;
  startDate?: string;
  endDate?: string;
}

const DataCleanup: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [options, setOptions] = useState<CleanupOptions>({
    dataType: 'solar-term-lines',
    action: 'all'
  });

  const dataTypeOptions = [
    { value: 'solar-term-lines', label: '节气预测折线数据', description: '清理节气预测折线相关数据' },
    { value: 'prediction-klines', label: '预测K线数据', description: '清理预测K线相关数据' },
    { value: 'real-time-klines', label: '实时K线数据', description: '清理实时K线相关数据' },
    { value: 'solar-term-configs', label: '节气预测转换比例配置', description: '清理节气预测转换比例配置数据' }
  ];

  const actionOptions = [
    { value: 'all', label: '清理所有数据', description: '删除所有相关数据' },
    { value: 'before', label: '清理指定日期之前', description: '删除指定日期之前的数据' },
    { value: 'after', label: '清理指定日期之后', description: '删除指定日期之后的数据' },
    { value: 'range', label: '清理指定时间段', description: '删除指定时间段内的数据' }
  ];

  const handleCleanup = async () => {
    try {
      setIsLoading(true);

      // 构建请求参数
      const params: any = {
        dataType: options.dataType,
        action: options.action
      };

      if (options.symbol) {
        params.symbol = options.symbol;
      }

      if (options.action === 'before' && options.beforeDate) {
        params.beforeDate = options.beforeDate;
      }

      if (options.action === 'after' && options.afterDate) {
        params.afterDate = options.afterDate;
      }

      if (options.action === 'range' && options.startDate && options.endDate) {
        params.startDate = options.startDate;
        params.endDate = options.endDate;
      }

      const response = await adminApiInstance.post('/admin/tools/data-cleanup', params);

      toast({
        title: '数据清理成功',
        description: `已删除 ${response.data.deletedCount} 条记录`,
        variant: 'default'
      });

      setIsLoading(false);
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '数据清理失败';
      toast({
        title: '数据清理失败',
        description: message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof CleanupOptions, value: string) => {
    setOptions(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const isFormValid = () => {
    if (options.action === 'before' && !options.beforeDate) return false;
    if (options.action === 'after' && !options.afterDate) return false;
    if (options.action === 'range' && (!options.startDate || !options.endDate)) return false;
    return true;
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">数据清理工具</h1>
          <p className="text-gray-600 mt-1">清理系统中的历史数据</p>
        </div>

        <Card>
          <div className="space-y-6">
            {/* 警告提示 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">注意事项</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    数据清理操作不可逆，请谨慎操作。建议在执行前备份重要数据。
                  </p>
                </div>
              </div>
            </div>

            {/* 数据类型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Database className="inline h-4 w-4 mr-2" />
                数据类型
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {dataTypeOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      options.dataType === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('dataType', option.value)}
                  >
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500 mt-1">{option.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 清理方式选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Trash2 className="inline h-4 w-4 mr-2" />
                清理方式
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {actionOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      options.action === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('action', option.value)}
                  >
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500 mt-1">{option.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 交易对选择 */}
            <div>
              <Input
                label="交易对 (可选)"
                placeholder="例如: BTCUSDT"
                value={options.symbol || ''}
                onChange={(e) => handleInputChange('symbol', e.target.value)}
                helperText="留空则清理所有交易对的数据"
              />
            </div>

            {/* 日期选择 */}
            {options.action === 'before' && (
              <div>
                <Input
                  label="截止日期"
                  type="date"
                  value={options.beforeDate || ''}
                  onChange={(e) => handleInputChange('beforeDate', e.target.value)}
                  helperText="将删除此日期之前的所有数据"
                />
              </div>
            )}

            {options.action === 'after' && (
              <div>
                <Input
                  label="起始日期"
                  type="date"
                  value={options.afterDate || ''}
                  onChange={(e) => handleInputChange('afterDate', e.target.value)}
                  helperText="将删除此日期之后的所有数据"
                />
              </div>
            )}

            {options.action === 'range' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="开始日期"
                  type="date"
                  value={options.startDate || ''}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
                <Input
                  label="结束日期"
                  type="date"
                  value={options.endDate || ''}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                />
              </div>
            )}

            {/* 执行按钮 */}
            <div className="flex justify-end pt-4 border-t border-gray-200">
              <Button
                onClick={handleCleanup}
                disabled={!isFormValid() || isLoading}
                loading={isLoading}
                variant="danger"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                {isLoading ? '清理中...' : '执行清理'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default DataCleanup;
