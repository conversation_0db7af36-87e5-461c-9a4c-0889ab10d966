import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './ui/button';

import { Trash2, Check, CheckCheck } from 'lucide-react';
import notificationService, { Notification } from '../services/notificationService';
import { useToast } from './ui/use-toast';

interface NotificationListProps {
  className?: string;
}

const NotificationList: React.FC<NotificationListProps> = ({ className = '' }) => {
  const { t, i18n } = useTranslation();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  // 完全移除loading状态，不显示任何加载动画
  // const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const { toast } = useToast();

  // 翻译通知内容
  const translateNotification = (notification: Notification) => {
    const { type, title, content } = notification;

    // 尝试从翻译文件获取标题
    const translatedTitle = t(`notifications.titles.${type}`, { defaultValue: title });

    // 尝试从翻译文件获取内容，并处理参数替换
    let translatedContent = content;

    // 解析内容中的参数
    const extractParams = (text: string) => {
      const params: any = {};

      // 提取天数
      const daysMatch = text.match(/(\d+)\s*天/);
      if (daysMatch) params.days = daysMatch[1];

      // 提取计划类型
      if (text.includes('月度')) params.plan = t('notifications.planNames.monthly');
      else if (text.includes('季度')) params.plan = t('notifications.planNames.quarterly');
      else if (text.includes('年度')) params.plan = t('notifications.planNames.yearly');

      // 提取日期
      const dateMatch = text.match(/(\d{4}-\d{2}-\d{2})/);
      if (dateMatch) params.endDate = dateMatch[1];

      // 提取邀请数量
      const countMatch = text.match(/(\d+)\s*位/);
      if (countMatch) params.count = countMatch[1];

      return params;
    };

    const params = extractParams(content);
    translatedContent = t(`notifications.contents.${type}`, { ...params, defaultValue: content }) as string;

    return {
      title: translatedTitle as string,
      content: translatedContent
    };
  };

  // 加载通知列表
  const loadNotifications = useCallback(async (pageNum: number = 1, unreadOnly: boolean = false) => {
    try {
      // 完全移除loading状态，不显示任何加载动画
      // setLoading(true);
      const response = await notificationService.getNotifications(pageNum, 10, unreadOnly);

      if (pageNum === 1) {
        setNotifications(response.data);
      } else {
        setNotifications(prev => [...prev, ...response.data]);
      }

      setPage(pageNum);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      console.error('加载通知失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401) {
        toast({
          title: t('errors.sessionExpired'),
          variant: 'destructive'
        });
        // 不在这里重定向，让拦截器处理
      } else if (error.response?.status === 404) {
        toast({
          title: t('errors.serviceUnavailable'),
          variant: 'destructive'
        });
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        toast({
          title: t('errors.networkError'),
          variant: 'destructive'
        });
      } else {
        toast({
          title: t('errors.dataLoadFailed'),
          description: error.response?.data?.message || error.message,
          variant: 'destructive'
        });
      }
    } finally {
      // setLoading(false);
    }
  }, [toast, t]);

  // 加载未读通知数量
  const loadUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error: any) {
      console.error('加载未读通知数量失败:', error);
      // 未读数量加载失败不显示错误提示，避免过多提示
      if (error.response?.status !== 401) {
        // 只有非认证错误才设置为0，认证错误让拦截器处理
        setUnreadCount(0);
      }
    }
  };

  // 标记通知为已读
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => 
          notification._id === notificationId 
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      );
      
      // 更新未读数量
      setUnreadCount(prev => Math.max(0, prev - 1));
      toast({
        title: t('notifications.markedAsRead'),
        variant: 'default'
      });
    } catch (error) {
      console.error('标记通知已读失败:', error);
      toast({
        title: t('errors.operationFailed'),
        variant: 'destructive'
      });
    }
  };

  // 标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => ({ 
          ...notification, 
          isRead: true, 
          readAt: new Date().toISOString() 
        }))
      );
      
      setUnreadCount(0);
      toast({
        title: t('notifications.allMarkedAsRead'),
        variant: 'default'
      });
    } catch (error) {
      console.error('标记所有通知已读失败:', error);
      toast({
        title: t('errors.operationFailed'),
        variant: 'destructive'
      });
    }
  };

  // 删除通知
  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await notificationService.deleteNotification(notificationId);
      
      // 从本地状态中移除
      const deletedNotification = notifications.find(n => n._id === notificationId);
      setNotifications(prev => prev.filter(n => n._id !== notificationId));
      
      // 如果删除的是未读通知，更新未读数量
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      toast({
        title: t('notifications.deleted'),
        variant: 'default'
      });
    } catch (error) {
      console.error('删除通知失败:', error);
      toast({
        title: t('errors.deleteFailed'),
        variant: 'destructive'
      });
    }
  };

  // 加载更多通知
  const handleLoadMore = () => {
    if (page < totalPages) {
      loadNotifications(page + 1, showUnreadOnly);
    }
  };

  // 切换显示模式
  const handleToggleUnreadOnly = (unreadOnly: boolean) => {
    setShowUnreadOnly(unreadOnly);
    setPage(1);
    loadNotifications(1, unreadOnly);
  };

  // 初始化加载
  useEffect(() => {
    loadNotifications();
    loadUnreadCount();
  }, [loadNotifications]);

  // 完全隐藏加载状态，不显示任何加载动画
  // if (loading && notifications.length === 0) {
  //   return (
  //     <div className={`space-y-4 ${className}`}>
  //       <div className="animate-pulse space-y-3">
  //         {[1, 2, 3].map(i => (
  //           <div key={i} className="h-20 bg-muted rounded-lg"></div>
  //         ))}
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleUnreadOnly(false)}
            className={!showUnreadOnly
              ? "bg-cyber-cyan/15 text-cyber-cyan border border-cyber-cyan/30 font-mono text-xs"
              : "text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
            }
          >
            {t('notifications.allNotifications')}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleUnreadOnly(true)}
            className={showUnreadOnly
              ? "bg-cyber-cyan/15 text-cyber-cyan border border-cyber-cyan/30 font-mono text-xs"
              : "text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
            }
          >
            {t('notifications.unreadNotifications')} {unreadCount > 0 && <span className="ml-1 bg-cyber-pink text-white text-xs rounded-full min-w-[16px] h-[16px] flex items-center justify-center px-1 font-mono">{unreadCount}</span>}
          </Button>
        </div>

        {unreadCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMarkAllAsRead}
            className="flex items-center space-x-1 text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
          >
            <CheckCheck className="h-4 w-4" />
            <span>{t('notifications.markAllAsRead')}</span>
          </Button>
        )}
      </div>

      {/* 通知列表 */}
      {notifications.length === 0 ? (
        <div className="p-4 rounded-xl text-center border border-cyber-cyan/20 bg-cyber-dark/30">
          <p className="text-cyber-muted text-sm font-mono">
            {showUnreadOnly ? t('notifications.noUnreadNotifications') : t('notifications.noNotifications')}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification._id}
              className={`p-4 rounded-xl border transition-colors backdrop-blur-sm ${
                notification.isRead
                  ? 'bg-cyber-dark/30 border-cyber-cyan/20'
                  : 'bg-cyber-cyan/5 border-cyber-cyan/30'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">
                      {notificationService.getTypeIcon(notification.type)}
                    </span>
                    <h4 className="font-medium text-cyber-text truncate font-mono">
                      {translateNotification(notification).title}
                    </h4>
                    {!notification.isRead && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyber-pink/20 text-cyber-pink border border-cyber-pink/30 font-mono">{t('notifications.unread')}</span>
                    )}
                  </div>

                  <p className="text-sm text-cyber-muted mb-2 leading-relaxed font-mono">
                    {translateNotification(notification).content}
                  </p>

                  <p className="text-xs text-cyber-muted font-mono">
                    {notificationService.formatTime(notification.createdAt, i18n.language)}
                  </p>
                </div>

                <div className="flex items-center space-x-1 ml-4">
                  {!notification.isRead && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMarkAsRead(notification._id)}
                      className="h-8 w-8 p-0 text-cyber-muted hover:text-cyber-green bg-transparent hover:bg-cyber-green/10 border border-cyber-green/20 hover:border-cyber-green/40 transition-all duration-200"
                      title={t('notifications.markAsRead')}
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteNotification(notification._id)}
                    className="h-8 w-8 p-0 text-cyber-muted hover:text-cyber-pink bg-transparent hover:bg-cyber-pink/10 border border-cyber-pink/20 hover:border-cyber-pink/40 transition-all duration-200"
                    title={t('notifications.deleteNotification')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 加载更多按钮 */}
      {page < totalPages && (
        <div className="text-center mt-6">
          <Button
            variant="ghost"
            onClick={handleLoadMore}
            className="text-cyber-muted hover:text-cyber-cyan bg-transparent hover:bg-cyber-cyan/10 border border-cyber-cyan/20 hover:border-cyber-cyan/40 font-mono text-xs transition-all duration-200"
          >
            {t('common.loadMore')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotificationList;
