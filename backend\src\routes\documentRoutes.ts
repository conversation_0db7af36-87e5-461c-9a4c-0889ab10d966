import express from 'express';
import documentController from '../controllers/documentController';
import { optionalAuthenticate } from '../middlewares/authMiddleware';

const router = express.Router();

// 公开路由 - 使用可选认证中间件
router.use(optionalAuthenticate);

// 获取文档列表
// GET /api/docs
router.get('/', documentController.getDocuments);

// 获取文档详情
// GET /api/docs/:id
router.get('/:id', documentController.getDocumentById);

export default router;
