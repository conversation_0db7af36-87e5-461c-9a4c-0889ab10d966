import { format, formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';

/**
 * 格式化日期时间 - 完整格式
 * @param date 日期字符串或Date对象
 * @param language 语言代码 ('zh' | 'en')
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date, language: string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = language === 'zh' ? zhCN : enUS;
  
  if (language === 'zh') {
    return format(dateObj, 'yyyy年MM月dd日 HH:mm', { locale });
  } else {
    return format(dateObj, 'MMM dd, yyyy HH:mm', { locale });
  }
};

/**
 * 格式化日期 - 短格式
 * @param date 日期字符串或Date对象
 * @param language 语言代码 ('zh' | 'en')
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, language: string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = language === 'zh' ? zhCN : enUS;
  
  if (language === 'zh') {
    return format(dateObj, 'yyyy年MM月dd日', { locale });
  } else {
    return format(dateObj, 'MMM dd, yyyy', { locale });
  }
};

/**
 * 格式化相对时间
 * @param date 日期字符串或Date对象
 * @param language 语言代码 ('zh' | 'en')
 * @returns 相对时间字符串，如 "3天前" / "3 days ago"
 */
export const formatRelativeTime = (date: string | Date, language: string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = language === 'zh' ? zhCN : enUS;
  
  return formatDistanceToNow(dateObj, { 
    addSuffix: true, 
    locale 
  });
};

/**
 * 格式化时间 - 仅时分
 * @param date 日期字符串或Date对象
 * @param language 语言代码 ('zh' | 'en')
 * @returns 格式化后的时间字符串
 */
export const formatTime = (date: string | Date, language: string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return format(dateObj, 'HH:mm');
};
